import React, { useState } from "react";

const ComputerVisionActivity = () => {
  const [matches, setMatches] = useState({});
  const [feedback, setFeedback] = useState("");

  const [isHappy, setIsHappy] = useState(false); // Track computer's mood

  const images = [
    { id: "cat", label: "Cat", src: "/lvl1_img/cat.jpg" },
    { id: "car", label: "Car", src: "/lvl1_img/car.jpeg" },
    { id: "apple", label: "Apple", src: "/lvl1_img/apple.jpeg" },
  ];

  const correctMatches = {
    cat: "Cat",
    car: "Car",
    apple: "Apple",
  };

  const handleDrop = (imageId, label) => {
    setMatches((prev) => ({
      ...prev,
      [imageId]: label,
    }));
  };

  const checkAnswers = () => {
    const allCorrect = Object.keys(correctMatches).every(
      (key) => matches[key] === correctMatches[key]
    );

    setIsHappy(allCorrect); // Change mood to happy if all answers are correct

    setFeedback(
      allCorrect
        ? "🎉 Great job! You've trained the computer successfully!"
        : "❌ Some matches are incorrect. Try again!"
    );
  };

  const resetActivity = () => {
    setMatches({});
    setFeedback("");
    setIsHappy(false); // Reset to confused mood
  };

  return (
    <div className="text-gray-700 bg-white mt-6 p-4 rounded-lg mb-7">
      <h1 className="text-4xl font-bold mb-6 text-purple-700 ">What is Computer Vision?</h1>
      <p className="text-lg mb-4">
        <strong>Computer Vision</strong> is like teaching computers how to "see" the world, just like we do with our eyes! Computers use
        cameras and special programs to look at pictures and understand what they are. It's like magic, but real!
      </p>
      <div className="bg-blue-100 p-4 border-l-5 border-blue-500 mb-4">
        <p>
          <strong>Fun Fact:</strong> Did you know your phone's camera uses computer vision to recognize your face and unlock? It’s just like
          how you recognize your friends and family!
        </p>
      </div>

      {/* Computer Image */}
      <div className="flex justify-center mb-6">
        <img
          src={
            isHappy
              ? "/lvl1_img/happy (1).jpeg"
              : "/lvl1_img/happy (2).jpeg"
          }
          alt={isHappy ? "Happy Computer" : "Confused Computer"}
          className="w-40 h-40"
        />
      </div>

      <h2 className="text-2xl font-bold mb-4">Activity: Train the Computer</h2>
      <p className="text-lg mb-4">
        Computers learn to recognize objects by being trained with examples. Drag the correct label to each image to help the computer
        understand what it is seeing. Let's teach the computer together!
      </p>

      {/* Activity Section */}
      <div className="flex flex-col md:flex-row items-start justify-center gap-6">
        {/* Images */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Images</h2>
          <div className="space-y-4">
            {images.map((image) => (
              <div
                key={image.id}
                onDragOver={(e) => e.preventDefault()}
                onDrop={(e) => {
                  const label = e.dataTransfer.getData("label");
                  handleDrop(image.id, label);
                }}
                className="w-32 h-32 bg-gray-100 border-2 border-dashed border-gray-400 rounded-md flex items-center justify-center relative"
              >
                <img src={image.src} alt={image.label} className="w-full h-full object-cover" />
                {matches[image.id] && (
                  <div className="absolute bottom-0 text-sm font-bold bg-green-200 p-1 rounded-md">
                    {matches[image.id]}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Labels */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Labels</h2>
          <div className="space-y-4">
            {Object.values(correctMatches).map((label) => (
              <div
                key={label}
                draggable
                onDragStart={(e) => e.dataTransfer.setData("label", label)}
                className="w-32 p-2 bg-white border border-gray-300 rounded-md shadow-sm text-center cursor-pointer hover:shadow-lg"
              >
                {label}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Buttons */}
      <div className="mt-6 space-x-4">
        <button
          onClick={checkAnswers}
          className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
        >
          Check Answers
        </button>
        <button
          onClick={resetActivity}
          className="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600"
        >
          Reset
        </button>
      </div>

      {/* Feedback */}
      {feedback && (
        <div
          className={`mt-6 p-4 rounded-lg shadow-md ${
            feedback.includes("Great")
              ? "bg-green-100 text-green-700"
              : "bg-red-100 text-red-700"
          }`}
        >
          <p className="text-lg font-bold">{feedback}</p>
        </div>
      )}

      {/* Learning Section */}
      <div className="mt-8 bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
        <h2 className="text-xl font-bold mb-2">What Did We Learn?</h2>
        <p className="text-lg">
          Computer vision works by learning from examples, just like we taught the computer in this activity. By labeling images, we help
          computers understand objects and make smart decisions. Imagine all the amazing things this technology can do!
        </p>
      </div>
    </div>
  );
};

export default ComputerVisionActivity;

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCakeRoll = createReactComponent("outline", "cake-roll", "IconCakeRoll", [["path", { "d": "M12 15c-4.97 0 -9 -2.462 -9 -5.5s4.03 -5.5 9 -5.5s9 2.462 9 5.5s-4.03 5.5 -9 5.5", "key": "svg-0" }], ["path", { "d": "M12 6.97c3 0 4 1.036 4 1.979c0 2.805 -8 2.969 -8 -.99c0 -2.11 1.5 -3.959 4 -3.959", "key": "svg-1" }], ["path", { "d": "M21 9.333v5.334c0 2.945 -4.03 5.333 -9 5.333s-9 -2.388 -9 -5.333v-5.334", "key": "svg-2" }]]);

export { IconCakeRoll as default };
//# sourceMappingURL=IconCakeRoll.mjs.map

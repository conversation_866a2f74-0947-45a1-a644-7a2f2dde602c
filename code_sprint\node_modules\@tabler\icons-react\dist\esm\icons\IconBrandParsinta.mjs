/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandParsinta = createReactComponent("outline", "brand-parsinta", "IconBrandParsinta", [["path", { "d": "M12 3a9 9 0 1 0 9 9", "key": "svg-0" }], ["path", { "d": "M21 12a9 9 0 0 0 -9 -9", "opacity": ".5", "key": "svg-1" }], ["path", { "d": "M10 9v6l5 -3z", "key": "svg-2" }]]);

export { IconBrandParsinta as default };
//# sourceMappingURL=IconBrandParsinta.mjs.map

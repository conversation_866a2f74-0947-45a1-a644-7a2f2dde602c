/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBuildingAirport = createReactComponent("outline", "building-airport", "IconBuildingAirport", [["path", { "d": "M3.59 7h8.82a1 1 0 0 1 .902 1.433l-1.44 3a1 1 0 0 1 -.901 .567h-5.942a1 1 0 0 1 -.901 -.567l-1.44 -3a1 1 0 0 1 .901 -1.433", "key": "svg-0" }], ["path", { "d": "M6 7l-.78 -2.342a.5 .5 0 0 1 .473 -.658h4.612a.5 .5 0 0 1 .475 .658l-.78 2.342", "key": "svg-1" }], ["path", { "d": "M8 2v2", "key": "svg-2" }], ["path", { "d": "M6 12v9h4v-9", "key": "svg-3" }], ["path", { "d": "M3 21h18", "key": "svg-4" }], ["path", { "d": "M22 5h-6l-1 -1", "key": "svg-5" }], ["path", { "d": "M18 3l2 2l-2 2", "key": "svg-6" }], ["path", { "d": "M10 17h7a2 2 0 0 1 2 2v2", "key": "svg-7" }]]);

export { IconBuildingAirport as default };
//# sourceMappingURL=IconBuildingAirport.mjs.map

import React from 'react';

export default function DataStorytelling() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-teal-400 via-indigo-500 to-purple-700 text-white p-10">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .card {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    margin: 20px 0;
                    max-width: 600px;
                    text-align: left;
                }

                .image-placeholder {
                    width: 200px;
                    height: 150px;
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px dashed #fff;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 1rem;
                    margin: 16px;
                    flex-shrink: 0; /* Ensures image placeholder doesn't shrink */
                }

                .content-row {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 20px; /* Adds space between cards and placeholders */
                    flex-wrap: wrap; /* Ensures responsiveness on smaller screens */
                }
                `}
            </style>

            <h1 className="text-5xl font-bold mb-8">Data Storytelling</h1>
            <p className="text-lg text-center max-w-3xl mb-12">
                Data Storytelling is about turning raw numbers into <span className="highlight">compelling stories</span> that engage, inform, and inspire your audience.
            </p>

            {/* Example 1: Using Charts for Impact */}
            <div className="content-row">
                <img
                    src="/charts_for_storytelling.jpg" /* Replace with actual image path */
                    alt="Charts for Storytelling"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📊 Using Charts for Impact</h2>
                    <p className="text-lg">
                        A chart is more than just data—it’s a visual way to make a point. For example:
                        <br />
                        - Show how sales improved after a marketing campaign.<br />
                        - Compare customer satisfaction across years.
                    </p>
                </div>
            </div>

            {/* Example 2: Narrating with Data */}
            <div className="content-row">
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📖 Narrating with Data</h2>
                    <p className="text-lg">
                        Good storytelling combines data with context. For instance:
                        <br />
                        - Explain how temperature trends affect farming.<br />
                        - Show how customer feedback helped improve product design.
                    </p>
                </div>
                <img
                    src="/narrating_with_data.jpg" /* Replace with actual image path */
                    alt="Narrating with Data"
                    className="image-placeholder"
                />
            </div>

            {/* Example 3: Engaging Your Audience */}
            <div className="content-row">
                <img
                    src="/engaging_audience.jpg" /* Replace with actual image path */
                    alt="Engaging Your Audience"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">💡 Engaging Your Audience</h2>
                    <p className="text-lg">
                        Make your audience the hero of your story. Use:
                        <br />
                        - Infographics to highlight key points.<br />
                        - Personalized data to make insights relatable.
                    </p>
                </div>
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-xl font-semibold mt-16 max-w-2xl">
                Data Storytelling turns facts into stories that move people to action. Ready to inspire with your data?
            </p>
        </div>
    );
}

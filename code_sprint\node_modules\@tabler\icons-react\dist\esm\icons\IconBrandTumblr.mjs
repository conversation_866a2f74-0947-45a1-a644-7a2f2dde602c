/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandTumblr = createReactComponent("outline", "brand-tumblr", "IconBrandTumblr", [["path", { "d": "M14 21h4v-4h-4v-6h4v-4h-4v-4h-4v1a3 3 0 0 1 -3 3h-1v4h4v6a4 4 0 0 0 4 4", "key": "svg-0" }]]);

export { IconBrandTumblr as default };
//# sourceMappingURL=IconBrandTumblr.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCactusOff = createReactComponent("outline", "cactus-off", "IconCactusOff", [["path", { "d": "M6 9v1a3 3 0 0 0 3 3h1", "key": "svg-0" }], ["path", { "d": "M18 8v5a3 3 0 0 1 -.129 .872m-2.014 2a3 3 0 0 1 -.857 .124h-1", "key": "svg-1" }], ["path", { "d": "M10 21v-11m0 -4v-1a2 2 0 1 1 4 0v5m0 4v7", "key": "svg-2" }], ["path", { "d": "M7 21h10", "key": "svg-3" }], ["path", { "d": "M3 3l18 18", "key": "svg-4" }]]);

export { IconCactusOff as default };
//# sourceMappingURL=IconCactusOff.mjs.map

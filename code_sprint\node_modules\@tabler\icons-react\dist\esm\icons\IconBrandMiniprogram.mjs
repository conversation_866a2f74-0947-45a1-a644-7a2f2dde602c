/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandMiniprogram = createReactComponent("outline", "brand-miniprogram", "IconBrandMiniprogram", [["path", { "d": "M21 12a9 9 0 1 1 -18 0a9 9 0 0 1 18 0z", "key": "svg-0" }], ["path", { "d": "M8 11.503a2.5 2.5 0 1 0 4 2v-3a2.5 2.5 0 1 1 4 2", "key": "svg-1" }]]);

export { IconBrandMiniprogram as default };
//# sourceMappingURL=IconBrandMiniprogram.mjs.map

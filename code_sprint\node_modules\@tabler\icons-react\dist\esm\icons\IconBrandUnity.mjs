/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandUnity = createReactComponent("outline", "brand-unity", "IconBrandUnity", [["path", { "d": "M14 3l6 4v7", "key": "svg-0" }], ["path", { "d": "M18 17l-6 4l-6 -4", "key": "svg-1" }], ["path", { "d": "M4 14v-7l6 -4", "key": "svg-2" }], ["path", { "d": "M4 7l8 5v9", "key": "svg-3" }], ["path", { "d": "M20 7l-8 5", "key": "svg-4" }]]);

export { IconBrandUnity as default };
//# sourceMappingURL=IconBrandUnity.mjs.map

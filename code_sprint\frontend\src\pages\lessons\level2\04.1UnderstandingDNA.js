import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "../level-4/section1/animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const Sportsgames = () => {
  const typedText = useTypingAnimation(
    "Genomic AI",
    100
  );

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>What is Genomic AI?</strong>
        </p>
        <img
          src="/path-to-genomic-ai-image.png"
          alt="Genomic AI"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          Genomic AI helps scientists understand and work with genes using
          artificial intelligence. It's like a super-smart helper that figures
          out the secrets of DNA.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Example:</strong> It can help doctors find out why some people
          are more likely to get certain diseases.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Real-World Examples:</strong>
        </p>
        <img
          src="/path-to-real-world-examples-image.png"
          alt="Real-World Examples"
          className="mb-4"
        />
        <ol className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Personalized Medicine:</strong> Create special treatments
            just for you, based on your genes.
          </li>
          <li>
            <strong>Disease Prevention:</strong> Predict and stop genetic
            diseases before they happen.
          </li>
          <li>
            <strong>Agriculture:</strong> Grow stronger, healthier crops by
            understanding plant genes.
          </li>
        </ol>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Fun Facts:</strong>
        </p>
        <img
          src="/path-to-fun-facts-image.png"
          alt="Fun Facts"
          className="mb-4"
        />
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            The human genome has about 3 billion "letters" in its DNA code!
          </li>
          <li>
            Genomic AI can look through genetic information much faster than
            people can.
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Understanding Tiny DNA Changes</strong>
        </p>
        <img
          src="/path-to-dna-changes-image.png"
          alt="Understanding DNA Changes"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          <strong>DNA is Like a Book:</strong> Think of your DNA as a giant book
          that has all the instructions for how your body works.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Tiny DNA "Typos":</strong> Sometimes, tiny changes happen in
          the DNA, like little "typos" in the instructions. These are called
          single nucleotide mutations.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Small Changes, Big Impact:</strong> Even though these changes
          are small, they can sometimes cause big problems, like making someone
          sick.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>AI Helps Us Understand:</strong> Scientists use a super-smart
          computer called artificial intelligence (AI) to figure out if these
          tiny DNA "typos" are important.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Cool AI Tool - CADD:</strong> One AI tool called CADD helps
          scientists decide if a DNA change might cause health problems by
          looking at clues in the DNA.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Cool AI Tool - DeepSEA:</strong> Another tool, DeepSEA, helps
          scientists see how DNA changes might affect how our body works, like
          turning genes on or off.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Cool AI Tool - ExPecto:</strong> ExPecto is another tool that
          checks how these changes might affect different parts of our body,
          like our brain or heart.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>New AI Method - DEMINING:</strong> Scientists also use a new
          AI method called DEMINING to find important DNA changes in people with
          diseases like leukemia, using a special test called RNA-seq.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Why Is This Important?</strong>
        </p>
        <img
          src="/path-to-importance-image.png"
          alt="Importance of Genomic AI"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          <strong>AI as a Super Helper:</strong> AI is like a super-powerful
          magnifying glass that helps scientists find hidden secrets in our
          genes.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Learning About Diseases:</strong> With AI, scientists can
          learn more about how diseases happen and how to treat them.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Making Medicine Just for You:</strong> This helps doctors
          create treatments that are perfect for each person’s unique DNA,
          making medicine smarter and better for everyone!
        </p>
      </div>
    </div>
  );
};

export default Sportsgames;

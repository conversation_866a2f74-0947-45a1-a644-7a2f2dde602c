/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandVsco = createReactComponent("outline", "brand-vsco", "IconBrandVsco", [["path", { "d": "M21 12a9 9 0 1 1 -18 0a9 9 0 0 1 18 0z", "key": "svg-0" }], ["path", { "d": "M17 12a5 5 0 1 0 -10 0a5 5 0 0 0 10 0z", "key": "svg-1" }], ["path", { "d": "M12 3v4", "key": "svg-2" }], ["path", { "d": "M21 12h-4", "key": "svg-3" }], ["path", { "d": "M12 21v-4", "key": "svg-4" }], ["path", { "d": "M3 12h4", "key": "svg-5" }], ["path", { "d": "M18.364 5.636l-2.828 2.828", "key": "svg-6" }], ["path", { "d": "M18.364 18.364l-2.828 -2.828", "key": "svg-7" }], ["path", { "d": "M5.636 18.364l2.828 -2.828", "key": "svg-8" }], ["path", { "d": "M5.636 5.636l2.828 2.828", "key": "svg-9" }]]);

export { IconBrandVsco as default };
//# sourceMappingURL=IconBrandVsco.mjs.map

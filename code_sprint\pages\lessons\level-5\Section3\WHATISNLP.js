import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../../level-4/Section1/useTypingAnimation";
import styles from "../../level-4/Section1/useTypingAnimation";
import { Player } from "@lottiefiles/react-lottie-player";

const Howitallbegan = () => {
  const typedText = useTypingAnimation("WHAT IS NLP ?", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>NLP: Teaching Computers to Understand Language</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          NLP stands for Natural Language Processing. It's like teaching
          computers to understand and talk like humans! Imagine you're talking
          to your computer. The process of NLP involves several stages that help
          the computer understand what you're saying and respond appropriately.
          Here’s a simple diagram to visualize how NLP works:
        </p>
        <pre
            style={{
              backgroundColor: "#f4f4f4",
              padding: "10px",
              borderRadius: "5px",
              color:"black"
            }}
          >
          [TEXT INPUT] → [TOKENIZATION] → [TEXT PREPROCESSING] → [UNDERSTANDING]
          → [RESPONSE]
          </pre>

        <p className="text-md text-gray-700 mb-4">
        </p>{" "}
        <h2 className="text-2xl font-bold text-gray-800 mt-6">
          <strong>How NLP Works: A Breakdown of the Steps</strong>
        </h2>
        <table
          className="min-w-full table-auto border-separate border-spacing-0 mt-2"
          style={{ borderRadius: "5px", overflow: "hidden" }}
        >
          <thead>
            <tr>
              <th className="text-lg text-gray-700 bg-gray-300 mb-4 px-4 py-2 border border-gray-300">
                Step
              </th>
              <th className="text-lg text-gray-700 bg-gray-300 mb-4 px-4 py-2 border border-gray-300">
                Explanation
              </th>
              <th className="text-lg text-gray-700 bg-gray-300 mb-4 px-4 py-2 border border-gray-300">
                Example
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                Text Input
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                The starting point where the computer receives a piece of text.
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                Typing "What is the weather today?" into Google.
              </td>
            </tr>
            <tr>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                Tokenization
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                The computer breaks the text into smaller parts called tokens,
                such as words or characters.
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                For "I love pizza," tokens might be ["I", "love", "pizza"].
              </td>
            </tr>
            <tr>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                Text Preprocessing
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                Preparing the text for analysis by cleaning it up, like removing
                extra spaces or fixing typos.
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                "Wh@t is the weathr 2day?" might be cleaned up to "what is the
                weather today?"
              </td>
            </tr>
            <tr>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                Understanding
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                The computer tries to understand the meaning and intent behind
                the text by analyzing the tokens.
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                Understanding that "Can you recommend a good movie?" is asking
                for a movie suggestion.
              </td>
            </tr>
            <tr>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                Response
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                After understanding, the computer generates an appropriate
                response or action based on the text.
              </td>
              <td className="text-lg text-gray-700 mb-4 px-4 py-2 border border-gray-300">
                Responding to "What's the weather today?" with "It's sunny and
                75°F."
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Howitallbegan;

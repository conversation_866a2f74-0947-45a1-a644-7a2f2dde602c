/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrowserMaximize = createReactComponent("outline", "browser-maximize", "IconBrowserMaximize", [["path", { "d": "M4 8h8", "key": "svg-0" }], ["path", { "d": "M20 11.5v6.5a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h6.5", "key": "svg-1" }], ["path", { "d": "M8 4v4", "key": "svg-2" }], ["path", { "d": "M16 8l5 -5", "key": "svg-3" }], ["path", { "d": "M21 7.5v-4.5h-4.5", "key": "svg-4" }]]);

export { IconBrowserMaximize as default };
//# sourceMappingURL=IconBrowserMaximize.mjs.map

import React, { useState, useEffect } from "react";
import { Player } from "@lottiefiles/react-lottie-player";
import { level3Data } from "./0.0CourseContent"; // Import curriculum data

const arrowAnimationUrl =
  "https://fonts.gstatic.com/s/e/notoemoji/latest/1f680/lottie.json"; // Rocket Animation
const ArrowIcon = () => (
  <Player
    autoplay
    loop
    src={arrowAnimationUrl}
    style={{ height: 30, width: 30, marginRight: 10 }}
  />
);

const DisplayDataScience = () => {
  const majorTopics = level3Data.majorTopics.map((topic) => topic.title);
  const bgColors = ["#a7f3d0", "#fef08a", "#bfdbfe", "#fbcfe8", "#e9d5ff"];
  const [currentIndex, setCurrentIndex] = useState(0);
  const [bgColor, setBgColor] = useState(bgColors[0]);
  const [animationCompleted, setAnimationCompleted] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % majorTopics.length;
        setBgColor(bgColors[nextIndex]);
        return nextIndex;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, [majorTopics.length]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-8">
      <style>
        {`
          @keyframes bounceIn {
            0% {
              transform: translateY(-200%);
              opacity: 0;
            }
            50% {
              transform: translateY(20%);
              opacity: 1;
            }
            100% {
              transform: translateY(0%);
            }
          }

          .bouncing-letter {
            display: inline-block;
            animation: bounceIn 0.6s ease forwards;
          }

          .resting-letter {
            animation: bounceIn 1.2s infinite ease-in-out;
          }
        `}
      </style>

      {/* Animated Heading */}
      <h1 className="text-5xl font-bold mb-6 text-gray-800">
        {Array.from("DATA SCIENCE").map((letter, index) => (
          <span
            key={index}
            className={`bouncing-letter ${animationCompleted ? "resting-letter" : ""}`}
            style={{ animationDelay: `${index * 0.2}s`, color: bgColor }}
            onAnimationEnd={() => {
              if (index === "DATA SCIENCE".length - 1) setAnimationCompleted(true);
            }}
          >
            {letter}
          </span>
        ))}
      </h1>

      {/* Description */}
      <p className="text-xl text-gray-700 font-medium text-center max-w-2xl mb-8">
        Explore the fascinating world of <span className="text-blue-500">Data Science</span>, where we turn data into
        powerful insights to solve real-world problems.
      </p>

      {/* Dynamic Topics Section */}
      <div
        className="color-transition p-6 rounded-lg text-left shadow-lg"
        style={{ backgroundColor: bgColor, width: "100%", maxWidth: "500px" }}
      >
        <ul className="list-none">
          <li className="flex items-center text-gray-700">
            <ArrowIcon />
            <span className="text-xl font-mono">{majorTopics[currentIndex]}</span>
          </li>
        </ul>
      </div>

      {/* Highlight Major Topics */}
      <div className="mt-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 w-full max-w-6xl">
        {level3Data.majorTopics.map((topic, index) => (
          <div
            key={index}
            className="p-6 rounded-xl bg-gradient-to-r from-purple-400 via-pink-500 to-red-400 shadow-lg hover:scale-105 transition-transform duration-300 text-white"
          >
            <h2 className="text-xl font-bold mb-4">{topic.title}</h2>
            <ul className="list-disc list-inside space-y-2">
              {topic.minorTopics.map((minor, minorIndex) => (
                <li key={minorIndex} className="text-sm">
                  {minor.title}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      {/* Footer */}
      <p className="mt-10 text-gray-600 text-center">
        Dive into Data Science and unlock the power of data to achieve your goals!
      </p>
    </div>
  );
};

export default DisplayDataScience;

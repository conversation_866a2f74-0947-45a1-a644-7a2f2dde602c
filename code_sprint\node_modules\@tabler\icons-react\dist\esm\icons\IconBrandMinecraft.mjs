/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandMinecraft = createReactComponent("outline", "brand-minecraft", "IconBrandMinecraft", [["path", { "d": "M21 16.008v-8.018a1.98 1.98 0 0 0 -1 -1.717l-7 -4.008a2.016 2.016 0 0 0 -2 0l-7 4.008c-.619 .355 -1 1.01 -1 1.718v8.018c0 .709 .381 1.363 1 1.717l7 4.008c.62 .354 1.38 .354 2 0l7 -4.008c.619 -.355 1 -1.01 1 -1.718z", "key": "svg-0" }], ["path", { "d": "M12 22v-10", "key": "svg-1" }], ["path", { "d": "M12 12l8.73 -5.04", "key": "svg-2" }], ["path", { "d": "M3.27 6.96l8.73 5.04", "key": "svg-3" }], ["path", { "d": "M12 17l3.003 -1.668m3 -1.667l2.997 -1.665m-9 5l-9 -5", "key": "svg-4" }], ["path", { "d": "M15 17l3 -1.67v-3l-3 1.67z", "key": "svg-5" }]]);

export { IconBrandMinecraft as default };
//# sourceMappingURL=IconBrandMinecraft.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandTelegram = createReactComponent("outline", "brand-telegram", "IconBrandTelegram", [["path", { "d": "M15 10l-4 4l6 6l4 -16l-18 7l4 2l2 6l3 -4", "key": "svg-0" }]]);

export { IconBrandTelegram as default };
//# sourceMappingURL=IconBrandTelegram.mjs.map

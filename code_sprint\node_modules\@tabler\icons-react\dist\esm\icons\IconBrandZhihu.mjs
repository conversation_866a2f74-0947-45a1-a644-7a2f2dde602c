/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandZhihu = createReactComponent("outline", "brand-zhihu", "IconBrandZhihu", [["path", { "d": "M14 6h6v12h-2l-2 2l-1 -2h-1z", "key": "svg-0" }], ["path", { "d": "M4 12h6.5", "key": "svg-1" }], ["path", { "d": "M10.5 6h-5", "key": "svg-2" }], ["path", { "d": "M6 4c-.5 2.5 -1.5 3.5 -2.5 4.5", "key": "svg-3" }], ["path", { "d": "M8 6v7c0 4.5 -2 5.5 -4 7", "key": "svg-4" }], ["path", { "d": "M11 18l-3 -5", "key": "svg-5" }]]);

export { IconBrandZhihu as default };
//# sourceMappingURL=IconBrandZhihu.mjs.map

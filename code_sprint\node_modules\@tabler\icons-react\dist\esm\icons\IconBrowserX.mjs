/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrowserX = createReactComponent("outline", "browser-x", "IconBrowserX", [["path", { "d": "M4 4m0 1a1 1 0 0 1 1 -1h14a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1 -1z", "key": "svg-0" }], ["path", { "d": "M4 8h16", "key": "svg-1" }], ["path", { "d": "M8 4v4", "key": "svg-2" }], ["path", { "d": "M10 16l4 -4", "key": "svg-3" }], ["path", { "d": "M14 16l-4 -4", "key": "svg-4" }]]);

export { IconBrowserX as default };
//# sourceMappingURL=IconBrowserX.mjs.map

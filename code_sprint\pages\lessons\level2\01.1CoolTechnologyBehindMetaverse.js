import React from 'react';

export default function CoolTechnologyBehindMetaverse() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-400 to-purple-500 text-white p-10">
      <div className="max-w-4xl text-center">
        <h1 className="text-5xl font-bold mb-8">Cool Technology Behind the Metaverse</h1>
        <p className="text-lg mb-6">
          The Metaverse is built with a mix of groundbreaking technologies that allow users to interact in immersive virtual worlds. Let’s explore the exciting tools that make the Metaverse possible!
        </p>
        <div className="space-y-8">
          {/* Virtual Reality Section */}
          <div className="p-6 bg-white bg-opacity-20 rounded-lg shadow-lg">
            <h2 className="text-3xl font-bold mb-4 text-gray-800">1. Virtual Reality</h2>
            <p className="text-lg text-gray-700">
              Virtual Reality (VR) creates an entirely digital world that you can step into using VR headsets and controllers. You can explore space, swim with dolphins, or fly a plane—all without leaving your room!
            </p>
            <img 
              src="/virtual-reality.png" 
              alt="Kids using VR Headsets" 
              className="w-full rounded-lg mt-4"
            />
          </div>

          {/* Augmented Reality Section */}
          <div className="p-6 bg-white bg-opacity-20 rounded-lg shadow-lg">
            <h2 className="text-3xl font-bold mb-4 text-gray-800">2. Augmented Reality</h2>
            <p className="text-lg text-gray-700">
              Augmented Reality (AR) brings the virtual world to life by adding digital images and animations to your real surroundings. Imagine seeing a game character sitting at your table or trying out furniture before buying it!
            </p>
            <img 
              src="/augmented-reality-example.png" 
              alt="Example of AR with kids" 
              className="w-full rounded-lg mt-4"
            />
          </div>

          {/* Blockchain Section */}
          <div className="p-6 bg-white bg-opacity-20 rounded-lg shadow-lg">
            <h2 className="text-3xl font-bold mb-4 text-gray-800">3. Blockchain</h2>
            <p className="text-lg text-gray-700">
              Blockchain technology ensures secure transactions and ownership of digital items in the Metaverse. It helps create virtual economies where people can buy, sell, and trade virtual goods.
            </p>
            <img 
              src="/blockchain-technology.png" 
              alt="Blockchain technology illustration" 
              className="w-full rounded-lg mt-4"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

import React, { useState, useRef } from 'react';
import Editor from 'react-simple-code-editor';
import Prism from 'prismjs';
import 'prismjs/components/prism-python';
import Replicate from 'replicate';

const ChatBox = () => {
  const [code, setCode] = useState("");
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const outputRef = useRef(null);

  const replicate = new Replicate({
    auth: process.env.REPLICATE_API_TOKEN,
  });

  const handleTyping = (newCode) => {
    setCode(newCode);
  };

  const sendMessage = async () => {
    if (!code.trim()) return;
  
    setMessages((prev) => [...prev, { text: code, sender: 'user' }]);
    setLoading(true);
  
    try {
      const input = {
        prompt: code,
      };
  
      let aiResponse = "";
      const response = await replicate.stream("meta/meta-llama-3-70b-instruct", { input });
  
      for await (const event of response) {
        aiResponse += event.toString();
      }
  
      setMessages((prev) => [...prev, { text: aiResponse || "No response available.", sender: 'ai' }]);
    } catch (error) {
      console.error("Detailed Error:", error);
  
      let errorMessage = "An error occurred while processing your request.";
      if (error.response && error.response.status === 401) {
        errorMessage = "Authentication failed. Please check your API key.";
      } else if (error.message) {
        errorMessage = error.message;
      }
  
      setMessages((prev) => [...prev, { text: errorMessage, sender: 'ai' }]);
    } finally {
      setLoading(false);
      setCode("");
  
      setTimeout(() => {
        outputRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }
  };
  
  return (
    <div className="p-4 max-w-2xl mx-auto">
      <div className="bg-green-100 p-4 rounded-lg">
        <Editor
          value={code}
          onValueChange={handleTyping}
          highlight={(code) => Prism.highlight(code, Prism.languages.python, "python")}
          padding={10}
          className="bg-green-50 text-orange-600 font-mono text-sm min-h-[100px] rounded-lg"
          style={{
            fontFamily: '"Fira Code", monospace',
            fontSize: "14px",
          }}
        />
        <button 
          onClick={sendMessage} 
          disabled={loading}
          className={`mt-2 px-4 py-2 rounded ${
            loading 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-green-500 hover:bg-green-600 text-white'
          }`}
        >
          {loading ? "Sending..." : "Send"}
        </button>
      </div>
      <div ref={outputRef} className="mt-4 space-y-2">
        {messages.map((msg, index) => (
          <div 
            key={index} 
            className={`p-2 rounded ${
              msg.sender === 'user' 
                ? 'bg-blue-100 text-right ml-auto' 
                : 'bg-green-100 text-left mr-auto'
            }`}
          >
            <strong>{msg.sender === 'user' ? 'You' : 'AI'}:</strong> {msg.text}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChatBox;

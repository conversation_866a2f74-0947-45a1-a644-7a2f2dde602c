/* Center content using flexbox */
.registerContainer {
  display: flex;
  /* font-weight: bold; */
  justify-content: center;
  align-items: center;
  min-height: 100vh; /* Full viewport height to ensure vertical centering */
  background-color: rgb(60, 59, 91); /* added light gray background color */
}

/* Structure the card and form content */
.card {
  padding: 2rem;
  border-radius: 10px;
  background: rgb(12, 7, 36);
  border: 1px solid rgba(0, 0, 0, 0.3);
  transition: background 200ms, border 200ms;
  max-width: 600px;
  width: 100%;
  text-align: center; /* Center text inside the card */
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); /* added box shadow */
}

/* Title styling */
.title {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #ffffff; /* added dark gray title color */
}

/* Input field styling */
.input {
  height: 40px;
  width: 100%;
  padding: 0.75rem;
  margin-top: 0.25rem;
  border: 2px solid rgba(66, 64, 64, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
}

.input:focus {
  outline: none;
  border-color: rgba(0, 0, 0, 0.6);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); /* added box shadow on focus */
}

/* Error message styling */
.error {
  color: #FF0000; /* added red error text color */
  margin-bottom: 1rem;
  font-size: 0.85rem;
}

/* Button styling */
.btn {
  background-color: #009688; /* added teal button background color */
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 200ms;
  width: 100%;
  text-align: center;
}

.btn:hover {
  background-color: #00796B; /* added darker teal button background color on hover */
}

/* Form group styling for spacing between elements */
.formGroup {
  margin-bottom: 1.5rem;
  text-align: left;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  /* font-weight: bold; */
  /* font-size: 16px; */
}

.flexContainer {
  display: flex;
  justify-content: space-between;
}

.formGroup {
  flex: 1;
  margin-right: 10px;
}

.formGroup:last-child {
  margin-right: 0;
}

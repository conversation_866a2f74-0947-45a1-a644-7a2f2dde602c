/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandTabler = createReactComponent("outline", "brand-tabler", "IconBrandTabler", [["path", { "d": "M8 9l3 3l-3 3", "key": "svg-0" }], ["path", { "d": "M13 15h3", "key": "svg-1" }], ["path", { "d": "M3 7a4 4 0 0 1 4 -4h10a4 4 0 0 1 4 4v10a4 4 0 0 1 -4 4h-10a4 4 0 0 1 -4 -4z", "key": "svg-2" }]]);

export { IconBrandTabler as default };
//# sourceMappingURL=IconBrandTabler.mjs.map

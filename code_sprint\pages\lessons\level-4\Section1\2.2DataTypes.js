import React, { useState, useRef } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const IntegerOperations = () => {
  const [code, setCode] = useState(`# Python Integer Operations
# Declare an integer
num1 = 10
num2 = 5

# Perform addition
sum_result = num1 + num2

# Perform subtraction
difference_result = num1 - num2

# Perform multiplication
product_result = num1 * num2

# Perform division
quotient_result = num1 / num2

print("Sum:", sum_result)
print("Difference:", difference_result)
print("Product:", product_result)
print("Quotient:", quotient_result)
`);

  const animatedCode = useTypingAnimation(code);

  const typedText = useTypingAnimation("Explore Python Data Types!", 100);

  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);

  const outputRef = useRef(null);

  const runCode = () => {
    try {
      const num1 = 10;
      const num2 = 5;
      const sum_result = num1 + num2;
      const difference_result = num1 - num2;
      const product_result = num1 * num2;
      const quotient_result = num1 / num2;
  
      setOutput(`
        Sum: ${sum_result}
        Difference: ${difference_result}
        Product: ${product_result}
        Quotient: ${quotient_result}`);
  
      setShowOutput(true);
      setShowConfetti(true);
  
      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);
  
      setTimeout(() => {
        document.querySelector(`.${styles.confetti}`)?.classList.add(styles.hidden);
        setTimeout(() => setShowConfetti(false), 500);
      }, 1000);
  
    } catch (error) {
      console.error(error);
      setOutput("Error: There was a problem running your code.");
      setShowOutput(true);
    }
  };
  

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom right, #FFEB3B, #4CAF50)",
        padding: "20px",
      }}
    >
      <div
        style={{
          maxWidth: "800px",
          textAlign: "center",
          backgroundColor: "#fff",
          padding: "20px",
          borderRadius: "15px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.1)",
          position: "relative",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            <div
              style={{
                position: "absolute",
                width: "10px",
                height: "10px",
                backgroundColor: "#FF4081",
                animation: "fall 1.5s infinite linear",
              }}
            />
            <div
              style={{
                position: "absolute",
                width: "10px",
                height: "10px",
                backgroundColor: "#FFD700",
                animation: "fall 1.5s infinite linear",
              }}
            />
            <div
              style={{
                position: "absolute",
                width: "10px",
                height: "10px",
                backgroundColor: "#7C4DFF",
                animation: "fall 1.5s infinite linear",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #FF4081, #448AFF)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
            animation: "bounce 1.5s infinite",
          }}
        >
          {typedText}
        </h1>{" "}
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            textAlign: "center",
            fontSize: "1.2rem",
            color: "#555",
          }}
        >
          <p>
            Learn about different <strong>data types</strong> in Python and how
            they work.{" "}
          </p>
          <Player
            src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f680/lottie.json"
            style={{ width: "24px", height: "24px", marginLeft: "8px" }}
            autoplay
            loop
          />
        </div>{" "}
        <hr style={{ margin: "20px 0", borderColor: "#ddd" }} />
        <p
          style={{
            fontWeight: "bold",
            fontSize: "2rem",
            color: "#333",
            marginTop: "20px",
          }}
        >
          Data Types in Python
        </p>
        <div
          style={{
            fontSize: "1rem",
            color: "#666",
            marginTop: "10px",
            textAlign: "center",
            columnGap: "20px",
          }}
        >
          <h3 style={{ color: "#4CAF50", fontWeight: "bold" }}>INTEGER</h3>
          <p>
            Integers (
            <code style={{ fontFamily: "monospace", color: "#448AFF" }}>
              int
            </code>
            ) are whole numbers, positive or negative, without a decimal point.
            Examples include:
          </p>
          <pre
            style={{
              backgroundColor: "#f4f4f4",
              padding: "10px",
              borderRadius: "5px",
            }}
          >
            -7, 42, 0
          </pre>
          <p>
            <code style={{ color: "#4CAF50" }}>Cars = 95</code>
          </p>
          <h3
            style={{ color: "#f3218c", fontWeight: "bold", marginTop: "15px" }}
          >
            FLOAT
          </h3>
          <p>
            Floating-Point Numbers (
            <code style={{ fontFamily: "monospace", color: "#448AFF" }}>
              float
            </code>
            ) include a decimal point. Examples include:
          </p>
          <pre
            style={{
              backgroundColor: "#f4f4f4",
              padding: "10px",
              borderRadius: "5px",
            }}
          >
            3.14, -0.03, 2.0
          </pre>
          <p>
            <code style={{ color: "#f3218c" }}>Pressure = 56.7</code>
          </p>
          <h3
            style={{ color: "#FF9800", fontWeight: "bold", marginTop: "15px" }}
          >
            STRING
          </h3>
          <p>
            Strings (
            <code style={{ fontFamily: "monospace", color: "#448AFF" }}>
              str
            </code>
            ) are sequences of characters enclosed in quotes. They can use
            single or double quotes. Examples include:
          </p>
          <pre
            style={{
              backgroundColor: "#f4f4f4",
              padding: "10px",
              borderRadius: "5px",
            }}
          >
            &quot;hello world&quot;, &apos;journey to python!!&apos;
          </pre>
          <p>
            <code style={{ color: "#FF9800" }}>
              Goals_2024 = &quot;become a python legend&quot;
            </code>
            <br />
            <code style={{ color: "#FF9800" }}>
              Message_to_self = &apos;i can do it!&apos;
            </code>
          </p>
          <h3
            style={{ color: "#9C27B0", fontWeight: "bold", marginTop: "15px" }}
          >
            BOOLEAN
          </h3>
          <p>
            Booleans (
            <code style={{ fontFamily: "monospace", color: "#448AFF" }}>
              bool
            </code>
            ) represent two values:
            <code style={{ fontFamily: "monospace", color: "#448AFF" }}>
              True
            </code>{" "}
            and{" "}
            <code style={{ fontFamily: "monospace", color: "#448AFF" }}>
              False
            </code>
            . They are used in conditional statements and comparisons. Examples
            include:
          </p>
          <pre
            style={{
              backgroundColor: "#f4f4f4",
              padding: "10px",
              borderRadius: "5px",
            }}
          >
            Hamilton_is_Goated = True
            <br />
            Alonso_rookie = False
          </pre>
        </div>
        <p
          style={{
            fontWeight: "bold",
            fontSize: "0.9rem",
            color: "#666",
            marginTop: "20px",
          }}
        >
          Use the editor below to explore the Python data types:
        </p>
        <pre
          style={{
            backgroundColor: "#F0F8FF",
            padding: "15px",
            borderRadius: "10px",
            textAlign: "left",
            color: "#444",
            marginTop: "10px",
            border: "1px solid #B3E5FC",
          }}
        >
          # Declare an integer{"\n"}
          num1 = 10{"\n\n"}
          num2 = 5{"\n\n"}# Perform addition{"\n"}
          sum_result = num1 + num2{"\n\n"}# Perform subtraction{"\n"}
          difference_result = num1 - num2{"\n\n"}# Perform multiplication{"\n"}
          product_result = num1 * num2{"\n\n"}# Perform division{"\n"}
          quotient_result = num1 / num2{"\n\n"}
          print(&quot;Sum:&quot;, sum_result){"\n"}
          print(&quot;Difference:&quot;, difference_result){"\n"}
          print(&quot;Product:&quot;, product_result){"\n"}
          print(&quot;Quotient:&quot;, quotient_result)
        </pre>
        <div
          style={{
            position: "relative", zIndex: 10,
            backgroundColor: "#d4d193",
            padding: "15px",
            borderRadius: "10px",
            marginTop: "20px",
            textAlign: "left",
            color: "#fff",
          }}
        >
          <Editor
            value={animatedCode}
            onValueChange={(newCode) => setCode(newCode)}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              borderRadius: "10px",
              backgroundColor: "#eee5bf",
              color: "#5d4a66",
              minHeight: "500px",
            }}
          />
        </div>
        
        <div>
          {}
          {!showOutput && (
            <button
              onClick={runCode}
              className={styles.fadeInUp}
              style={{
                position: "relative", zIndex: 10,
                marginTop: "20px",
                backgroundColor: "#d2ab99",
                color: "#37371f",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
              }}
            >
              Run Code 🎉
            </button>
          )}

          {}
          <div className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "88%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#EBA2C7",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #FFE082",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#524C4F",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#444", marginTop: "10px" }}
              >
                {output}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IntegerOperations;

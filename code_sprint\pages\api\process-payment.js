import dbConnect from '../../utils/dbConnect';
import User from '../../models/User';
import jwt from 'jsonwebtoken';
import { getHashedString } from '@/utils/common';

export default async function handler(req, res) {
  await dbConnect();

  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided or invalid token format' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const { level, amount } = req.body;

    if (!level) {
      return res.status(400).json({ message: 'Level and amount are required' });
    }

    // Paycaps payment integration here
    const orderId = `ORDER_${Date.now()}_${user._id.toString()}`;
    
    const paymentParams = {
      APP_ID: process.env.PAYCAPS_APP_ID,
      ORDER_ID: orderId,
      RETURN_URL: `${req.headers.origin}/api/payment-callback`,
      MERCHANTNAME: process.env.PAYCAPS_MERCHANT_NAME,
      AMOUNT: amount * 100, // Amount in phils/paisa/cents
      CURRENCY_CODE: 784, // Numerical code for AED as in paycaps documentation
      TXNTYPE: 'SALE', // Dont know whether its required, becoz its not mentioned in request params, but mentioned in response params.
      CUST_NAME: user.name ?? 'test_name',
      CUST_EMAIL: user.email ?? '<EMAIL>',
      CUST_PHONE: user.phone ?? 526395565,
      CUST_CITY: user.city ?? 'test_city',
      CUST_COUNTRY: user.country ?? 'test_country',
      CUST_ZIP: user.zip ?? 123,
    };
    
    const hash = getHashedString(paymentParams);
    paymentParams.HASH = hash;

    try {
      const result = await User.findByIdAndUpdate(decoded.userId, {
        pendingOrderId: orderId,
        pendingPremiumLevel: level,
      });
      console.log("result :\n", result);
      return res.status(200).json({ 
        message: 'Payment initiated', 
        paymentParams 
      });
      
    } catch (error) {
      console.error('Payment initiation failed:', error);
      return res.status(500).json({ message: 'Payment initiation failed', error: error.message });
    }    
  } catch (error) {
    console.error('Error during payment processing:', error);
    res.status(500).json({ message: 'Payment failed', error: error.message });
  }
}

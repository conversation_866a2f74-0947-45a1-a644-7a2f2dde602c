/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBubbleTea2 = createReactComponent("outline", "bubble-tea-2", "IconBubbleTea2", [["path", { "d": "M17.95 9l-1.478 8.69c-.25 1.463 -.374 2.195 -.936 2.631c-1.2 .931 -6.039 .88 -7.172 0c-.562 -.436 -.687 -1.168 -.936 -2.632l-1.478 -8.689", "key": "svg-0" }], ["path", { "d": "M6 9l.514 -1.286a5.908 5.908 0 0 1 10.972 0l.514 1.286", "key": "svg-1" }], ["path", { "d": "M5 9h14", "key": "svg-2" }], ["path", { "d": "M12 9l4 -7", "key": "svg-3" }], ["path", { "d": "M7 14c.593 .642 1.484 1.017 2.5 1c1.016 .017 1.907 -.358 2.5 -1s1.484 -1.017 2.5 -1c1.016 -.017 1.907 .358 2.5 1", "key": "svg-4" }]]);

export { IconBubbleTea2 as default };
//# sourceMappingURL=IconBubbleTea2.mjs.map

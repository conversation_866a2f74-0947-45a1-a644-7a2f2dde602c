/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCameraAi = createReactComponent("outline", "camera-ai", "IconCameraAi", [["path", { "d": "M10 20h-5a2 2 0 0 1 -2 -2v-9a2 2 0 0 1 2 -2h1a2 2 0 0 0 2 -2a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1a2 2 0 0 0 2 2h1a2 2 0 0 1 2 2v2", "key": "svg-0" }], ["path", { "d": "M14.362 11.15a3 3 0 1 0 -4.144 4.263", "key": "svg-1" }], ["path", { "d": "M14 21v-4a2 2 0 1 1 4 0v4", "key": "svg-2" }], ["path", { "d": "M14 19h4", "key": "svg-3" }], ["path", { "d": "M21 15v6", "key": "svg-4" }]]);

export { IconCameraAi as default };
//# sourceMappingURL=IconCameraAi.mjs.map

import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import MarkAsReadButton from './MarkAsReadButton';
import { trackLessonAccess } from '../utils/progressTracker';

const LessonWrapper = ({ 
  children, 
  lessonId, 
  level, 
  title,
  showMarkAsRead = true 
}) => {
  const router = useRouter();

  // Track lesson access when component mounts
  useEffect(() => {
    const trackAccess = async () => {
      try {
        await trackLessonAccess(lessonId, level);
      } catch (error) {
        console.error('Error tracking lesson access:', error);
      }
    };

    if (lessonId && level) {
      trackAccess();
    }
  }, [lessonId, level]);

  const handleLessonComplete = (progress) => {
    console.log('Lesson completed!', progress);
    
    // Optional: Show completion animation or redirect
    // You can add custom logic here
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Lesson Header */}
      {title && (
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 mb-6">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold mb-2">{title}</h1>
            <div className="flex items-center space-x-4 text-sm opacity-90">
              <span className="bg-white/20 px-3 py-1 rounded-full">
                {level?.toUpperCase()}
              </span>
              <span>Lesson: {lessonId}</span>
            </div>
          </div>
        </div>
      )}

      {/* Lesson Content */}
      <div className="max-w-4xl mx-auto px-6 pb-12">
        {children}
        
        {/* Mark as Read Button */}
        {showMarkAsRead && lessonId && level && (
          <div className="mt-8 pt-8 border-t border-gray-200">
            <MarkAsReadButton 
              lessonId={lessonId} 
              level={level}
              onComplete={handleLessonComplete}
            />
          </div>
        )}
      </div>

      {/* Navigation Footer */}
      <div className="bg-gray-50 border-t border-gray-200 p-6">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <button
            onClick={() => router.back()}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back</span>
          </button>
          
          <button
            onClick={() => router.push('/dashboard')}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};

export default LessonWrapper;

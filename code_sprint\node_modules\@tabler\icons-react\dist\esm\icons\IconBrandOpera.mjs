/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandOpera = createReactComponent("outline", "brand-opera", "IconBrandOpera", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M12 12m-3 0a3 5 0 1 0 6 0a3 5 0 1 0 -6 0", "key": "svg-1" }]]);

export { IconBrandOpera as default };
//# sourceMappingURL=IconBrandOpera.mjs.map

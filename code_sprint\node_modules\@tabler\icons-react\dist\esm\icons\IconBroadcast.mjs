/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBroadcast = createReactComponent("outline", "broadcast", "IconBroadcast", [["path", { "d": "M18.364 19.364a9 9 0 1 0 -12.728 0", "key": "svg-0" }], ["path", { "d": "M15.536 16.536a5 5 0 1 0 -7.072 0", "key": "svg-1" }], ["path", { "d": "M12 13m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-2" }]]);

export { IconBroadcast as default };
//# sourceMappingURL=IconBroadcast.mjs.map

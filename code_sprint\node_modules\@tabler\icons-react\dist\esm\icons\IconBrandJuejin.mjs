/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandJuejin = createReactComponent("outline", "brand-juejin", "IconBrandJuejin", [["path", { "d": "M2 12l10 7.422l10 -7.422", "key": "svg-0" }], ["path", { "d": "M7 9l5 4l5 -4", "key": "svg-1" }], ["path", { "d": "M11 6l1 .8l1 -.8l-1 -.8z", "key": "svg-2" }]]);

export { IconBrandJuejin as default };
//# sourceMappingURL=IconBrandJuejin.mjs.map

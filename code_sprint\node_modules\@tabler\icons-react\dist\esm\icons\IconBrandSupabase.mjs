/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandSupabase = createReactComponent("outline", "brand-supabase", "IconBrandSupabase", [["path", { "d": "M4 14h8v7l8 -11h-8v-7z", "key": "svg-0" }]]);

export { IconBrandSupabase as default };
//# sourceMappingURL=IconBrandSupabase.mjs.map

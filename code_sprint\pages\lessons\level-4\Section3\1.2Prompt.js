import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Prompt = () => {
  const typedText = useTypingAnimation("COOKING UP AN EFFECTIVE PROMPT", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Specificity: The Key to Success</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          The more specific your prompt, the better the AI&apos;s response will be.
          Instead of saying, &quot;
Tell me about the ocean,&quot;
 try saying, &quot;
Describe
          the different zones of the ocean and the creatures that live there.&quot;

          The second prompt provides the AI with a clear direction, resulting in
          a more detailed answer.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Write a prompt about your favorite hobby. Be as specific as possible.
          For example:
        </p>
        <p className="text-lg text-gray-700 mb-4">
          &quot;Describe the process of creating a landscape painting using
          watercolors.&quot;
        </p>
        <hr className="my-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Clarity: Make Your Intentions Clear</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          A clear prompt avoids any confusion. Rather than saying, &quot;Explain
          programming,&quot; you could say, &quot;Explain the basics of Python programming
          for beginners.&quot; This makes your intentions clear, so the AI knows
          exactly what you&apos;re asking for.
        </p>
        <aside className="bg-green-400 p-4 rounded-lg">
          💡 <strong>Tip:</strong> Avoid using slang or vague terms. Keep your
          language simple and direct.
        </aside>
        <hr className="my-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Creativity: Thinking Outside the Box</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Creativity makes prompt writing exciting! Don&apos;t hesitate to ask the AI
          to do something unusual or imaginative. For example:
        </p>
        <p className="text-lg text-gray-700 mb-4">
          &quot;Imagine a world where animals can speak. What would a day in the life
          of a dog be like?&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Write a creative prompt that combines two different themes, like
          nature and technology. For example:
        </p>
        <p className="text-lg text-gray-700 mb-4">
          &quot;What would a robotic tree look like? Describe its features and
          functions.&quot;
        </p>
        <hr className="my-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Common Mistakes and How to Avoid Them</strong>
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Being too vague:</strong> Ensure your prompt has enough
            detail for the AI to understand your request.
          </li>
          <li>
            <strong>Using unclear language:</strong> Keep your words
            straightforward and clear.
          </li>
          <li>
            <strong>Asking too much at once:</strong> Focus on one idea at a
            time for the best results.
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          <strong>INSTRUCTIONS</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Look at the following prompt and think of ways to improve it:
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Original:</strong> &quot;Tell me about food.&quot;
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Improved:</strong> &quot;Describe traditional Italian dishes and
          how they are prepared.&quot;
        </p>
      </div>
    </div>
  );
};

export default Prompt;

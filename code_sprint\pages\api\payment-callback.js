import dbConnect from '../../utils/dbConnect';
import User from '../../models/User';
import { getHashedString } from '@/utils/common';

export default async function handler(req, res) {
  await dbConnect();
  
  try {
    const {
      APP_ID,
      ORDER_ID,
      AMOUNT,
      CURRENCY_CODE,
      TXNTYPE,
      CUST_NAME,
      CUST_EMAIL,
      CUST_PHONE,
      CUST_CITY,
      CUST_COUNTRY,
      CUST_ZIP,
      MERCHANTNAME,
      RETURN_URL,
      // HASH,
      RESPONSE_CODE,
      RESPONSE_MESSAGE,
    } = req.body;
    
    const responseParams = {
      APP_ID,
      ORDER_ID,
      AMOUNT,
      CURRENCY_CODE,
      TXNTYPE,
      CUST_NAME,
      CUST_EMAIL,
      CUST_PHONE,
      CUST_CITY,
      CUST_COUNTRY,
      CUST_ZIP,
      MERCHANTNAME,
      RETURN_URL
    };
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Response Params:\n', JSON.stringify(responseParams, null, 3));
    }
    // const hashString = getHashedString(responseParams);
    const user = await User.findOne({ pendingOrderId: ORDER_ID });
    console.log('User:\n', JSON.stringify(user, null, 3));
    if (!user) {
      return res.redirect(302, `/payment/failed?orderId=${ORDER_ID}`);
    }
    
    if (RESPONSE_CODE === '000') {
      user.isPremium = true;
      user.premiumLevel = user.pendingPremiumLevel;
      user.pendingOrderId = null;
      user.pendingPremiumLevel = null;
      await user.save();
      return res.redirect(302, `/payment/success?orderId=${ORDER_ID}`);
    } else {
      // Payment failed
      user.pendingOrderId = null;
      user.pendingPremiumLevel = null;
      await user.save();
      return res.redirect(302, `/payment/failed?orderId=${ORDER_ID}&message=${RESPONSE_MESSAGE}`);
    }
  } catch (error) {
    console.error('Error processing payment callback:', error);
    return res.status(500).json({ message: 'Error processing payment callback', error: error.message });
  }
}

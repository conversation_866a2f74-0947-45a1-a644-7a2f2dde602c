/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBuildingChurch = createReactComponent("outline", "building-church", "IconBuildingChurch", [["path", { "d": "M3 21l18 0", "key": "svg-0" }], ["path", { "d": "M10 21v-4a2 2 0 0 1 4 0v4", "key": "svg-1" }], ["path", { "d": "M10 5l4 0", "key": "svg-2" }], ["path", { "d": "M12 3l0 5", "key": "svg-3" }], ["path", { "d": "M6 21v-7m-2 2l8 -8l8 8m-2 -2v7", "key": "svg-4" }]]);

export { IconBuildingChurch as default };
//# sourceMappingURL=IconBuildingChurch.mjs.map

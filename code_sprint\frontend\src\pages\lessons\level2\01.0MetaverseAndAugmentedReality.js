import React, { useState } from 'react';

export default function MetaverseAndAugmentedReality() {
    const [quizAnswer, setQuizAnswer] = useState(null);
    const [quizFeedback, setQuizFeedback] = useState('');

    const handleQuiz = (answer) => {
        setQuizAnswer(answer);
        setQuizFeedback(
            answer === 'b'
                ? 'Correct! The Metaverse is a 3D virtual world where people interact.'
                : 'Oops! Try again.'
        );
    };

    return (
        <div className="min-h-screen flex flex-col items-center bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 text-white py-10">
            <h1 className="text-5xl font-bold mb-6">Metaverse and Augmented Reality</h1>
            <p className="text-lg text-center max-w-4xl mb-12">
                Discover the amazing world of the Metaverse and Augmented Reality, where digital and physical realities come together to create fun and immersive experiences!
            </p>

            {/* Metaverse Section */}
            <div className="bg-white bg-opacity-20 rounded-lg shadow-lg p-8 max-w-3xl mb-10">
                <h2 className="text-4xl font-bold text-gray-800 mb-4">What is the Metaverse?</h2>
                <p className="text-lg text-gray-700 mb-4">
                    Imagine stepping into a giant, interactive video game world! The Metaverse is like another version of the internet, where you can work, play, and meet friends in a 3D virtual space.
                </p>
                <img 
                    src="/metaverse-example.png" 
                    alt="Metaverse Example" 
                    className="w-full rounded-lg shadow-md mb-4"
                />
                <div className="bg-white p-4 rounded-md">
                    <h3 className="text-2xl font-semibold text-gray-800 mb-2">Quiz: What is the Metaverse?</h3>
                    <select
                        onChange={(e) => handleQuiz(e.target.value)}
                        className="w-full p-2 rounded-lg bg-gray-200 text-gray-700 mb-2"
                    >
                        <option value="">Select an answer</option>
                        <option value="a">A type of video game</option>
                        <option value="b">A 3D virtual world where people interact</option>
                        <option value="c">A new social media app</option>
                        <option value="d">A music streaming platform</option>
                    </select>
                    {quizAnswer && (
                        <p className={`mt-2 ${quizAnswer === 'b' ? 'text-green-500' : 'text-red-500'}`}>
                            {quizFeedback}
                        </p>
                    )}
                </div>
            </div>

            {/* How Does the Metaverse Work */}
            <div className="bg-white bg-opacity-20 rounded-lg shadow-lg p-8 max-w-3xl mb-10">
                <h2 className="text-4xl font-bold text-gray-800 mb-4">How Does the Metaverse Work?</h2>
                <p className="text-lg text-gray-700 mb-4">
                    The Metaverse connects virtual worlds using the internet. People use VR goggles or smartphones to enter this digital space, create avatars, and interact in real time. Technologies like blockchain ensure secure and seamless experiences.
                </p>
                <img 
                    src="/metaverse-technology.png" 
                    alt="Metaverse Technology" 
                    className="w-full rounded-lg shadow-md"
                />
            </div>

            {/* Augmented Reality Section */}
            <div className="bg-white bg-opacity-20 rounded-lg shadow-lg p-8 max-w-3xl mb-10">
                <h2 className="text-4xl font-bold text-gray-800 mb-4">What is Augmented Reality?</h2>
                <p className="text-lg text-gray-700 mb-4">
                    Augmented Reality (AR) blends digital elements with the real world. Using devices like smartphones or AR glasses, you can see animations, characters, or helpful information overlaid onto your surroundings.
                </p>
                <img 
                    src="/augmented-reality.png" 
                    alt="Augmented Reality Example" 
                    className="w-full rounded-lg shadow-md mb-4"
                />
            </div>

            {/* Fun with Games */}
            <div className="bg-white bg-opacity-20 rounded-lg shadow-lg p-8 max-w-3xl mb-10">
                <h2 className="text-4xl font-bold text-gray-800 mb-4">Fun with Games</h2>
                <p className="text-lg text-gray-700 mb-4">
                    AR and the Metaverse are amazing for games! Imagine catching virtual creatures in real-world locations or exploring magical 3D worlds with friends. Games make learning fun and interactive!
                </p>
                <img 
                    src="/gaming-fun.png" 
                    alt="Gaming with AR and Metaverse" 
                    className="w-full rounded-lg shadow-md"
                />
            </div>

            {/* Closing Section */}
            <div className="text-center max-w-3xl mt-10">
                <h2 className="text-3xl font-bold mb-4">Ready to Explore?</h2>
                <p className="text-lg">
                    Step into the world of the Metaverse and Augmented Reality to experience the future of technology and learning. Discover endless possibilities and have fun along the way!
                </p>
            </div>
        </div>
    );
}

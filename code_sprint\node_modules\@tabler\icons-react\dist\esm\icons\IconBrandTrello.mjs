/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandTrello = createReactComponent("outline", "brand-trello", "IconBrandTrello", [["path", { "d": "M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M7 7h3v10h-3z", "key": "svg-1" }], ["path", { "d": "M14 7h3v6h-3z", "key": "svg-2" }]]);

export { IconBrandTrello as default };
//# sourceMappingURL=IconBrandTrello.mjs.map

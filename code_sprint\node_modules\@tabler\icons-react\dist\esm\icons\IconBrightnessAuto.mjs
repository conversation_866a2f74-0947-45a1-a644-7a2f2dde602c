/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrightnessAuto = createReactComponent("outline", "brightness-auto", "IconBrightnessAuto", [["path", { "d": "M6 6h3.5l2.5 -2.5l2.5 2.5h3.5v3.5l2.5 2.5l-2.5 2.5v3.5h-3.5l-2.5 2.5l-2.5 -2.5h-3.5v-3.5l-2.5 -2.5l2.5 -2.5z", "key": "svg-0" }], ["path", { "d": "M10 14.5v-3.5a2 2 0 1 1 4 0v3.5", "key": "svg-1" }], ["path", { "d": "M10 13h4", "key": "svg-2" }]]);

export { IconBrightnessAuto as default };
//# sourceMappingURL=IconBrightnessAuto.mjs.map

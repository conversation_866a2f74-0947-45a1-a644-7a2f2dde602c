/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconHistoryOff = createReactComponent("outline", "history-off", "IconHistoryOff", [["path", { "d": "M3.05 11a8.975 8.975 0 0 1 2.54 -5.403m2.314 -1.697a9 9 0 0 1 12.113 12.112m-1.695 2.312a9 9 0 0 1 -14.772 -3.324m-.5 5v-5h5", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]]);

export { IconHistoryOff as default };
//# sourceMappingURL=IconHistoryOff.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandXing = createReactComponent("outline", "brand-xing", "IconBrandXing", [["path", { "d": "M16 21l-4 -7l6.5 -11", "key": "svg-0" }], ["path", { "d": "M7 7l2 3.5l-3 4.5", "key": "svg-1" }]]);

export { IconBrandXing as default };
//# sourceMappingURL=IconBrandXing.mjs.map

import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Sportsgames = () => {
  const typedText = useTypingAnimation("Exploring the Types of Esports Games", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Sports Games</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Let&apos;s kick things off with sports games! If you&apos;re into soccer,
          basketball, or racing, this category has something for you. Esports
          brings your favorite sports to life in a virtual arena.
        </p>

        <p className="text-lg text-gray-700 mb-4">
          <strong>FIFA:</strong> Just like on a real soccer field, FIFA allows
          you to control your favorite teams and players in intense matches. You
          can pass, dribble, and score goals, all while strategizing to
          outmaneuver your opponent. FIFA tournaments are massive, with players
          from around the world competing to be the best.
        </p>
        <img src="/fifa.png" alt="FIFA" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>NBA 2K:</strong> For basketball fans, NBA 2K offers the chance
          to play as your favorite NBA teams and players. Like on a real court,
          you&apos;ll need to master dribbling, shooting, and teamwork to secure the
          win. It&apos;s like having your own virtual basketball tournament at home.
        </p>
        <img src="/nba2k.png" alt="NBA 2K" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          Sports games are perfect if you love the excitement of real-world
          sports and want to experience that same thrill in a video game. Plus,
          they&apos;re great for friendly matches or joining an online league!
        </p>
      </div>
    </div>
  );
};

export default Sportsgames;

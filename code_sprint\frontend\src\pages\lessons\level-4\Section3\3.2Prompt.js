import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Prompt = () => {
  const typedText = useTypingAnimation("SIT BACK AND REFLECT", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <h3 className="text-lg text-gray-700 mb-4 font-bold">
          What Have You Learned?
        </h3>
        <p className="text-lg text-gray-700 mb-4">
          Now that you&apos;ve gone through the journey of learning about prompt
          engineering, it&apos;s time to reflect on what you&apos;ve gained. Consider how
          your understanding of prompts has evolved and what skills you&apos;ve
          developed along the way.
        </p>
        <h4 className="text-lg text-gray-700 mb-4 font-bold">INSTRUCTIONS</h4>
        <p className="text-lg text-gray-700 mb-4">
          Write a short reflection on your experience with prompt engineering.
          What was the most fascinating thing you learned? What was the most
          challenging aspect? How do you think you&apos;ll use these skills in the
          future?
        </p>
        <hr className="mb-4" />
        <h3 className="text-lg text-gray-700 mb-4 font-bold">
          Peer Review: Exchanging Feedback
        </h3>
        <p className="text-lg text-gray-700 mb-4">
          Feedback is an essential part of the learning process. By sharing your
          prompts with others and receiving their input, you can gain new
          perspectives and ideas.
        </p>
        <h4 className="text-lg text-gray-700 mb-4 font-bold">INSTRUCTIONS</h4>
        <p className="text-lg text-gray-700 mb-4">
          Pair up with a classmate and exchange prompt booklets. Review each
          other&apos;s work and offer constructive feedback. Highlight what you liked
          and suggest areas for improvement. Use this feedback to further refine
          your prompts.
        </p>
        <hr className="mb-4" />
        <h3 className="text-lg text-gray-700 mb-4 font-bold">
          Your Prompt Journey: Tracking Your Progress
        </h3>
        <p className="text-lg text-gray-700 mb-4">
          Prompt engineering is a skill that you can continue to hone over time.
          By documenting your progress, you can see how far you&apos;ve come and set
          new goals for the future.
        </p>
        <h4 className="text-lg text-gray-700 mb-4 font-bold">INSTRUCTIONS</h4>
        <p className="text-lg text-gray-700 mb-4">
          Create a timeline or journal that captures your journey as a prompt
          engineer. Include your first prompts, the challenges you faced, and
          how you overcame them. Set goals for what you want to learn or achieve
          next.
        </p>
      </div>
    </div>
  );
};

export default Prompt;

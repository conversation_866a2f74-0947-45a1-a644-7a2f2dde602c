import React, { useEffect } from "react";
import level1Data from "../../../data/level1Data";
import { useRouter } from "next/router";

export default function Level1Lessons() {
  const [currentMajorTopic, setCurrentMajorTopic] = React.useState(null);
  const [currentMinorTopic, setCurrentMinorTopic] = React.useState(null);
  const router = useRouter();

  const handleMinorTopicClick = (majorIndex, minorIndex) => {
    setCurrentMajorTopic(majorIndex);
    setCurrentMinorTopic(minorIndex);
  };

  const handleBackClick = () => {
    setCurrentMajorTopic(null);
    setCurrentMinorTopic(null);
  };

  const currentTopic =
    level1Data.majorTopics[currentMajorTopic]?.minorTopics[currentMinorTopic];
  const CurrentLessonComponent = currentTopic?.component;

  // Load Spline Viewer Script
  useEffect(() => {
    const script = document.createElement("script");
    script.src =
      "https://unpkg.com/@splinetool/viewer@1.9.54/build/spline-viewer.js";
    script.type = "module";
    document.body.appendChild(script);
    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-1/4 bg-purple-700 text-white p-6 flex flex-col justify-between">
        <div>
          <h2 className="text-3xl font-extrabold mb-8 text-center drop-shadow-lg">
            Course Progress
          </h2>
          <ul className="space-y-4">
            {currentMajorTopic === null
              ? level1Data.majorTopics.map((major, majorIndex) => (
                  <li
                    key={majorIndex}
                    onClick={() => handleMinorTopicClick(majorIndex, 0)}
                    className="text-lg font-semibold hover:text-yellow-300 cursor-pointer transition-all"
                  >
                    {major.title}
                  </li>
                ))
              : level1Data.majorTopics[currentMajorTopic]?.minorTopics.map(
                  (minor, minorIndex) => (
                    <li
                      key={minorIndex}
                      onClick={() =>
                        handleMinorTopicClick(currentMajorTopic, minorIndex)
                      }
                      className="text-lg font-semibold hover:text-yellow-300 cursor-pointer transition-all"
                    >
                      {minor.title}
                    </li>
                  )
                )}
          </ul>
          {currentMajorTopic !== null && (
            <button
              onClick={handleBackClick}
              className="mt-6 bg-yellow-500 text-black py-2 px-4 rounded-lg hover:bg-yellow-400 transition-all shadow-md"
            >
              Back to Topics
            </button>
          )}
        </div>

        {/* Back to Dashboard Button */}
        <button
          onClick={() => router.push("/dashboard")}
          className="mt-6 bg-green-500 text-white py-3 px-6 rounded-lg shadow-lg hover:bg-green-600 transition-all duration-300"
        >
          Back to Dashboard
        </button>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center p-10 bg-white shadow-md rounded-lg mx-6 my-6">
        {currentMajorTopic === null && currentMinorTopic === null ? (
          <div className="text-center mt-20">
            <h1 className="text-5xl font-extrabold text-gray-800 mb-4">
              Welcome to Level 1!
            </h1>
            <p className="text-lg text-gray-600 mt-4 leading-relaxed">
              Start learning by selecting a topic from the sidebar.
            </p>
            {/* Add Spline Viewer */}
            <div className="mt-8 w-full">
              <spline-viewer
                url="https://prod.spline.design/gUAXNK7hcAGZebnj/scene.splinecode"
                style={{
                  width: "100%",
                  height: "500px",
                  borderRadius: "12px",
                  boxShadow: "0px 4px 10px rgba(0,0,0,0.2)",
                }}
              ></spline-viewer>
            </div>
          </div>
        ) : (
          <div className="text-gray-800">
            {/* Display Current Lesson */}
            {CurrentLessonComponent}
          </div>
        )}
      </div>
    </div>
  );
}

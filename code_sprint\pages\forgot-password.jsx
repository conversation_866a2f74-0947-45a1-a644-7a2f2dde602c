// pages/forgot-password.jsx
import { useState } from 'react';
// import { useRouter } from 'next/router';
import Image from 'next/image';
import Link from 'next/link';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  // const router = useRouter();

  const handleForgotPassword = async (e) => {
    e.preventDefault();
    setMessage('');
    setError('');
    try {
      const res = await fetch('/api/forgot-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      if (res.ok) {
        setMessage('A password reset link has been sent to your email.');
      } else {
        const errorData = await res.json();
        setError(errorData.message || 'Failed to send reset link. Please try again.');
      }
    } catch (error) {
      console.error('An unexpected error occurred:', error);
      setError('An unexpected error occurred');
    }
  };

  return (
    <div className="min-h-screen bg-[#FFF6E5] flex flex-col items-center justify-center p-4">
      <header className="flex flex-col items-center mb-8">
        <Image src="/codesprint-logo.png" alt="CodeSprint Logo" width={150} height={50} />
        <h1 className="text-3xl font-bold text-[#5E2C99] mt-4">Forgot secret code</h1>
        <h3 className="text-sm font-normal text-[#5E2C99] mt-4">Don&apos;t worry, sometimes it happens</h3>
      </header>
      <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full border-4 border-[#5E2C99] text-center">
        <p className="text-gray-600 mb-6">Enter your email to receive a secret code reset link.</p>
        <form onSubmit={handleForgotPassword} className="space-y-6">
          <div className="text-left">
            <label htmlFor="email" className="block text-sm font-medium text-[#5E2C99]">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              required
              className="mt-1 block w-full px-3 py-2 bg-[#F3F4F6] border-2 border-[#FFC107] rounded-md text-sm shadow-sm placeholder-gray-400
                         focus:outline-none focus:border-[#5E2C99] focus:ring-2 focus:ring-[#5E2C99]"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
            />
          </div>
          {message && <p className="text-green-600 text-sm font-bold">{message}</p>}
          {error && <p className="text-[#E91E63] text-sm font-bold">{error}</p>}
          <button
            type="submit"
            className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#4CAF50] hover:bg-[#45a049] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#4CAF50] transition-colors duration-200"
          >
            Send Reset Link
          </button>
        </form>
        <div className="mt-6">
          <p className="text-center text-sm text-gray-600">
            Remember your secret code?{' '}
            <Link href="/login" className="font-medium text-[#5E2C99] hover:text-[#3a1c70]">
              Go back to login
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

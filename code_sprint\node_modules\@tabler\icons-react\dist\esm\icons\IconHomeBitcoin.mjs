/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconHomeBitcoin = createReactComponent("outline", "home-bitcoin", "IconHomeBitcoin", [["path", { "d": "M17 21v-6m2 0v-1.5m0 9v-1.5m-2 -3h3m-1 0h.5a1.5 1.5 0 0 1 0 3h-3.5m3 -3h.5a1.5 1.5 0 0 0 0 -3h-3.5", "key": "svg-0" }], ["path", { "d": "M19.5 10.5l-7.5 -7.5l-9 9h2v7a2 2 0 0 0 2 2h6", "key": "svg-1" }], ["path", { "d": "M9 21v-6a2 2 0 0 1 2 -2h2c.387 0 .748 .11 1.054 .3", "key": "svg-2" }], ["path", { "d": "M21 15h-2.5a1.5 1.5 0 0 0 0 3h1a1.5 1.5 0 0 1 0 3h-2.5", "key": "svg-3" }]]);

export { IconHomeBitcoin as default };
//# sourceMappingURL=IconHomeBitcoin.mjs.map

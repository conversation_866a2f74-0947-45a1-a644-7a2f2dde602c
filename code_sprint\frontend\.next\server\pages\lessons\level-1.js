/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/lessons/level-1";
exports.ids = ["pages/lessons/level-1"];
exports.modules = {

/***/ "(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel-1&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel-1.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel-1&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel-1.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./src/pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./src/pages/_app.js\");\n/* harmony import */ var _src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/lessons/level-1.js */ \"(pages-dir-node)/./src/pages/lessons/level-1.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/lessons/level-1\",\n        pathname: \"/lessons/level-1\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel-1&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel-1.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./src/data/level1Data.js":
/*!********************************!*\
  !*** ./src/data/level1Data.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _pages_lessons_level1_1_1WhatIsComputer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../pages/lessons/level1/1.1WhatIsComputer */ \"(pages-dir-node)/./src/pages/lessons/level1/1.1WhatIsComputer.js\");\n/* harmony import */ var _pages_lessons_level1_1_2HowComputersWork__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../pages/lessons/level1/1.2HowComputersWork */ \"(pages-dir-node)/./src/pages/lessons/level1/1.2HowComputersWork.js\");\n/* harmony import */ var _pages_lessons_level1_1_3WhatIsAProgram__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../pages/lessons/level1/1.3WhatIsAProgram */ \"(pages-dir-node)/./src/pages/lessons/level1/1.3WhatIsAProgram.js\");\n/* harmony import */ var _pages_lessons_level1_1_4TypesOfComputers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../pages/lessons/level1/1.4TypesOfComputers */ \"(pages-dir-node)/./src/pages/lessons/level1/1.4TypesOfComputers.js\");\n/* harmony import */ var _pages_lessons_level1_1_5HardwareAndSoftware__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../pages/lessons/level1/1.5HardwareAndSoftware */ \"(pages-dir-node)/./src/pages/lessons/level1/1.5HardwareAndSoftware.js\");\n/* harmony import */ var _pages_lessons_level1_1_6OperatingSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../pages/lessons/level1/1.6OperatingSystem */ \"(pages-dir-node)/./src/pages/lessons/level1/1.6OperatingSystem.js\");\n/* harmony import */ var _pages_lessons_level1_2_1WhatIsScratch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../pages/lessons/level1/2.1WhatIsScratch */ \"(pages-dir-node)/./src/pages/lessons/level1/2.1WhatIsScratch.js\");\n/* harmony import */ var _pages_lessons_level1_3_1WhatIsIoT__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pages/lessons/level1/3.1WhatIsIoT */ \"(pages-dir-node)/./src/pages/lessons/level1/3.1WhatIsIoT.js\");\n/* harmony import */ var _pages_lessons_level1_3_2HowIoTWorks__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../pages/lessons/level1/3.2HowIoTWorks */ \"(pages-dir-node)/./src/pages/lessons/level1/3.2HowIoTWorks.js\");\n/* harmony import */ var _pages_lessons_level1_3_3IoTExamples__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../pages/lessons/level1/3.3IoTExamples */ \"(pages-dir-node)/./src/pages/lessons/level1/3.3IoTExamples.js\");\n/* harmony import */ var _pages_lessons_level1_3_4IoTBenefits__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../pages/lessons/level1/3.4IoTBenefits */ \"(pages-dir-node)/./src/pages/lessons/level1/3.4IoTBenefits.js\");\n/* harmony import */ var _pages_lessons_level1_4_1WhatIsComputerVision__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../pages/lessons/level1/4.1WhatIsComputerVision */ \"(pages-dir-node)/./src/pages/lessons/level1/4.1WhatIsComputerVision.js\");\n/* harmony import */ var _pages_lessons_level1_4_2HowComputerVisionWorks__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../pages/lessons/level1/4.2HowComputerVisionWorks */ \"(pages-dir-node)/./src/pages/lessons/level1/4.2HowComputerVisionWorks.js\");\n/* harmony import */ var _pages_lessons_level1_4_3ComputerVisionApplications__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../pages/lessons/level1/4.3ComputerVisionApplications */ \"(pages-dir-node)/./src/pages/lessons/level1/4.3ComputerVisionApplications.js\");\n/* harmony import */ var _pages_lessons_level1_5_1Summary__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../pages/lessons/level1/5.1Summary */ \"(pages-dir-node)/./src/pages/lessons/level1/5.1Summary.js\");\n/* harmony import */ var _pages_lessons_level1_5_3CompletionMessage__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../pages/lessons/level1/5.3CompletionMessage */ \"(pages-dir-node)/./src/pages/lessons/level1/5.3CompletionMessage.js\");\n\n\n\n\n\n\n\n\n\n// import NavigatingScratch from \"../pages/lessons/level1/2.2NavigatingScratch\";\n// import FirstScratchProject from \"../pages/lessons/level1/2.3FirstScratchProject\";\n\n\n\n\n\n\n\n\n\nconst level1Data = {\n    majorTopics: [\n        {\n            title: \"Introduction to Computers\",\n            minorTopics: [\n                {\n                    title: \"What is a Computer?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_1WhatIsComputer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 26,\n                        columnNumber: 52\n                    }, undefined)\n                },\n                {\n                    title: \"How Computers Work\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_2HowComputersWork__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 27,\n                        columnNumber: 51\n                    }, undefined)\n                },\n                {\n                    title: \"What is a Computer Program?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_3WhatIsAProgram__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 28,\n                        columnNumber: 60\n                    }, undefined)\n                },\n                {\n                    title: \"Different Types of Computers\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_4TypesOfComputers__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 29,\n                        columnNumber: 61\n                    }, undefined)\n                },\n                {\n                    title: \"Hardware vs. Software\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_5HardwareAndSoftware__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 30,\n                        columnNumber: 54\n                    }, undefined)\n                },\n                {\n                    title: \"What is an Operating System?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_6OperatingSystem__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 31,\n                        columnNumber: 61\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: \"Getting Started with Scratch\",\n            minorTopics: [\n                {\n                    title: \"What is Scratch?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_2_1WhatIsScratch__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 37,\n                        columnNumber: 49\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: \"Introduction to IoT\",\n            minorTopics: [\n                {\n                    title: \"What is IoT?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_3_1WhatIsIoT__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 45,\n                        columnNumber: 45\n                    }, undefined)\n                },\n                {\n                    title: \"How IoT Works\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_3_2HowIoTWorks__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 46,\n                        columnNumber: 46\n                    }, undefined)\n                },\n                {\n                    title: \"Examples of IoT Devices\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_3_3IoTExamples__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 47,\n                        columnNumber: 56\n                    }, undefined)\n                },\n                {\n                    title: \"Benefits of IoT\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_3_4IoTBenefits__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 48,\n                        columnNumber: 48\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: \"Introduction to Computer Vision\",\n            minorTopics: [\n                {\n                    title: \"What is Computer Vision?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_4_1WhatIsComputerVision__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 54,\n                        columnNumber: 57\n                    }, undefined)\n                },\n                {\n                    title: \"How Does Computer Vision Work?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_4_2HowComputerVisionWorks__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 55,\n                        columnNumber: 63\n                    }, undefined)\n                },\n                {\n                    title: \"Applications of Computer Vision\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_4_3ComputerVisionApplications__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 56,\n                        columnNumber: 64\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: \"Conclusion and Review\",\n            minorTopics: [\n                {\n                    title: \"Summary of Key Concepts\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_5_1Summary__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 62,\n                        columnNumber: 58\n                    }, undefined)\n                },\n                {\n                    title: \"Course Completion Message\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_5_3CompletionMessage__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/data/level1Data.js\",\n                        lineNumber: 63,\n                        columnNumber: 60\n                    }, undefined)\n                }\n            ]\n        }\n    ]\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (level1Data);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/data/level1Data.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_app.js":
/*!***************************!*\
  !*** ./src/pages/_app.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(pages-dir-node)/./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lessons/level-4/Section1/animations.module.css */ \"(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\");\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"prop-types\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n// pages/_app.js\n\n // Global styles\n // Animations CSS\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_app.js\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n// Add PropTypes for App\nApp.propTypes = {\n    Component: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().elementType).isRequired,\n    pageProps: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().object).isRequired\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBLGdCQUFnQjs7QUFDYyxDQUFDLGdCQUFnQjtBQUNXLENBQUMsaUJBQWlCO0FBQ3pDO0FBRXBCLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQscUJBQU8sOERBQUNEO1FBQVcsR0FBR0MsU0FBUzs7Ozs7O0FBQ2pDO0FBRUEsd0JBQXdCO0FBQ3hCRixJQUFJRyxTQUFTLEdBQUc7SUFDZEYsV0FBV0YsK0RBQXFCLENBQUNNLFVBQVU7SUFDM0NILFdBQVdILDBEQUFnQixDQUFDTSxVQUFVO0FBQ3hDIiwic291cmNlcyI6WyIvbW50L2QvQWFtaXIvY29kZXNwcmludC9jb2RlX3NwcmludC9mcm9udGVuZC9zcmMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWdlcy9fYXBwLmpzXG5pbXBvcnQgXCJAL3N0eWxlcy9nbG9iYWxzLmNzc1wiOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgXCIuL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3NcIjsgLy8gQW5pbWF0aW9ucyBDU1NcbmltcG9ydCBQcm9wVHlwZXMgZnJvbSBcInByb3AtdHlwZXNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPjtcbn1cblxuLy8gQWRkIFByb3BUeXBlcyBmb3IgQXBwXG5BcHAucHJvcFR5cGVzID0ge1xuICBDb21wb25lbnQ6IFByb3BUeXBlcy5lbGVtZW50VHlwZS5pc1JlcXVpcmVkLCAvLyBDb21wb25lbnQgbXVzdCBiZSBhIHZhbGlkIFJlYWN0IGNvbXBvbmVudFxuICBwYWdlUHJvcHM6IFByb3BUeXBlcy5vYmplY3QuaXNSZXF1aXJlZCwgLy8gcGFnZVByb3BzIGlzIGFuIG9iamVjdFxufTtcbiJdLCJuYW1lcyI6WyJQcm9wVHlwZXMiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJwcm9wVHlwZXMiLCJlbGVtZW50VHlwZSIsImlzUmVxdWlyZWQiLCJvYmplY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_document.js":
/*!********************************!*\
  !*** ./src/pages/_document.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7Ozs7MEJBQ0wsOERBQUNLOztrQ0FDQyw4REFBQ0osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL19kb2N1bWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSBcIm5leHQvZG9jdW1lbnRcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZCAvPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxNYWluIC8+XG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9IdG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_document.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level-1.js":
/*!**************************************!*\
  !*** ./src/pages/lessons/level-1.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Level1Lessons)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_level1Data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../data/level1Data */ \"(pages-dir-node)/./src/data/level1Data.js\");\n/* harmony import */ var _splinetool_react_spline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @splinetool/react-spline */ \"@splinetool/react-spline\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_splinetool_react_spline__WEBPACK_IMPORTED_MODULE_3__]);\n_splinetool_react_spline__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n // Adjust the path if needed\n // Import the Spline component\nfunction Level1Lessons() {\n    const [currentMajorTopic, setCurrentMajorTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentMinorTopic, setCurrentMinorTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleMajorTopicClick = (majorIndex)=>{\n        setCurrentMajorTopic(majorIndex);\n        setCurrentMinorTopic(0); // Automatically select the first sub-topic\n    };\n    const handleBackToMajorTopics = ()=>{\n        setCurrentMajorTopic(null);\n        setCurrentMinorTopic(null);\n    };\n    const currentTopic = currentMajorTopic !== null && currentMinorTopic !== null && _data_level1Data__WEBPACK_IMPORTED_MODULE_2__[\"default\"].majorTopics[currentMajorTopic]?.minorTopics[currentMinorTopic];\n    const CurrentLessonComponent = currentTopic?.component;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-1/4 bg-purple-700 text-white p-6 flex flex-col justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-extrabold mb-8 text-center drop-shadow-lg\",\n                                children: \"Course Progress\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-4\",\n                                children: currentMajorTopic === null ? _data_level1Data__WEBPACK_IMPORTED_MODULE_2__[\"default\"].majorTopics.map((major, majorIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        onClick: ()=>handleMajorTopicClick(majorIndex),\n                                        className: \"text-lg font-semibold cursor-pointer hover:text-yellow-300 transition-all\",\n                                        children: major.title\n                                    }, majorIndex, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                                        lineNumber: 37,\n                                        columnNumber: 19\n                                    }, this)) : _data_level1Data__WEBPACK_IMPORTED_MODULE_2__[\"default\"].majorTopics[currentMajorTopic]?.minorTopics.map((minor, minorIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        onClick: ()=>setCurrentMinorTopic(minorIndex),\n                                        className: `text-lg font-semibold cursor-pointer ${currentMinorTopic === minorIndex ? \"text-yellow-300\" : \"hover:text-yellow-300\"} transition-all`,\n                                        children: minor.title\n                                    }, minorIndex, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                                        lineNumber: 47,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            currentMajorTopic !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleBackToMajorTopics,\n                                className: \"mt-6 bg-yellow-500 text-black py-2 px-4 rounded-lg hover:bg-yellow-400 transition-all shadow-md\",\n                                children: \"Back to Topics\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>alert(\"Back to Dashboard\"),\n                        className: \"mt-6 bg-green-500 text-white py-3 px-6 rounded-lg shadow-lg hover:bg-green-600 transition-all duration-300\",\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative bg-gradient-to-b from-blue-400 via-white to-green-600 flex flex-col items-center justify-center p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            pointerEvents: \"none\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_splinetool_react_spline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            scene: \"https://prod.spline.design/gUAXNK7hcAGZebnj/scene.splinecode\",\n                            style: {\n                                width: \"100%\",\n                                height: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    currentMajorTopic === null && currentMinorTopic === null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-extrabold text-purple-700 mb-6 animate-bounce\",\n                                children: \"Welcome Young Developer!\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl text-gray-700 mt-4 mb-6 leading-relaxed\",\n                                children: [\n                                    \"Get ready to embark on an exciting learning journey! \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                                        lineNumber: 107,\n                                        columnNumber: 68\n                                    }, this),\n                                    \"Select a topic from the sidebar and dive into interactive lessons and activities.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleMajorTopicClick(0),\n                                className: \"mt-8 bg-purple-500 text-white py-3 px-8 rounded-full font-bold shadow-md hover:bg-purple-600 transition-all\",\n                                children: \"Let's Get Started!\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 p-6 w-full\",\n                        children: CurrentLessonComponent || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700\",\n                            children: \"No lesson found for the selected topic.\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                            lineNumber: 121,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level-1.js\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level-1.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css":
/*!******************************************************************!*\
  !*** ./src/pages/lessons/level-4/Section1/animations.module.css ***!
  \******************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"confetti\": \"animations_confetti__VOjD6\",\n\t\"fadeInUp\": \"animations_fadeInUp__wib6A\",\n\t\"hidden\": \"animations_hidden__KLYnx\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9sZXNzb25zL2xldmVsLTQvU2VjdGlvbjEvYW5pbWF0aW9ucy5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY29uZmV0dGlcIjogXCJhbmltYXRpb25zX2NvbmZldHRpX19WT2pENlwiLFxuXHRcImZhZGVJblVwXCI6IFwiYW5pbWF0aW9uc19mYWRlSW5VcF9fd2liNkFcIixcblx0XCJoaWRkZW5cIjogXCJhbmltYXRpb25zX2hpZGRlbl9fS0xZbnhcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/1.1WhatIsComputer.js":
/*!*******************************************************!*\
  !*** ./src/pages/lessons/level1/1.1WhatIsComputer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n // Import Image from next/image\n\nconst WhatIsComputer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \" bg-white text-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white-100 border-l-4 border-blue-500 p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-4 text-purple-700\",\n                    children: \"What is a Computer?\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 8,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg mb-4\",\n                    children: [\n                        \"A computer is a \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-bold text-blue-500\",\n                            children: \"smart machine\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                            lineNumber: 12,\n                            columnNumber: 25\n                        }, undefined),\n                        \" that helps us do many things like play games, draw pictures, learn new things, and talk to our friends. It can follow instructions to do all these tasks very quickly.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 11,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Microchip on a Fingertip:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Did you know? Computers that used to take up an entire room now fit comfortably on your finger! A microchip is a tiny part inside a computer that helps it do all the amazing things it can do.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex justify-center items-center\",\n                    children: [\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            src: \"/lvl1_img/Untitled.png\" // Corrected path\n                            ,\n                            alt: \"A computer illustration\",\n                            width: 500,\n                            height: 300,\n                            className: \"rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhatIsComputer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/1.1WhatIsComputer.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/1.2HowComputersWork.js":
/*!*********************************************************!*\
  !*** ./src/pages/lessons/level1/1.2HowComputersWork.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n // Import Next.js Image component\n\nconst HowComputersWork = ()=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        Input: \"\",\n        Storage: \"\",\n        Processing: \"\",\n        Output: \"\"\n    });\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const options = [\n        \"Processor\",\n        \"Keyboard\",\n        \"Monitor\",\n        \"Hard Drive\",\n        \"Meow🐱\"\n    ];\n    const correctAnswers = {\n        Input: \"Keyboard\",\n        Storage: \"Hard Drive\",\n        Processing: \"Processor\",\n        Output: \"Monitor\"\n    };\n    const handleMatch = (part, value)=>{\n        setMatches((prevMatches)=>({\n                ...prevMatches,\n                [part]: value\n            }));\n    };\n    const checkAnswers = ()=>{\n        const newFeedback = {};\n        let allCorrect = true;\n        for (const [part, correct] of Object.entries(correctAnswers)){\n            if (matches[part] === correct) {\n                newFeedback[part] = \"🌟 Correct!\";\n            } else {\n                newFeedback[part] = \"🚫 Try again!\";\n                allCorrect = false;\n            }\n        }\n        setFeedback(newFeedback);\n        setIsSubmitted(true);\n        if (allCorrect) {\n            alert(\"🎉 Great job! You matched all the parts correctly!\");\n        }\n    };\n    const resetActivity = ()=>{\n        setMatches({\n            Input: \"\",\n            Storage: \"\",\n            Processing: \"\",\n            Output: \"\"\n        });\n        setFeedback({});\n        setIsSubmitted(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 bg-gradient-to-b from-blue-200 via-purple-50 to-pink-100 p-8 min-h-screen rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700 text-center animate-bounce\",\n                children: \"How Computers Work\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    src: \"/lvl1_img/a-clean-and-modern-educational-illustrat_5raWjKvJSMK6ZvBRud3Q7w_FWwn2-2QTKurLKSC523P9g.jpeg\",\n                    alt: \"Fun computer illustration\",\n                    width: 400,\n                    height: 250,\n                    className: \"rounded-lg shadow-lg\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl mb-6 text-center\",\n                children: [\n                    \"A computer combines \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"input\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 78,\n                        columnNumber: 29\n                    }, undefined),\n                    \", \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"storage\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 78,\n                        columnNumber: 53\n                    }, undefined),\n                    \",\",\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"processing\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    \", and \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"output\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 79,\n                        columnNumber: 42\n                    }, undefined),\n                    \" to function.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"list-disc pl-8 mb-8 text-lg text-purple-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Input:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Devices like a keyboard, mouse, or microphone give the computer information.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Memory/Storage:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Computers store your files on a hard drive or memory card.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Processing:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            \" The processor, a microchip, processes the data with help from a cooling fan.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Output:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Results are shown using devices like screens, speakers, or printers.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 p-6 rounded-xl shadow-lg border-l-4 border-blue-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-center font-semibold text-blue-800 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Activity:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Match the computer parts with their functions by selecting the correct option below!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-6\",\n                        children: [\n                            \"Input\",\n                            \"Storage\",\n                            \"Processing\",\n                            \"Output\"\n                        ].map((part, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-xl shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4 text-purple-600\",\n                                        children: [\n                                            index + 1,\n                                            \". \",\n                                            part\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        className: \"p-3 border border-gray-300 rounded-xl bg-white shadow-inner w-full\",\n                                        value: matches[part],\n                                        onChange: (e)=>handleMatch(part, e.target.value),\n                                        disabled: isSubmitted,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select an option\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option,\n                                                    children: option\n                                                }, option, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `mt-4 text-center font-bold ${feedback[part] === \"🌟 Correct!\" ? \"text-green-600\" : \"text-red-600\"}`,\n                                        children: feedback[part]\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, part, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex justify-center gap-6\",\n                        children: !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-6 py-3 bg-purple-600 text-white font-bold rounded-xl shadow-md hover:bg-purple-700 transition-all\",\n                            onClick: checkAnswers,\n                            children: \"Check Answers\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-6 py-3 bg-yellow-500 text-white font-bold rounded-xl shadow-md hover:bg-yellow-600 transition-all\",\n                            onClick: resetActivity,\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.2HowComputersWork.js\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HowComputersWork);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/1.2HowComputersWork.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/1.3WhatIsAProgram.js":
/*!*******************************************************!*\
  !*** ./src/pages/lessons/level1/1.3WhatIsAProgram.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst WhatIsAProgram = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 bg-gradient-to-b from-blue-100 via-purple-50 to-green-100 p-6 rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700 text-center animate-bounce\",\n                children: \"What is a Computer Program?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl mb-4 text-center\",\n                children: [\n                    \"A \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"computer program\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, undefined),\n                    \" is like a recipe! It's a set of instructions that tells the computer how to do cool things like playing a game, drawing pictures, or solving problems.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex justify-center items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: \"/lvl1_img/Untitled (3).png\",\n                    alt: \"A fun computer illustration\",\n                    width: 400,\n                    height: 250,\n                    className: \"rounded-lg shadow-lg\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 mt-6 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Fun Fact:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Long ago, people had to write programs to perform even simple tasks! Now, we have apps like\",\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                            children: \"Microsoft Word\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined),\n                        \" and games that make things easier and more fun!\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl mt-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Programs vs. Calculators:\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    \" Programs are like smart helpers. Calculators only do math, but programs can play music, draw, and even play chess!\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 p-4 border-l-4 border-green-500 mt-6 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Activity:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Think about the programs or apps you use daily. What do they do for you?\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\nconst DragDropActivity = ()=>{\n    const items = [\n        {\n            id: 1,\n            name: \"Microsoft Word\",\n            correctCategory: \"Programs\"\n        },\n        {\n            id: 2,\n            name: \"Calculator\",\n            correctCategory: \"Programs\"\n        },\n        {\n            id: 3,\n            name: \"Notebook (Paper)\",\n            correctCategory: \"Not Programs\"\n        },\n        {\n            id: 4,\n            name: \"Chess App\",\n            correctCategory: \"Programs\"\n        },\n        {\n            id: 5,\n            name: \"Pen\",\n            correctCategory: \"Not Programs\"\n        }\n    ];\n    const [droppedItems, setDroppedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        Programs: [],\n        \"Not Programs\": []\n    });\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleDrop = (category, item)=>{\n        setDroppedItems((prev)=>{\n            if (prev[category].some((i)=>i.id === item.id)) return prev;\n            return {\n                ...prev,\n                [category]: [\n                    ...prev[category],\n                    item\n                ]\n            };\n        });\n    };\n    const checkAnswers = ()=>{\n        let allCorrect = true;\n        for (const [category, itemsInCategory] of Object.entries(droppedItems)){\n            for (const item of itemsInCategory){\n                if (item.correctCategory !== category) {\n                    allCorrect = false;\n                }\n            }\n        }\n        setFeedback(allCorrect ? \"🎉 Great job! All answers are correct!\" : \"❌ Some answers are incorrect. Try again!\");\n    };\n    const resetActivity = ()=>{\n        setDroppedItems({\n            Programs: [],\n            \"Not Programs\": []\n        });\n        setFeedback(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-gradient-to-b from-yellow-100 via-white to-blue-100 rounded-lg shadow-md mt-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-4xl font-bold text-purple-700 text-center mb-6\",\n                children: \"Fun Activity: Programs or Not?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-6 text-black\",\n                children: [\n                    \"Drag and drop the items into the correct category:\",\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Programs\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined),\n                    \" or \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Not Programs\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 104,\n                        columnNumber: 38\n                    }, undefined),\n                    \".\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4 flex-wrap justify-center text-black\",\n                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        draggable: true,\n                        onDragStart: (e)=>e.dataTransfer.setData(\"item\", JSON.stringify(item)),\n                        className: \"p-3 bg-white border border-gray-300 rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-all\",\n                        children: item.name\n                    }, item.id, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 grid grid-cols-2 gap-6\",\n                children: [\n                    \"Programs\",\n                    \"Not Programs\"\n                ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onDragOver: (e)=>e.preventDefault(),\n                        onDrop: (e)=>{\n                            const data = e.dataTransfer.getData(\"item\");\n                            const item = JSON.parse(data);\n                            handleDrop(category, item);\n                        },\n                        className: \"p-6 bg-blue-50 border border-blue-300 rounded-lg min-h-[150px] shadow-inner flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-purple-600 mb-4\",\n                                children: category\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: droppedItems[category].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-lg text-gray-700\",\n                                        children: item.name\n                                    }, item.id, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, category, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 flex justify-center gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-6 py-3 bg-purple-600 text-white font-bold rounded-lg shadow-md hover:bg-purple-700 transition-all\",\n                        onClick: checkAnswers,\n                        children: \"Check Answers\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-6 py-3 bg-gray-600 text-white font-bold rounded-lg shadow-md hover:bg-gray-700 transition-all\",\n                        onClick: resetActivity,\n                        children: \"Reset\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-6 text-lg font-semibold text-center text-black\",\n                children: feedback\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\nconst App = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gradient-to-b from-blue-100 via-white to-green-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WhatIsAProgram, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DragDropActivity, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.3WhatIsAProgram.js\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (App);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9sZXNzb25zL2xldmVsMS8xLjNXaGF0SXNBUHJvZ3JhbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF3QztBQUNUO0FBRS9CLE1BQU1HLGlCQUFpQjtJQUNyQixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUFxRTs7Ozs7OzBCQUduRiw4REFBQ0U7Z0JBQUVGLFdBQVU7O29CQUEyQjtrQ0FDcEMsOERBQUNHO2tDQUFPOzs7Ozs7b0JBQXlCOzs7Ozs7OzBCQUlyQyw4REFBQ0o7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNILG1EQUFLQTtvQkFDSk8sS0FBSTtvQkFDSkMsS0FBSTtvQkFDSkMsT0FBTztvQkFDUEMsUUFBUTtvQkFDUlAsV0FBVTs7Ozs7Ozs7Ozs7MEJBR2QsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRTs7c0NBQ0MsOERBQUNDO3NDQUFPOzs7Ozs7d0JBQWtCO3dCQUN3QjtzQ0FDbEQsOERBQUNLO3NDQUFHOzs7Ozs7d0JBQW1COzs7Ozs7Ozs7Ozs7MEJBSTNCLDhEQUFDTjtnQkFBRUYsV0FBVTs7a0NBQ1gsOERBQUNHO2tDQUFPOzs7Ozs7b0JBQWtDOzs7Ozs7OzBCQUk1Qyw4REFBQ0o7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNFOztzQ0FDQyw4REFBQ0M7c0NBQU87Ozs7Ozt3QkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1wQztBQUVBLE1BQU1NLG1CQUFtQjtJQUN2QixNQUFNQyxRQUFRO1FBQ1o7WUFBRUMsSUFBSTtZQUFHQyxNQUFNO1lBQWtCQyxpQkFBaUI7UUFBVztRQUM3RDtZQUFFRixJQUFJO1lBQUdDLE1BQU07WUFBY0MsaUJBQWlCO1FBQVc7UUFDekQ7WUFBRUYsSUFBSTtZQUFHQyxNQUFNO1lBQW9CQyxpQkFBaUI7UUFBZTtRQUNuRTtZQUFFRixJQUFJO1lBQUdDLE1BQU07WUFBYUMsaUJBQWlCO1FBQVc7UUFDeEQ7WUFBRUYsSUFBSTtZQUFHQyxNQUFNO1lBQU9DLGlCQUFpQjtRQUFlO0tBQ3ZEO0lBRUQsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR25CLCtDQUFRQSxDQUFDO1FBQy9Db0IsVUFBVSxFQUFFO1FBQ1osZ0JBQWdCLEVBQUU7SUFDcEI7SUFDQSxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR3RCLCtDQUFRQSxDQUFDO0lBRXpDLE1BQU11QixhQUFhLENBQUNDLFVBQVVDO1FBQzVCTixnQkFBZ0IsQ0FBQ087WUFDZixJQUFJQSxJQUFJLENBQUNGLFNBQVMsQ0FBQ0csSUFBSSxDQUFDLENBQUNDLElBQU1BLEVBQUViLEVBQUUsS0FBS1UsS0FBS1YsRUFBRSxHQUFHLE9BQU9XO1lBRXpELE9BQU87Z0JBQ0wsR0FBR0EsSUFBSTtnQkFDUCxDQUFDRixTQUFTLEVBQUU7dUJBQUlFLElBQUksQ0FBQ0YsU0FBUztvQkFBRUM7aUJBQUs7WUFDdkM7UUFDRjtJQUNGO0lBRUEsTUFBTUksZUFBZTtRQUNuQixJQUFJQyxhQUFhO1FBQ2pCLEtBQUssTUFBTSxDQUFDTixVQUFVTyxnQkFBZ0IsSUFBSUMsT0FBT0MsT0FBTyxDQUFDZixjQUFlO1lBQ3RFLEtBQUssTUFBTU8sUUFBUU0sZ0JBQWlCO2dCQUNsQyxJQUFJTixLQUFLUixlQUFlLEtBQUtPLFVBQVU7b0JBQ3JDTSxhQUFhO2dCQUNmO1lBQ0Y7UUFDRjtRQUNBUixZQUNFUSxhQUNJLDJDQUNBO0lBRVI7SUFFQSxNQUFNSSxnQkFBZ0I7UUFDcEJmLGdCQUFnQjtZQUNkQyxVQUFVLEVBQUU7WUFDWixnQkFBZ0IsRUFBRTtRQUNwQjtRQUNBRSxZQUFZO0lBQ2Q7SUFFQSxxQkFDRSw4REFBQ25CO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDK0I7Z0JBQUcvQixXQUFVOzBCQUFzRDs7Ozs7OzBCQUdwRSw4REFBQ0U7Z0JBQUVGLFdBQVU7O29CQUFzQztvQkFDRTtrQ0FDbkQsOERBQUNHO2tDQUFPOzs7Ozs7b0JBQWlCO2tDQUFJLDhEQUFDQTtrQ0FBTzs7Ozs7O29CQUFxQjs7Ozs7OzswQkFJNUQsOERBQUNKO2dCQUFJQyxXQUFVOzBCQUNaVSxNQUFNc0IsR0FBRyxDQUFDLENBQUNYLHFCQUNWLDhEQUFDdEI7d0JBRUNrQyxTQUFTO3dCQUNUQyxhQUFhLENBQUNDLElBQ1pBLEVBQUVDLFlBQVksQ0FBQ0MsT0FBTyxDQUFDLFFBQVFDLEtBQUtDLFNBQVMsQ0FBQ2xCO3dCQUVoRHJCLFdBQVU7a0NBRVRxQixLQUFLVCxJQUFJO3VCQVBMUyxLQUFLVixFQUFFOzs7Ozs7Ozs7OzBCQWFsQiw4REFBQ1o7Z0JBQUlDLFdBQVU7MEJBQ1o7b0JBQUM7b0JBQVk7aUJBQWUsQ0FBQ2dDLEdBQUcsQ0FBQyxDQUFDWix5QkFDakMsOERBQUNyQjt3QkFFQ3lDLFlBQVksQ0FBQ0wsSUFBTUEsRUFBRU0sY0FBYzt3QkFDbkNDLFFBQVEsQ0FBQ1A7NEJBQ1AsTUFBTVEsT0FBT1IsRUFBRUMsWUFBWSxDQUFDUSxPQUFPLENBQUM7NEJBQ3BDLE1BQU12QixPQUFPaUIsS0FBS08sS0FBSyxDQUFDRjs0QkFDeEJ4QixXQUFXQyxVQUFVQzt3QkFDdkI7d0JBQ0FyQixXQUFVOzswQ0FFViw4REFBQzhDO2dDQUFHOUMsV0FBVTswQ0FDWG9COzs7Ozs7MENBRUgsOERBQUMyQjswQ0FDRWpDLFlBQVksQ0FBQ00sU0FBUyxDQUFDWSxHQUFHLENBQUMsQ0FBQ1gscUJBQzNCLDhEQUFDMkI7d0NBQWlCaEQsV0FBVTtrREFDekJxQixLQUFLVCxJQUFJO3VDQURIUyxLQUFLVixFQUFFOzs7Ozs7Ozs7Ozt1QkFkZlM7Ozs7Ozs7Ozs7MEJBd0JYLDhEQUFDckI7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDaUQ7d0JBQ0NqRCxXQUFVO3dCQUNWa0QsU0FBU3pCO2tDQUNWOzs7Ozs7a0NBR0QsOERBQUN3Qjt3QkFDQ2pELFdBQVU7d0JBQ1ZrRCxTQUFTcEI7a0NBQ1Y7Ozs7Ozs7Ozs7OztZQUlGYiwwQkFDQyw4REFBQ2Y7Z0JBQUVGLFdBQVU7MEJBQXFEaUI7Ozs7Ozs7Ozs7OztBQUkxRTtBQUVBLE1BQU1rQyxNQUFNO0lBQ1YscUJBQ0UsOERBQUNwRDtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Y7Ozs7OzBCQUNELDhEQUFDVzs7Ozs7Ozs7Ozs7QUFHUDtBQUVBLGlFQUFlMEMsR0FBR0EsRUFBQyIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL2xlc3NvbnMvbGV2ZWwxLzEuM1doYXRJc0FQcm9ncmFtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5cbmNvbnN0IFdoYXRJc0FQcm9ncmFtID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBiZy1ncmFkaWVudC10by1iIGZyb20tYmx1ZS0xMDAgdmlhLXB1cnBsZS01MCB0by1ncmVlbi0xMDAgcC02IHJvdW5kZWQtbGcgc2hhZG93LW1kXCI+XG4gICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC01eGwgZm9udC1ib2xkIG1iLTYgdGV4dC1wdXJwbGUtNzAwIHRleHQtY2VudGVyIGFuaW1hdGUtYm91bmNlXCI+XG4gICAgICAgIFdoYXQgaXMgYSBDb21wdXRlciBQcm9ncmFtP1xuICAgICAgPC9oMT5cbiAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgbWItNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICBBIDxzdHJvbmc+Y29tcHV0ZXIgcHJvZ3JhbTwvc3Ryb25nPiBpcyBsaWtlIGEgcmVjaXBlISBJdCZhcG9zO3MgYSBzZXQgb2ZcbiAgICAgICAgaW5zdHJ1Y3Rpb25zIHRoYXQgdGVsbHMgdGhlIGNvbXB1dGVyIGhvdyB0byBkbyBjb29sIHRoaW5ncyBsaWtlIHBsYXlpbmdcbiAgICAgICAgYSBnYW1lLCBkcmF3aW5nIHBpY3R1cmVzLCBvciBzb2x2aW5nIHByb2JsZW1zLlxuICAgICAgPC9wPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgIDxJbWFnZVxuICAgICAgICAgIHNyYz1cIi9sdmwxX2ltZy9VbnRpdGxlZCAoMykucG5nXCJcbiAgICAgICAgICBhbHQ9XCJBIGZ1biBjb21wdXRlciBpbGx1c3RyYXRpb25cIlxuICAgICAgICAgIHdpZHRoPXs0MDB9XG4gICAgICAgICAgaGVpZ2h0PXsyNTB9XG4gICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1sZyBzaGFkb3ctbGdcIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy0xMDAgcC00IGJvcmRlci1sLTQgYm9yZGVyLXllbGxvdy01MDAgbXQtNiByb3VuZGVkXCI+XG4gICAgICAgIDxwPlxuICAgICAgICAgIDxzdHJvbmc+RnVuIEZhY3Q6PC9zdHJvbmc+IExvbmcgYWdvLCBwZW9wbGUgaGFkIHRvIHdyaXRlIHByb2dyYW1zIHRvXG4gICAgICAgICAgcGVyZm9ybSBldmVuIHNpbXBsZSB0YXNrcyEgTm93LCB3ZSBoYXZlIGFwcHMgbGlrZXtcIiBcIn1cbiAgICAgICAgICA8ZW0+TWljcm9zb2Z0IFdvcmQ8L2VtPiBhbmQgZ2FtZXMgdGhhdCBtYWtlIHRoaW5ncyBlYXNpZXIgYW5kIG1vcmVcbiAgICAgICAgICBmdW4hXG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBtdC02IHRleHQtY2VudGVyXCI+XG4gICAgICAgIDxzdHJvbmc+UHJvZ3JhbXMgdnMuIENhbGN1bGF0b3JzOjwvc3Ryb25nPiBQcm9ncmFtcyBhcmUgbGlrZSBzbWFydFxuICAgICAgICBoZWxwZXJzLiBDYWxjdWxhdG9ycyBvbmx5IGRvIG1hdGgsIGJ1dCBwcm9ncmFtcyBjYW4gcGxheSBtdXNpYywgZHJhdyxcbiAgICAgICAgYW5kIGV2ZW4gcGxheSBjaGVzcyFcbiAgICAgIDwvcD5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tMTAwIHAtNCBib3JkZXItbC00IGJvcmRlci1ncmVlbi01MDAgbXQtNiByb3VuZGVkXCI+XG4gICAgICAgIDxwPlxuICAgICAgICAgIDxzdHJvbmc+QWN0aXZpdHk6PC9zdHJvbmc+IFRoaW5rIGFib3V0IHRoZSBwcm9ncmFtcyBvciBhcHBzIHlvdSB1c2VcbiAgICAgICAgICBkYWlseS4gV2hhdCBkbyB0aGV5IGRvIGZvciB5b3U/XG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuY29uc3QgRHJhZ0Ryb3BBY3Rpdml0eSA9ICgpID0+IHtcbiAgY29uc3QgaXRlbXMgPSBbXG4gICAgeyBpZDogMSwgbmFtZTogXCJNaWNyb3NvZnQgV29yZFwiLCBjb3JyZWN0Q2F0ZWdvcnk6IFwiUHJvZ3JhbXNcIiB9LFxuICAgIHsgaWQ6IDIsIG5hbWU6IFwiQ2FsY3VsYXRvclwiLCBjb3JyZWN0Q2F0ZWdvcnk6IFwiUHJvZ3JhbXNcIiB9LFxuICAgIHsgaWQ6IDMsIG5hbWU6IFwiTm90ZWJvb2sgKFBhcGVyKVwiLCBjb3JyZWN0Q2F0ZWdvcnk6IFwiTm90IFByb2dyYW1zXCIgfSxcbiAgICB7IGlkOiA0LCBuYW1lOiBcIkNoZXNzIEFwcFwiLCBjb3JyZWN0Q2F0ZWdvcnk6IFwiUHJvZ3JhbXNcIiB9LFxuICAgIHsgaWQ6IDUsIG5hbWU6IFwiUGVuXCIsIGNvcnJlY3RDYXRlZ29yeTogXCJOb3QgUHJvZ3JhbXNcIiB9LFxuICBdO1xuXG4gIGNvbnN0IFtkcm9wcGVkSXRlbXMsIHNldERyb3BwZWRJdGVtc10gPSB1c2VTdGF0ZSh7XG4gICAgUHJvZ3JhbXM6IFtdLFxuICAgIFwiTm90IFByb2dyYW1zXCI6IFtdLFxuICB9KTtcbiAgY29uc3QgW2ZlZWRiYWNrLCBzZXRGZWVkYmFja10gPSB1c2VTdGF0ZShcIlwiKTtcblxuICBjb25zdCBoYW5kbGVEcm9wID0gKGNhdGVnb3J5LCBpdGVtKSA9PiB7XG4gICAgc2V0RHJvcHBlZEl0ZW1zKChwcmV2KSA9PiB7XG4gICAgICBpZiAocHJldltjYXRlZ29yeV0uc29tZSgoaSkgPT4gaS5pZCA9PT0gaXRlbS5pZCkpIHJldHVybiBwcmV2O1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBbY2F0ZWdvcnldOiBbLi4ucHJldltjYXRlZ29yeV0sIGl0ZW1dLFxuICAgICAgfTtcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBjaGVja0Fuc3dlcnMgPSAoKSA9PiB7XG4gICAgbGV0IGFsbENvcnJlY3QgPSB0cnVlO1xuICAgIGZvciAoY29uc3QgW2NhdGVnb3J5LCBpdGVtc0luQ2F0ZWdvcnldIG9mIE9iamVjdC5lbnRyaWVzKGRyb3BwZWRJdGVtcykpIHtcbiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBpdGVtc0luQ2F0ZWdvcnkpIHtcbiAgICAgICAgaWYgKGl0ZW0uY29ycmVjdENhdGVnb3J5ICE9PSBjYXRlZ29yeSkge1xuICAgICAgICAgIGFsbENvcnJlY3QgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICBzZXRGZWVkYmFjayhcbiAgICAgIGFsbENvcnJlY3RcbiAgICAgICAgPyBcIvCfjokgR3JlYXQgam9iISBBbGwgYW5zd2VycyBhcmUgY29ycmVjdCFcIlxuICAgICAgICA6IFwi4p2MIFNvbWUgYW5zd2VycyBhcmUgaW5jb3JyZWN0LiBUcnkgYWdhaW4hXCJcbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IHJlc2V0QWN0aXZpdHkgPSAoKSA9PiB7XG4gICAgc2V0RHJvcHBlZEl0ZW1zKHtcbiAgICAgIFByb2dyYW1zOiBbXSxcbiAgICAgIFwiTm90IFByb2dyYW1zXCI6IFtdLFxuICAgIH0pO1xuICAgIHNldEZlZWRiYWNrKFwiXCIpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYmctZ3JhZGllbnQtdG8tYiBmcm9tLXllbGxvdy0xMDAgdmlhLXdoaXRlIHRvLWJsdWUtMTAwIHJvdW5kZWQtbGcgc2hhZG93LW1kIG10LThcIj5cbiAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1wdXJwbGUtNzAwIHRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgRnVuIEFjdGl2aXR5OiBQcm9ncmFtcyBvciBOb3Q/XG4gICAgICA8L2gyPlxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWNlbnRlciBtYi02IHRleHQtYmxhY2tcIj5cbiAgICAgICAgRHJhZyBhbmQgZHJvcCB0aGUgaXRlbXMgaW50byB0aGUgY29ycmVjdCBjYXRlZ29yeTp7XCIgXCJ9XG4gICAgICAgIDxzdHJvbmc+UHJvZ3JhbXM8L3N0cm9uZz4gb3IgPHN0cm9uZz5Ob3QgUHJvZ3JhbXM8L3N0cm9uZz4uXG4gICAgICA8L3A+XG5cbiAgICAgIHsvKiBEcmFnZ2FibGUgSXRlbXMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTQgZmxleC13cmFwIGp1c3RpZnktY2VudGVyIHRleHQtYmxhY2tcIj5cbiAgICAgICAge2l0ZW1zLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGtleT17aXRlbS5pZH1cbiAgICAgICAgICAgIGRyYWdnYWJsZVxuICAgICAgICAgICAgb25EcmFnU3RhcnQ9eyhlKSA9PlxuICAgICAgICAgICAgICBlLmRhdGFUcmFuc2Zlci5zZXREYXRhKFwiaXRlbVwiLCBKU09OLnN0cmluZ2lmeShpdGVtKSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMyBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgc2hhZG93LW1kIGN1cnNvci1wb2ludGVyIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIERyb3AgWm9uZXMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggZ3JpZCBncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICB7W1wiUHJvZ3JhbXNcIiwgXCJOb3QgUHJvZ3JhbXNcIl0ubWFwKChjYXRlZ29yeSkgPT4gKFxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGtleT17Y2F0ZWdvcnl9XG4gICAgICAgICAgICBvbkRyYWdPdmVyPXsoZSkgPT4gZS5wcmV2ZW50RGVmYXVsdCgpfVxuICAgICAgICAgICAgb25Ecm9wPXsoZSkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBkYXRhID0gZS5kYXRhVHJhbnNmZXIuZ2V0RGF0YShcIml0ZW1cIik7XG4gICAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSBKU09OLnBhcnNlKGRhdGEpO1xuICAgICAgICAgICAgICBoYW5kbGVEcm9wKGNhdGVnb3J5LCBpdGVtKTtcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJwLTYgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMzAwIHJvdW5kZWQtbGcgbWluLWgtWzE1MHB4XSBzaGFkb3ctaW5uZXIgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXJcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wdXJwbGUtNjAwIG1iLTRcIj5cbiAgICAgICAgICAgICAge2NhdGVnb3J5fVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDx1bD5cbiAgICAgICAgICAgICAge2Ryb3BwZWRJdGVtc1tjYXRlZ29yeV0ubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxpIGtleT17aXRlbS5pZH0gY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEZlZWRiYWNrIGFuZCBCdXR0b25zICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IGZsZXgganVzdGlmeS1jZW50ZXIgZ2FwLTZcIj5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1wdXJwbGUtNjAwIHRleHQtd2hpdGUgZm9udC1ib2xkIHJvdW5kZWQtbGcgc2hhZG93LW1kIGhvdmVyOmJnLXB1cnBsZS03MDAgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICAgIG9uQ2xpY2s9e2NoZWNrQW5zd2Vyc31cbiAgICAgICAgPlxuICAgICAgICAgIENoZWNrIEFuc3dlcnNcbiAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBjbGFzc05hbWU9XCJweC02IHB5LTMgYmctZ3JheS02MDAgdGV4dC13aGl0ZSBmb250LWJvbGQgcm91bmRlZC1sZyBzaGFkb3ctbWQgaG92ZXI6YmctZ3JheS03MDAgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICAgIG9uQ2xpY2s9e3Jlc2V0QWN0aXZpdHl9XG4gICAgICAgID5cbiAgICAgICAgICBSZXNldFxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuICAgICAge2ZlZWRiYWNrICYmIChcbiAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNiB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1jZW50ZXIgdGV4dC1ibGFja1wiPntmZWVkYmFja308L3A+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuY29uc3QgQXBwID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwicC04IGJnLWdyYWRpZW50LXRvLWIgZnJvbS1ibHVlLTEwMCB2aWEtd2hpdGUgdG8tZ3JlZW4tMTAwXCI+XG4gICAgICA8V2hhdElzQVByb2dyYW0gLz5cbiAgICAgIDxEcmFnRHJvcEFjdGl2aXR5IC8+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBBcHA7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkltYWdlIiwiV2hhdElzQVByb2dyYW0iLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJzdHJvbmciLCJzcmMiLCJhbHQiLCJ3aWR0aCIsImhlaWdodCIsImVtIiwiRHJhZ0Ryb3BBY3Rpdml0eSIsIml0ZW1zIiwiaWQiLCJuYW1lIiwiY29ycmVjdENhdGVnb3J5IiwiZHJvcHBlZEl0ZW1zIiwic2V0RHJvcHBlZEl0ZW1zIiwiUHJvZ3JhbXMiLCJmZWVkYmFjayIsInNldEZlZWRiYWNrIiwiaGFuZGxlRHJvcCIsImNhdGVnb3J5IiwiaXRlbSIsInByZXYiLCJzb21lIiwiaSIsImNoZWNrQW5zd2VycyIsImFsbENvcnJlY3QiLCJpdGVtc0luQ2F0ZWdvcnkiLCJPYmplY3QiLCJlbnRyaWVzIiwicmVzZXRBY3Rpdml0eSIsImgyIiwibWFwIiwiZHJhZ2dhYmxlIiwib25EcmFnU3RhcnQiLCJlIiwiZGF0YVRyYW5zZmVyIiwic2V0RGF0YSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvbkRyYWdPdmVyIiwicHJldmVudERlZmF1bHQiLCJvbkRyb3AiLCJkYXRhIiwiZ2V0RGF0YSIsInBhcnNlIiwiaDMiLCJ1bCIsImxpIiwiYnV0dG9uIiwib25DbGljayIsIkFwcCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/1.3WhatIsAProgram.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/1.4TypesOfComputers.js":
/*!*********************************************************!*\
  !*** ./src/pages/lessons/level1/1.4TypesOfComputers.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst TypesOfComputers = ()=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const computerTypes = [\n        {\n            id: 1,\n            name: \"Desktop Computers\",\n            image: \"/lvl1_img/desktop.webp\"\n        },\n        {\n            id: 2,\n            name: \"Laptop Computers\",\n            image: \"/lvl1_img/laptop.webp\"\n        },\n        {\n            id: 3,\n            name: \"Tablet Computers\",\n            image: \"/lvl1_img/tablet.webp\"\n        },\n        {\n            id: 4,\n            name: \"Smartphones\",\n            image: \"/lvl1_img/phone.webp\"\n        }\n    ];\n    const purposes = [\n        {\n            id: \"a\",\n            label: \"Large-screen productivity\"\n        },\n        {\n            id: \"b\",\n            label: \"Portable computing\"\n        },\n        {\n            id: \"c\",\n            label: \"Mobile communication and gaming\"\n        },\n        {\n            id: \"d\",\n            label: \"Touchscreen convenience\"\n        }\n    ];\n    const correctMatches = {\n        a: 1,\n        b: 2,\n        c: 4,\n        d: 3\n    };\n    const handleDragStart = (id)=>{\n        setIsDragging(id);\n    };\n    const handleDrop = (purposeId)=>{\n        if (isDragging) {\n            setMatches((prev)=>({\n                    ...prev,\n                    [purposeId]: isDragging\n                }));\n            setIsDragging(null);\n        }\n    };\n    const checkAnswers = ()=>{\n        const results = Object.keys(correctMatches).map((purposeId)=>({\n                purpose: purposes.find((purpose)=>purpose.id === purposeId).label,\n                isCorrect: correctMatches[purposeId] === matches[purposeId]\n            }));\n        const allCorrect = results.every((result)=>result.isCorrect);\n        setFeedback({\n            message: allCorrect ? \"🎉 Great job! All matches are correct!\" : \"❌ Some matches are incorrect. Try again!\",\n            results,\n            allCorrect\n        });\n    };\n    const retry = ()=>{\n        setMatches({});\n        setFeedback(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 bg-gradient-to-b from-blue-50 via-white to-green-100 p-6 rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700 text-center\",\n                children: \"Different Types of Computers\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-6\",\n                children: \"Computers come in many shapes and sizes, each designed for specific tasks. Let’s learn about them and match them to their purposes!\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex justify-center items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: \"/lvl1_img/Untitled (4).png\",\n                    alt: \"A fun computer illustration\",\n                    width: 500,\n                    height: 300,\n                    className: \"rounded-lg shadow-md\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-10 grid grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold mb-4 text-center\",\n                                children: \"Computer Types\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 flex flex-col items-center\",\n                                children: computerTypes.map((type)=>!Object.values(matches).includes(type.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: ()=>handleDragStart(type.id),\n                                        className: \"flex flex-col items-center p-4 border border-gray-300 bg-white rounded-md shadow-md hover:shadow-lg cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: type.image,\n                                                alt: type.name,\n                                                className: \"w-32 h-32 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                                lineNumber: 99,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center font-semibold\",\n                                                children: type.name\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                                lineNumber: 104,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, type.id, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                        lineNumber: 93,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold mb-4 text-center\",\n                                children: \"Computer Purposes\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 flex flex-col items-center\",\n                                children: purposes.map((purpose)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDrop: ()=>handleDrop(purpose.id),\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        className: \"flex flex-col items-center justify-center w-36 h-36 border-2 border-dashed border-blue-500 rounded-md bg-blue-50 shadow-inner\",\n                                        children: matches[purpose.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: computerTypes.find((type)=>type.id === matches[purpose.id]).image,\n                                                alt: \"Matched Computer\",\n                                                className: \"w-28 h-28 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                                lineNumber: 126,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                            lineNumber: 125,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-700 font-semibold text-center\",\n                                            children: purpose.label\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, purpose.id, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: checkAnswers,\n                        className: \"px-6 py-3 bg-purple-600 text-white font-bold rounded-lg shadow-md hover:bg-purple-700 transition-all\",\n                        children: \"Check Answers\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: retry,\n                        className: \"ml-4 px-6 py-3 bg-gray-600 text-white font-bold rounded-lg shadow-md hover:bg-gray-700 transition-all\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-8 p-4 rounded-lg shadow-md ${feedback.allCorrect ? \"bg-green-100\" : \"bg-yellow-100\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: `text-lg font-bold text-center ${feedback.allCorrect ? \"text-green-700\" : \"text-yellow-700\"}`,\n                        children: feedback.message\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined),\n                    !feedback.allCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc pl-6 mt-4\",\n                        children: feedback.results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: `${result.isCorrect ? \"text-green-700\" : \"text-red-700\"}`,\n                                children: [\n                                    result.purpose,\n                                    \":\",\n                                    \" \",\n                                    result.isCorrect ? \"Correct!\" : \"Incorrect\"\n                                ]\n                            }, index, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 179,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 177,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.4TypesOfComputers.js\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TypesOfComputers);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/1.4TypesOfComputers.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/1.5HardwareAndSoftware.js":
/*!************************************************************!*\
  !*** ./src/pages/lessons/level1/1.5HardwareAndSoftware.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst HardwareAndSoftware = ()=>{\n    const items = [\n        {\n            id: 1,\n            name: \"Mouse\",\n            type: \"Hardware\",\n            image: \"/lvl1_img/download.jpeg\"\n        },\n        {\n            id: 2,\n            name: \"Keyboard\",\n            type: \"Hardware\",\n            image: \"/lvl1_img/a-children-s-storybook-illustration-of-a_TWRewv6OSx2ZXmbs_7GdPQ_ZjKDda0IR8mrk4f58u1Wfw.jpeg\"\n        },\n        {\n            id: 3,\n            name: \"Web Browser\",\n            type: \"Software\",\n            image: \"/lvl1_img/download (2).jpeg\"\n        },\n        {\n            id: 4,\n            name: \"Video Game\",\n            type: \"Software\",\n            image: \"/tetris.png\"\n        }\n    ];\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [completed, setCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAnswer = (answer)=>{\n        const currentItem = items[currentItemIndex];\n        const isCorrect = answer === currentItem.type;\n        if (isCorrect) {\n            setScore((prevScore)=>prevScore + 1);\n            setFeedback(\"🎉 Correct! Great job!\");\n        } else {\n            setFeedback(\"❌ Oops, that's not correct. Try again!\");\n        }\n        setTimeout(()=>{\n            if (currentItemIndex + 1 < items.length) {\n                setCurrentItemIndex((prevIndex)=>prevIndex + 1);\n                setFeedback(null);\n            } else {\n                setCompleted(true);\n            }\n        }, 1000);\n    };\n    const resetQuiz = ()=>{\n        setCurrentItemIndex(0);\n        setScore(0);\n        setFeedback(null);\n        setCompleted(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 p-6 bg-gradient-to-b from-blue-50 via-white to-green-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-6 text-purple-700 text-center\",\n                children: \"Hardware vs. Software\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: \"/lvl1_img/a-children-s-storybook-illustration-of-a_J3vVFxGhRDS1Kmx2vm5HWw_0yQljxkMRMGI2wW4RGanxA.jpeg\" // Replace with your image path\n                    ,\n                    alt: \"Operating System Illustration\",\n                    width: 500,\n                    height: 300,\n                    className: \"rounded-lg shadow-md\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                    lineNumber: 51,\n                    columnNumber: 15\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 50,\n                columnNumber: 10\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4 text-center\",\n                children: [\n                    \"Computers are made up of \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"hardware\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 60,\n                        columnNumber: 34\n                    }, undefined),\n                    \" and \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"software\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 60,\n                        columnNumber: 64\n                    }, undefined),\n                    \". Let's learn the difference!\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 mb-6 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Hardware:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined),\n                            \" These are the physical parts of a computer that you can see and touch, like a mouse or keyboard.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Software:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            \" These are the programs that make the computer do cool stuff, like games or web browsers!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            !completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: items[currentItemIndex].image,\n                            alt: items[currentItemIndex].name,\n                            className: \"w-48 h-48 border border-gray-300 rounded-lg shadow-md\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: items[currentItemIndex].name\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAnswer(\"Hardware\"),\n                                className: \"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                                children: \"Hardware\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAnswer(\"Software\"),\n                                className: \"px-6 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600\",\n                                children: \"Software\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined),\n                    feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: `mt-4 text-lg font-bold ${feedback.includes(\"Correct\") ? \"text-green-600\" : \"text-red-600\"}`,\n                        children: feedback\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Quiz Completed!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg mb-4\",\n                        children: [\n                            \"Your Score: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: [\n                                    score,\n                                    \"/\",\n                                    items.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 111,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: resetQuiz,\n                        className: \"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600\",\n                        children: \"Retry Quiz\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HardwareAndSoftware);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/1.5HardwareAndSoftware.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/1.6OperatingSystem.js":
/*!********************************************************!*\
  !*** ./src/pages/lessons/level1/1.6OperatingSystem.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst OperatingSystem = ()=>{\n    const [quizAnswer, setQuizAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [quizFeedback, setQuizFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [dragFeedback, setDragFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const correctAnswer = \"Windows\";\n    const options = [\n        \"Windows\",\n        \"Google Chrome\",\n        \"Microsoft Word\",\n        \"Safari\"\n    ];\n    const correctMatches = {\n        Desktop: \"Windows\",\n        Mobile: \"Android\"\n    };\n    const handleQuizSubmit = ()=>{\n        if (quizAnswer === correctAnswer) {\n            setQuizFeedback(\"🎉 Correct! Windows is an operating system.\");\n        } else {\n            setQuizFeedback(\"❌ Incorrect. Try again!\");\n        }\n    };\n    const handleDragStart = (item)=>{\n        setDraggedItem(item);\n    };\n    const handleDrop = (target)=>{\n        setMatches((prev)=>({\n                ...prev,\n                [target]: draggedItem\n            }));\n        if (correctMatches[target] === draggedItem) {\n            setDragFeedback(`🎉 Correct! ${draggedItem} is the right match for ${target}.`);\n        } else {\n            setDragFeedback(`❌ Incorrect. ${draggedItem} does not match with ${target}.`);\n        }\n        setDraggedItem(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 p-6 bg-gradient-to-b from-blue-50 via-white to-green-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-6 text-purple-700 text-center\",\n                children: \"What is an Operating System?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: \"/lvl1_img/Untitled (6).png\" // Replace with your image path\n                    ,\n                    alt: \"Operating System Illustration\",\n                    width: 500,\n                    height: 300,\n                    className: \"rounded-lg shadow-md\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4 text-center\",\n                children: [\n                    \"An \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Operating System (OS)\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 59,\n                        columnNumber: 12\n                    }, undefined),\n                    \" is like the boss of a computer. It tells all the parts of the computer how to work together. Without an OS, your computer wouldn’t know how to show your games, apps, or even turn on properly!\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-100 p-4 border-l-4 border-blue-500 mb-4 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Example:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Imagine you have a robot that can dance, sing, and clean. The operating system is like the robot’s brain—it gives instructions so everything works smoothly.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4\",\n                children: \"There are many types of operating systems:\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"list-disc pl-6 text-lg mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Windows:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined),\n                            \" A popular OS for laptops and desktop computers.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"MacOS:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined),\n                            \" The OS used by Apple computers.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Android:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Used in most smartphones and tablets.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"iOS:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            \" The OS for iPhones and iPads.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 mb-6 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Fun Fact:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Without an operating system, your computer would be like a car without a driver—it has all the parts but doesn’t know how to move or what to do!\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 p-4 border-l-4 border-green-500 mb-6 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Activity 1:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Which of the following is an operating system?\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"radio\",\n                                            name: \"quiz\",\n                                            value: option,\n                                            onChange: (e)=>setQuizAnswer(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: option\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleQuizSubmit,\n                        className: \"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                        children: \"Submit Answer\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    quizFeedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg font-bold\",\n                        children: quizFeedback\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 116,\n                        columnNumber: 26\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl font-semibold\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Activity 2:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Match the operating systems to the devices they work best on.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row mt-6 space-x-6 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: ()=>handleDragStart(\"Windows\"),\n                                        className: \"w-32 h-16 bg-white border border-gray-400 shadow-md flex items-center justify-center cursor-pointer hover:bg-gray-50\",\n                                        children: \"Windows\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: ()=>handleDragStart(\"Android\"),\n                                        className: \"w-32 h-16 bg-white border border-gray-400 shadow-md flex items-center justify-center cursor-pointer hover:bg-gray-50\",\n                                        children: \"Android\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDrop: ()=>handleDrop(\"Desktop\"),\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        className: \"w-32 h-16 bg-gray-100 border-2 border-dashed border-gray-400 flex items-center justify-center rounded-md\",\n                                        children: matches.Desktop || \"Desktop\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDrop: ()=>handleDrop(\"Mobile\"),\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        className: \"w-32 h-16 bg-gray-100 border-2 border-dashed border-gray-400 flex items-center justify-center rounded-md\",\n                                        children: matches.Mobile || \"Mobile\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    dragFeedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg font-bold\",\n                        children: dragFeedback\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 161,\n                        columnNumber: 26\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.6OperatingSystem.js\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OperatingSystem);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/1.6OperatingSystem.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/2.1WhatIsScratch.js":
/*!******************************************************!*\
  !*** ./src/pages/lessons/level1/2.1WhatIsScratch.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst LandingPage = ()=>{\n    const steps = [\n        {\n            id: 1,\n            title: \"What is Scratch?\",\n            link: \"/lessons/level1/step-1\"\n        },\n        {\n            id: 2,\n            title: \"Set Up Scratch\",\n            link: \"/lessons/level1/step-2\"\n        },\n        {\n            id: 3,\n            title: \"Create Your First Project\",\n            link: \"/lessons/level1/step-3\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-purple-600 text-white py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-center\",\n                        children: \"Getting Started with Scratch\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center mt-2\",\n                        children: \"Learn Scratch step by step and start creating amazing projects today!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-4xl mx-auto mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold mb-4\",\n                        children: \"Learn Step by Step\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-4\",\n                        children: steps.map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"p-4 bg-white rounded-lg shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: step.title\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                                        lineNumber: 25,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: step.link,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"mt-2 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                            children: [\n                                                \"Go to Step \",\n                                                step.id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                                            lineNumber: 27,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, step.id, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-100 mt-12 py-4 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"\\xa9 \",\n                        new Date().getFullYear(),\n                        \" Getting Started with Scratch. All Rights Reserved.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/2.1WhatIsScratch.js\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/2.1WhatIsScratch.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/3.1WhatIsIoT.js":
/*!**************************************************!*\
  !*** ./src/pages/lessons/level1/3.1WhatIsIoT.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IoTMatchingActivity = ()=>{\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [connections, setConnections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const devices = [\n        {\n            id: \"Smart Fridge\",\n            x: 100,\n            y: 80,\n            label: \"Smart Fridge\",\n            icon: \"🧊\"\n        },\n        {\n            id: \"Smart Bulb\",\n            x: 100,\n            y: 220,\n            label: \"Smart Bulb\",\n            icon: \"💡\"\n        },\n        {\n            id: \"Smart Watch\",\n            x: 100,\n            y: 360,\n            label: \"Smart Watch\",\n            icon: \"⌚\"\n        }\n    ];\n    const correctMatches = {\n        \"Smart Fridge\": \"Internet\",\n        \"Smart Bulb\": \"Internet\",\n        \"Smart Watch\": \"Internet\"\n    };\n    const internet = {\n        id: \"Internet\",\n        x: 550,\n        y: 220,\n        label: \"Internet\",\n        icon: \"☁️\"\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IoTMatchingActivity.useEffect\": ()=>{\n            drawAllConnections();\n        }\n    }[\"IoTMatchingActivity.useEffect\"], [\n        connections\n    ]);\n    const startDrag = (device)=>{\n        setDraggedItem(device);\n        setFeedback(\"\"); // Clear feedback while dragging\n    };\n    const handleDrop = (target)=>{\n        if (draggedItem) {\n            const isAlreadyConnected = connections.some((connection)=>connection.from.id === draggedItem.id);\n            if (isAlreadyConnected) {\n                setFeedback(`⚠️ ${draggedItem.label} is already connected!`);\n                setDraggedItem(null);\n                return;\n            }\n            const isCorrect = correctMatches[draggedItem.id] === target.id;\n            setConnections((prev)=>[\n                    ...prev,\n                    {\n                        from: draggedItem,\n                        to: target,\n                        isCorrect\n                    }\n                ]);\n            setFeedback(isCorrect ? `✅ Signal Sent! ${draggedItem.label} connected to the Internet Cloud!` : `❌ Oops! ${draggedItem.label} couldn't connect. Try again!`);\n            setDraggedItem(null); // Reset dragged item\n        }\n    };\n    const drawAllConnections = ()=>{\n        const canvas = canvasRef.current;\n        if (canvas) {\n            const ctx = canvas.getContext(\"2d\");\n            ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear previous lines\n            connections.forEach((connection)=>{\n                drawLine(connection.from, connection.to, connection.isCorrect, ctx);\n            });\n        }\n    };\n    const drawLine = (from, to, isCorrect, ctx)=>{\n        ctx.beginPath();\n        ctx.moveTo(from.x + 50, from.y + 40); // Start position (IoT device)\n        ctx.lineTo(to.x + 60, to.y + 40); // End position (Internet)\n        ctx.strokeStyle = isCorrect ? \"#4CAF50\" : \"#F44336\"; // Green for correct, red for incorrect\n        ctx.lineWidth = 2;\n        ctx.stroke();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center text-gray-700 bg-gradient-to-b from-blue-50 via-white to-green-50 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-6 bg-white text-purple-700\",\n                children: \"IoT Signal Matching\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 w-full max-w-4xl text-left\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"What is the Internet of Things (IoT)?\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"The Internet of Things, or IoT, is like a magic network that connects everyday objects to the internet!\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Imagine if your toys, toothbrush, or even your fridge could talk to each other and to you. With IoT, this is possible because tiny computers called sensors are put into everyday items. These sensors can collect information, like how often you brush your teeth, and send that data to other devices or even your smartphone.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/lvl1_img/Untitled (7).png\",\n                        alt: \"IoT illustration\",\n                        className: \"w-full mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Fun Fact:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined),\n                            ' Thanks to IoT, your home could become a \"smart home\" where lights turn off automatically when you leave the room, or your fridge could remind you when you\\'re out of milk! How cool is that?'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 w-full max-w-4xl text-center bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"How to Play\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: 'Drag and drop each IoT device (e.g., Smart Fridge, Smart Bulb) to the Internet Cloud to connect them. Watch the lines appear between the devices and the cloud! Each correct connection will be marked as \"Correct\" in the history below. Can you connect all the devices?'\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative p-8 rounded-lg w-full max-w-4xl h-96\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef,\n                        width: 700,\n                        height: 400,\n                        className: \"absolute top-0 left-0 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-24 bg-white rounded-full shadow-md flex flex-col items-center justify-center cursor-pointer border-4 border-yellow-400\",\n                            style: {\n                                position: \"absolute\",\n                                top: device.y,\n                                left: device.x\n                            },\n                            draggable: true,\n                            onDragStart: ()=>startDrag(device),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-3xl\",\n                                    children: device.icon\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-xs font-bold text-gray-800\",\n                                    children: device.label\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, device.id, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-48 h-48 bg-white rounded-full shadow-md border-4 border-blue-400 flex flex-col items-center justify-center cursor-pointer\",\n                        style: {\n                            position: \"absolute\",\n                            top: internet.y,\n                            left: internet.x\n                        },\n                        onDrop: ()=>handleDrop(internet),\n                        onDragOver: (e)=>e.preventDefault(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-5xl\",\n                                children: internet.icon\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm font-bold text-gray-800\",\n                                children: internet.label\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-4 px-4 py-2 rounded-lg shadow-md w-full max-w-2xl text-center ${feedback.startsWith(\"✅\") ? \"bg-green-100 text-green-700\" : feedback.startsWith(\"⚠️\") ? \"bg-yellow-100 text-yellow-700\" : \"bg-red-100 text-red-700\"}`,\n                children: feedback\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 w-full max-w-4xl text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Connections\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    connections.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-2\",\n                        children: connections.map((connection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: `font-bold ${connection.isCorrect ? \"text-green-700\" : \"text-red-700\"}`,\n                                children: [\n                                    connection.from.label,\n                                    \" → \",\n                                    connection.to.label,\n                                    \" (\",\n                                    connection.isCorrect ? \"Correct\" : \"Wrong\",\n                                    \").\"\n                                ]\n                            }, index, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-500\",\n                        children: \"No connections made yet.\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.1WhatIsIoT.js\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IoTMatchingActivity);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/3.1WhatIsIoT.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/3.2HowIoTWorks.js":
/*!****************************************************!*\
  !*** ./src/pages/lessons/level1/3.2HowIoTWorks.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IoTHowItWorksActivity = ()=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const items = [\n        {\n            id: \"Smart Devices\",\n            label: \"Smart Devices\",\n            icon: \"📱\"\n        },\n        {\n            id: \"IoT Applications\",\n            label: \"IoT Applications\",\n            icon: \"🧠\"\n        },\n        {\n            id: \"Graphical User Interface\",\n            label: \"Graphical User Interface\",\n            icon: \"💻\"\n        }\n    ];\n    const descriptions = [\n        {\n            id: \"Smart Devices\",\n            label: \"Everyday items like your TV, toys, or even cars are equipped with sensors to collect data.\"\n        },\n        {\n            id: \"IoT Applications\",\n            label: \"Applications analyze the data collected and decide what action to take.\"\n        },\n        {\n            id: \"Graphical User Interface\",\n            label: \"You control everything using an app on your phone or computer.\"\n        }\n    ];\n    const correctMatches = {\n        \"Smart Devices\": \"Smart Devices\",\n        \"IoT Applications\": \"IoT Applications\",\n        \"Graphical User Interface\": \"Graphical User Interface\"\n    };\n    const handleDragStart = (id)=>{\n        setFeedback(\"\"); // Clear feedback while dragging\n        setMatches((prev)=>({\n                ...prev,\n                dragging: id\n            }));\n    };\n    const handleDrop = (descriptionId)=>{\n        const draggingId = matches.dragging;\n        if (draggingId) {\n            setMatches((prev)=>({\n                    ...prev,\n                    [descriptionId]: draggingId,\n                    dragging: null\n                }));\n        }\n    };\n    const checkAnswers = ()=>{\n        const results = Object.keys(correctMatches).map((descriptionId)=>({\n                description: descriptionId,\n                isCorrect: correctMatches[descriptionId] === matches[descriptionId]\n            }));\n        const allCorrect = results.every((result)=>result.isCorrect);\n        setFeedback(allCorrect ? \"🎉 Great job! All matches are correct!\" : \"❌ Some matches are incorrect. Try again!\");\n    };\n    const resetActivity = ()=>{\n        setMatches({});\n        setFeedback(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center text-gray-700 bg-gradient-to-b from-blue-50 via-white to-green-50 p-6 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700\",\n                children: \"How IoT Works\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl text-center mb-4\",\n                children: \"IoT works by connecting devices that communicate with each other over the internet. Let’s explore its components:\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"list-disc pl-6 mb-6 text-lg text-left max-w-4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Smart Devices:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Everyday items like TVs, toys, or cars with sensors that collect data.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"IoT Applications:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Analyze data and take actions like turning on lights automatically.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Graphical User Interface:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Lets you control devices using apps on your phone or computer.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Example:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Your smart garden can water plants when it detects they are dry. Amazing, right?\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-8 w-full max-w-5xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-4 text-blue-600\",\n                                children: \"Components\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: ()=>handleDragStart(item.id),\n                                        className: \"flex items-center justify-center w-56 h-20 bg-white border-2 border-gray-300 rounded-md shadow-sm cursor-pointer hover:shadow-lg transition\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl mr-2\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-4 text-green-600\",\n                                children: \"Descriptions\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: descriptions.map((description)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDrop: ()=>handleDrop(description.id),\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        className: \"w-full h-24 bg-gray-50 border-2 border-dashed border-blue-500 rounded-md p-2 flex items-center justify-center hover:bg-blue-50 transition\",\n                                        children: matches[description.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-green-100 rounded-md flex items-center justify-center text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: matches[description.id]\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                                lineNumber: 129,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-center\",\n                                            children: description.label\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, description.id, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 flex space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: checkAnswers,\n                        className: \"px-6 py-3 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600 transition\",\n                        children: \"Check Answers\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: resetActivity,\n                        className: \"px-6 py-3 bg-red-500 text-white rounded-lg shadow-md hover:bg-red-600 transition\",\n                        children: \"Reset\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-6 px-4 py-2 rounded-lg shadow-md ${feedback.includes(\"Great\") ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg font-bold\",\n                    children: feedback\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                    lineNumber: 165,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.2HowIoTWorks.js\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IoTHowItWorksActivity);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/3.2HowIoTWorks.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/3.3IoTExamples.js":
/*!****************************************************!*\
  !*** ./src/pages/lessons/level1/3.3IoTExamples.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SmartCityActivity = ()=>{\n    const [cityZones, setCityZones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        traffic: [],\n        parking: [],\n        environment: []\n    });\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const devices = [\n        {\n            id: \"Smart Traffic Light\",\n            label: \"Smart Traffic Light 🚦\",\n            correctZone: \"traffic\"\n        },\n        {\n            id: \"Parking Sensor\",\n            label: \"Parking Sensor 🅿️\",\n            correctZone: \"parking\"\n        },\n        {\n            id: \"Air Quality Monitor\",\n            label: \"Air Quality Monitor 🌫️\",\n            correctZone: \"environment\"\n        },\n        {\n            id: \"Smart Waste Bin\",\n            label: \"Smart Waste Bin 🗑️\",\n            correctZone: \"environment\"\n        },\n        {\n            id: \"Smart Streetlight\",\n            label: \"Smart Streetlight 💡\",\n            correctZone: \"traffic\"\n        }\n    ];\n    const handleDrop = (zone, device)=>{\n        if (device.correctZone === zone) {\n            setCityZones((prev)=>({\n                    ...prev,\n                    [zone]: [\n                        ...prev[zone],\n                        device\n                    ]\n                }));\n            setFeedback(`✅ Correct! ${device.label} belongs in the ${zone} zone.`);\n        } else {\n            setFeedback(`❌ Oops! ${device.label} doesn’t belong in the ${zone} zone. Try again!`);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center min-h-screen bg-gradient-to-b from-blue-50 via-green-50 to-yellow-50 p-6 text-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold text-blue-700 mb-6\",\n                children: \"Build Your Smart City\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: \"A smart city uses IoT devices to improve the quality of life by managing resources and services efficiently.\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg mt-2\",\n                        children: \"Drag and drop IoT devices into the correct city zones below. You'll receive feedback for each action. Can you complete your smart city?\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8 items-start w-full max-w-6xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-1/3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-4 text-green-600\",\n                                children: \"IoT Devices\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>e.dataTransfer.setData(\"device\", JSON.stringify(device)),\n                                        className: \"p-4 bg-white border border-gray-300 rounded-lg shadow-lg hover:shadow-xl cursor-pointer transition\",\n                                        children: device.label\n                                    }, device.id, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-2/3 grid grid-cols-1 lg:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold col-span-3 text-blue-700 mb-4 text-center\",\n                                children: \"City Zones\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onDrop: (e)=>{\n                                    const device = JSON.parse(e.dataTransfer.getData(\"device\"));\n                                    handleDrop(\"traffic\", device);\n                                },\n                                onDragOver: (e)=>e.preventDefault(),\n                                className: \"h-36 bg-blue-100 border-2 border-dashed border-blue-500 rounded-lg flex flex-col items-center justify-center transition hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-blue-700\",\n                                        children: \"Traffic Zone\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    cityZones.traffic.length > 0 && cityZones.traffic.map((device, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1\",\n                                            children: device.label\n                                        }, index, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onDrop: (e)=>{\n                                    const device = JSON.parse(e.dataTransfer.getData(\"device\"));\n                                    handleDrop(\"parking\", device);\n                                },\n                                onDragOver: (e)=>e.preventDefault(),\n                                className: \"h-36 bg-green-100 border-2 border-dashed border-green-500 rounded-lg flex flex-col items-center justify-center transition hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-green-700\",\n                                        children: \"Parking Zone\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    cityZones.parking.length > 0 && cityZones.parking.map((device, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1\",\n                                            children: device.label\n                                        }, index, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onDrop: (e)=>{\n                                    const device = JSON.parse(e.dataTransfer.getData(\"device\"));\n                                    handleDrop(\"environment\", device);\n                                },\n                                onDragOver: (e)=>e.preventDefault(),\n                                className: \"h-36 bg-yellow-100 border-2 border-dashed border-yellow-500 rounded-lg flex flex-col items-center justify-center transition hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-yellow-700\",\n                                        children: \"Environment Zone\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    cityZones.environment.length > 0 && cityZones.environment.map((device, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1\",\n                                            children: device.label\n                                        }, index, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-6 p-4 rounded-lg shadow-lg text-lg font-semibold transition ${feedback.startsWith(\"✅\") ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                children: feedback\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 w-full max-w-4xl bg-white p-6 border border-gray-300 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"City Summary\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc pl-6 space-y-2 text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Traffic Zone:\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    cityZones.traffic.length > 0 ? cityZones.traffic.map((device)=>device.label).join(\", \") : \"No devices added yet.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Parking Zone:\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    cityZones.parking.length > 0 ? cityZones.parking.map((device)=>device.label).join(\", \") : \"No devices added yet.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Environment Zone:\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    cityZones.environment.length > 0 ? cityZones.environment.map((device)=>device.label).join(\", \") : \"No devices added yet.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.3IoTExamples.js\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SmartCityActivity);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/3.3IoTExamples.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/3.4IoTBenefits.js":
/*!****************************************************!*\
  !*** ./src/pages/lessons/level1/3.4IoTBenefits.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IoTBenefits = ()=>{\n    const [unlockedBenefits, setUnlockedBenefits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const benefits = [\n        {\n            id: \"Innovation\",\n            label: \"Innovation 💡\",\n            description: \"Businesses can create smarter products based on what customers need.\"\n        },\n        {\n            id: \"Efficiency\",\n            label: \"Efficiency ⚙️\",\n            description: \"IoT helps make tasks faster and more productive, like keeping factories running smoothly.\"\n        },\n        {\n            id: \"Safety\",\n            label: \"Safety 🛡️\",\n            description: \"IoT devices can monitor workplaces and send alerts if something is wrong.\"\n        }\n    ];\n    const handleUnlock = (id)=>{\n        if (!unlockedBenefits.includes(id)) {\n            setUnlockedBenefits((prev)=>[\n                    ...prev,\n                    id\n                ]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 min-h-screen bg-gradient-to-b from-blue-50 via-white to-green-50 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700 text-center\",\n                children: \"Benefits of IoT\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-8\",\n                children: \"Discover how IoT makes life better! Click on the colorful icons to unlock and learn about each benefit.\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-3 gap-8 justify-center items-center mb-12\",\n                children: benefits.map((benefit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleUnlock(benefit.id),\n                        className: `p-6 w-full h-40 flex flex-col items-center justify-center cursor-pointer rounded-lg shadow-lg transform hover:scale-105 transition-transform ${unlockedBenefits.includes(benefit.id) ? \"bg-green-100 border-4 border-green-500\" : \"bg-gray-100 border-4 border-gray-300\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-5xl\",\n                                children: benefit.label.split(\" \")[1]\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 font-bold text-xl text-gray-800\",\n                                children: benefit.label.split(\" \")[0]\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, benefit.id, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-100 p-6 border-l-4 border-blue-500 rounded-lg shadow-lg mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-semibold mb-4 text-blue-700\",\n                        children: \"Unlocked Benefits\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, undefined),\n                    unlockedBenefits.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc pl-6 space-y-4 text-lg\",\n                        children: benefits.filter((benefit)=>unlockedBenefits.includes(benefit.id)).map((benefit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"text-gray-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: [\n                                            benefit.label.split(\" \")[0],\n                                            \":\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                                        lineNumber: 64,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \" \",\n                                    benefit.description\n                                ]\n                            }, benefit.id, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                                lineNumber: 63,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Click on the icons above to reveal the benefits of IoT!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-6 border-l-4 border-yellow-500 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold mb-4 text-yellow-700\",\n                        children: \"Creative Activity\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: \"Imagine you could invent an IoT device to help with your daily chores, like cleaning your room or finishing homework. What would it do? Write your idea on paper and share it with your friends or family!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/3.4IoTBenefits.js\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IoTBenefits);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/3.4IoTBenefits.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/4.1WhatIsComputerVision.js":
/*!*************************************************************!*\
  !*** ./src/pages/lessons/level1/4.1WhatIsComputerVision.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ComputerVisionActivity = ()=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isHappy, setIsHappy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Track computer's mood\n    const images = [\n        {\n            id: \"cat\",\n            label: \"Cat\",\n            src: \"/lvl1_img/cat.jpg\"\n        },\n        {\n            id: \"car\",\n            label: \"Car\",\n            src: \"/lvl1_img/car.jpeg\"\n        },\n        {\n            id: \"apple\",\n            label: \"Apple\",\n            src: \"/lvl1_img/apple.jpeg\"\n        }\n    ];\n    const correctMatches = {\n        cat: \"Cat\",\n        car: \"Car\",\n        apple: \"Apple\"\n    };\n    const handleDrop = (imageId, label)=>{\n        setMatches((prev)=>({\n                ...prev,\n                [imageId]: label\n            }));\n    };\n    const checkAnswers = ()=>{\n        const allCorrect = Object.keys(correctMatches).every((key)=>matches[key] === correctMatches[key]);\n        setIsHappy(allCorrect); // Change mood to happy if all answers are correct\n        setFeedback(allCorrect ? \"🎉 Great job! You've trained the computer successfully!\" : \"❌ Some matches are incorrect. Try again!\");\n    };\n    const resetActivity = ()=>{\n        setMatches({});\n        setFeedback(\"\");\n        setIsHappy(false); // Reset to confused mood\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 bg-white mt-6 p-4 rounded-lg mb-7\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-6 text-purple-700 \",\n                children: \"What is Computer Vision?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Computer Vision\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    ' is like teaching computers how to \"see\" the world, just like we do with our eyes! Computers use cameras and special programs to look at pictures and understand what they are. It\\'s like magic, but real!'\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-100 p-4 border-l-5 border-blue-500 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Fun Fact:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Did you know your phone's camera uses computer vision to recognize your face and unlock? It’s just like how you recognize your friends and family!\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: isHappy ? \"/lvl1_img/happy (1).jpeg\" : \"/lvl1_img/happy (2).jpeg\",\n                    alt: isHappy ? \"Happy Computer\" : \"Confused Computer\",\n                    className: \"w-40 h-40\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"Activity: Train the Computer\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4\",\n                children: \"Computers learn to recognize objects by being trained with examples. Drag the correct label to each image to help the computer understand what it is seeing. Let's teach the computer together!\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row items-start justify-center gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold mb-4\",\n                                children: \"Images\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: images.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        onDrop: (e)=>{\n                                            const label = e.dataTransfer.getData(\"label\");\n                                            handleDrop(image.id, label);\n                                        },\n                                        className: \"w-32 h-32 bg-gray-100 border-2 border-dashed border-gray-400 rounded-md flex items-center justify-center relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image.src,\n                                                alt: image.label,\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            matches[image.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 text-sm font-bold bg-green-200 p-1 rounded-md\",\n                                                children: matches[image.id]\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, image.id, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold mb-4\",\n                                children: \"Labels\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: Object.values(correctMatches).map((label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>e.dataTransfer.setData(\"label\", label),\n                                        className: \"w-32 p-2 bg-white border border-gray-300 rounded-md shadow-sm text-center cursor-pointer hover:shadow-lg\",\n                                        children: label\n                                    }, label, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: checkAnswers,\n                        className: \"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                        children: \"Check Answers\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: resetActivity,\n                        className: \"px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600\",\n                        children: \"Reset\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-6 p-4 rounded-lg shadow-md ${feedback.includes(\"Great\") ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg font-bold\",\n                    children: feedback\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-2\",\n                        children: \"What Did We Learn?\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: \"Computer vision works by learning from examples, just like we taught the computer in this activity. By labeling images, we help computers understand objects and make smart decisions. Imagine all the amazing things this technology can do!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComputerVisionActivity);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9sZXNzb25zL2xldmVsMS80LjFXaGF0SXNDb21wdXRlclZpc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBd0M7QUFFeEMsTUFBTUUseUJBQXlCO0lBQzdCLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHSCwrQ0FBUUEsQ0FBQyxDQUFDO0lBQ3hDLE1BQU0sQ0FBQ0ksVUFBVUMsWUFBWSxHQUFHTCwrQ0FBUUEsQ0FBQztJQUV6QyxNQUFNLENBQUNNLFNBQVNDLFdBQVcsR0FBR1AsK0NBQVFBLENBQUMsUUFBUSx3QkFBd0I7SUFFdkUsTUFBTVEsU0FBUztRQUNiO1lBQUVDLElBQUk7WUFBT0MsT0FBTztZQUFPQyxLQUFLO1FBQW9CO1FBQ3BEO1lBQUVGLElBQUk7WUFBT0MsT0FBTztZQUFPQyxLQUFLO1FBQXFCO1FBQ3JEO1lBQUVGLElBQUk7WUFBU0MsT0FBTztZQUFTQyxLQUFLO1FBQXVCO0tBQzVEO0lBRUQsTUFBTUMsaUJBQWlCO1FBQ3JCQyxLQUFLO1FBQ0xDLEtBQUs7UUFDTEMsT0FBTztJQUNUO0lBRUEsTUFBTUMsYUFBYSxDQUFDQyxTQUFTUDtRQUMzQlAsV0FBVyxDQUFDZSxPQUFVO2dCQUNwQixHQUFHQSxJQUFJO2dCQUNQLENBQUNELFFBQVEsRUFBRVA7WUFDYjtJQUNGO0lBRUEsTUFBTVMsZUFBZTtRQUNuQixNQUFNQyxhQUFhQyxPQUFPQyxJQUFJLENBQUNWLGdCQUFnQlcsS0FBSyxDQUNsRCxDQUFDQyxNQUFRdEIsT0FBTyxDQUFDc0IsSUFBSSxLQUFLWixjQUFjLENBQUNZLElBQUk7UUFHL0NqQixXQUFXYSxhQUFhLGtEQUFrRDtRQUUxRWYsWUFDRWUsYUFDSSw0REFDQTtJQUVSO0lBRUEsTUFBTUssZ0JBQWdCO1FBQ3BCdEIsV0FBVyxDQUFDO1FBQ1pFLFlBQVk7UUFDWkUsV0FBVyxRQUFRLHlCQUF5QjtJQUM5QztJQUVBLHFCQUNFLDhEQUFDbUI7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEyQzs7Ozs7OzBCQUN6RCw4REFBQ0U7Z0JBQUVGLFdBQVU7O2tDQUNYLDhEQUFDRztrQ0FBTzs7Ozs7O29CQUF3Qjs7Ozs7OzswQkFHbEMsOERBQUNKO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRTs7c0NBQ0MsOERBQUNDO3NDQUFPOzs7Ozs7d0JBQWtCOzs7Ozs7Ozs7Ozs7MEJBTTlCLDhEQUFDSjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0k7b0JBQ0NwQixLQUNFTCxVQUNJLDZCQUNBO29CQUVOMEIsS0FBSzFCLFVBQVUsbUJBQW1CO29CQUNsQ3FCLFdBQVU7Ozs7Ozs7Ozs7OzBCQUlkLDhEQUFDTTtnQkFBR04sV0FBVTswQkFBMEI7Ozs7OzswQkFDeEMsOERBQUNFO2dCQUFFRixXQUFVOzBCQUFlOzs7Ozs7MEJBTTVCLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEOzswQ0FDQyw4REFBQ087Z0NBQUdOLFdBQVU7MENBQThCOzs7Ozs7MENBQzVDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDWm5CLE9BQU8wQixHQUFHLENBQUMsQ0FBQ0Msc0JBQ1gsOERBQUNUO3dDQUVDVSxZQUFZLENBQUNDLElBQU1BLEVBQUVDLGNBQWM7d0NBQ25DQyxRQUFRLENBQUNGOzRDQUNQLE1BQU0zQixRQUFRMkIsRUFBRUcsWUFBWSxDQUFDQyxPQUFPLENBQUM7NENBQ3JDekIsV0FBV21CLE1BQU0xQixFQUFFLEVBQUVDO3dDQUN2Qjt3Q0FDQWlCLFdBQVU7OzBEQUVWLDhEQUFDSTtnREFBSXBCLEtBQUt3QixNQUFNeEIsR0FBRztnREFBRXFCLEtBQUtHLE1BQU16QixLQUFLO2dEQUFFaUIsV0FBVTs7Ozs7OzRDQUNoRHpCLE9BQU8sQ0FBQ2lDLE1BQU0xQixFQUFFLENBQUMsa0JBQ2hCLDhEQUFDaUI7Z0RBQUlDLFdBQVU7MERBQ1p6QixPQUFPLENBQUNpQyxNQUFNMUIsRUFBRSxDQUFDOzs7Ozs7O3VDQVhqQjBCLE1BQU0xQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2tDQW9CckIsOERBQUNpQjs7MENBQ0MsOERBQUNPO2dDQUFHTixXQUFVOzBDQUE4Qjs7Ozs7OzBDQUM1Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1pOLE9BQU9xQixNQUFNLENBQUM5QixnQkFBZ0JzQixHQUFHLENBQUMsQ0FBQ3hCLHNCQUNsQyw4REFBQ2dCO3dDQUVDaUIsU0FBUzt3Q0FDVEMsYUFBYSxDQUFDUCxJQUFNQSxFQUFFRyxZQUFZLENBQUNLLE9BQU8sQ0FBQyxTQUFTbkM7d0NBQ3BEaUIsV0FBVTtrREFFVGpCO3VDQUxJQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFhZiw4REFBQ2dCO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ21CO3dCQUNDQyxTQUFTNUI7d0JBQ1RRLFdBQVU7a0NBQ1g7Ozs7OztrQ0FHRCw4REFBQ21CO3dCQUNDQyxTQUFTdEI7d0JBQ1RFLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7OztZQU1GdkIsMEJBQ0MsOERBQUNzQjtnQkFDQ0MsV0FBVyxDQUFDLDhCQUE4QixFQUN4Q3ZCLFNBQVM0QyxRQUFRLENBQUMsV0FDZCxnQ0FDQSwyQkFDSjswQkFFRiw0RUFBQ25CO29CQUFFRixXQUFVOzhCQUFxQnZCOzs7Ozs7Ozs7OzswQkFLdEMsOERBQUNzQjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNNO3dCQUFHTixXQUFVO2tDQUF5Qjs7Ozs7O2tDQUN2Qyw4REFBQ0U7d0JBQUVGLFdBQVU7a0NBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU8vQjtBQUVBLGlFQUFlMUIsc0JBQXNCQSxFQUFDIiwic291cmNlcyI6WyIvbW50L2QvQWFtaXIvY29kZXNwcmludC9jb2RlX3NwcmludC9mcm9udGVuZC9zcmMvcGFnZXMvbGVzc29ucy9sZXZlbDEvNC4xV2hhdElzQ29tcHV0ZXJWaXNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5cbmNvbnN0IENvbXB1dGVyVmlzaW9uQWN0aXZpdHkgPSAoKSA9PiB7XG4gIGNvbnN0IFttYXRjaGVzLCBzZXRNYXRjaGVzXSA9IHVzZVN0YXRlKHt9KTtcbiAgY29uc3QgW2ZlZWRiYWNrLCBzZXRGZWVkYmFja10gPSB1c2VTdGF0ZShcIlwiKTtcblxuICBjb25zdCBbaXNIYXBweSwgc2V0SXNIYXBweV0gPSB1c2VTdGF0ZShmYWxzZSk7IC8vIFRyYWNrIGNvbXB1dGVyJ3MgbW9vZFxuXG4gIGNvbnN0IGltYWdlcyA9IFtcbiAgICB7IGlkOiBcImNhdFwiLCBsYWJlbDogXCJDYXRcIiwgc3JjOiBcIi9sdmwxX2ltZy9jYXQuanBnXCIgfSxcbiAgICB7IGlkOiBcImNhclwiLCBsYWJlbDogXCJDYXJcIiwgc3JjOiBcIi9sdmwxX2ltZy9jYXIuanBlZ1wiIH0sXG4gICAgeyBpZDogXCJhcHBsZVwiLCBsYWJlbDogXCJBcHBsZVwiLCBzcmM6IFwiL2x2bDFfaW1nL2FwcGxlLmpwZWdcIiB9LFxuICBdO1xuXG4gIGNvbnN0IGNvcnJlY3RNYXRjaGVzID0ge1xuICAgIGNhdDogXCJDYXRcIixcbiAgICBjYXI6IFwiQ2FyXCIsXG4gICAgYXBwbGU6IFwiQXBwbGVcIixcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wID0gKGltYWdlSWQsIGxhYmVsKSA9PiB7XG4gICAgc2V0TWF0Y2hlcygocHJldikgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbaW1hZ2VJZF06IGxhYmVsLFxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCBjaGVja0Fuc3dlcnMgPSAoKSA9PiB7XG4gICAgY29uc3QgYWxsQ29ycmVjdCA9IE9iamVjdC5rZXlzKGNvcnJlY3RNYXRjaGVzKS5ldmVyeShcbiAgICAgIChrZXkpID0+IG1hdGNoZXNba2V5XSA9PT0gY29ycmVjdE1hdGNoZXNba2V5XVxuICAgICk7XG5cbiAgICBzZXRJc0hhcHB5KGFsbENvcnJlY3QpOyAvLyBDaGFuZ2UgbW9vZCB0byBoYXBweSBpZiBhbGwgYW5zd2VycyBhcmUgY29ycmVjdFxuXG4gICAgc2V0RmVlZGJhY2soXG4gICAgICBhbGxDb3JyZWN0XG4gICAgICAgID8gXCLwn46JIEdyZWF0IGpvYiEgWW91J3ZlIHRyYWluZWQgdGhlIGNvbXB1dGVyIHN1Y2Nlc3NmdWxseSFcIlxuICAgICAgICA6IFwi4p2MIFNvbWUgbWF0Y2hlcyBhcmUgaW5jb3JyZWN0LiBUcnkgYWdhaW4hXCJcbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IHJlc2V0QWN0aXZpdHkgPSAoKSA9PiB7XG4gICAgc2V0TWF0Y2hlcyh7fSk7XG4gICAgc2V0RmVlZGJhY2soXCJcIik7XG4gICAgc2V0SXNIYXBweShmYWxzZSk7IC8vIFJlc2V0IHRvIGNvbmZ1c2VkIG1vb2RcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBiZy13aGl0ZSBtdC02IHAtNCByb3VuZGVkLWxnIG1iLTdcIj5cbiAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgbWItNiB0ZXh0LXB1cnBsZS03MDAgXCI+V2hhdCBpcyBDb21wdXRlciBWaXNpb24/PC9oMT5cbiAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgbWItNFwiPlxuICAgICAgICA8c3Ryb25nPkNvbXB1dGVyIFZpc2lvbjwvc3Ryb25nPiBpcyBsaWtlIHRlYWNoaW5nIGNvbXB1dGVycyBob3cgdG8gXCJzZWVcIiB0aGUgd29ybGQsIGp1c3QgbGlrZSB3ZSBkbyB3aXRoIG91ciBleWVzISBDb21wdXRlcnMgdXNlXG4gICAgICAgIGNhbWVyYXMgYW5kIHNwZWNpYWwgcHJvZ3JhbXMgdG8gbG9vayBhdCBwaWN0dXJlcyBhbmQgdW5kZXJzdGFuZCB3aGF0IHRoZXkgYXJlLiBJdCdzIGxpa2UgbWFnaWMsIGJ1dCByZWFsIVxuICAgICAgPC9wPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTEwMCBwLTQgYm9yZGVyLWwtNSBib3JkZXItYmx1ZS01MDAgbWItNFwiPlxuICAgICAgICA8cD5cbiAgICAgICAgICA8c3Ryb25nPkZ1biBGYWN0Ojwvc3Ryb25nPiBEaWQgeW91IGtub3cgeW91ciBwaG9uZSdzIGNhbWVyYSB1c2VzIGNvbXB1dGVyIHZpc2lvbiB0byByZWNvZ25pemUgeW91ciBmYWNlIGFuZCB1bmxvY2s/IEl04oCZcyBqdXN0IGxpa2VcbiAgICAgICAgICBob3cgeW91IHJlY29nbml6ZSB5b3VyIGZyaWVuZHMgYW5kIGZhbWlseSFcbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb21wdXRlciBJbWFnZSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtYi02XCI+XG4gICAgICAgIDxpbWdcbiAgICAgICAgICBzcmM9e1xuICAgICAgICAgICAgaXNIYXBweVxuICAgICAgICAgICAgICA/IFwiL2x2bDFfaW1nL2hhcHB5ICgxKS5qcGVnXCJcbiAgICAgICAgICAgICAgOiBcIi9sdmwxX2ltZy9oYXBweSAoMikuanBlZ1wiXG4gICAgICAgICAgfVxuICAgICAgICAgIGFsdD17aXNIYXBweSA/IFwiSGFwcHkgQ29tcHV0ZXJcIiA6IFwiQ29uZnVzZWQgQ29tcHV0ZXJcIn1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQwIGgtNDBcIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItNFwiPkFjdGl2aXR5OiBUcmFpbiB0aGUgQ29tcHV0ZXI8L2gyPlxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyBtYi00XCI+XG4gICAgICAgIENvbXB1dGVycyBsZWFybiB0byByZWNvZ25pemUgb2JqZWN0cyBieSBiZWluZyB0cmFpbmVkIHdpdGggZXhhbXBsZXMuIERyYWcgdGhlIGNvcnJlY3QgbGFiZWwgdG8gZWFjaCBpbWFnZSB0byBoZWxwIHRoZSBjb21wdXRlclxuICAgICAgICB1bmRlcnN0YW5kIHdoYXQgaXQgaXMgc2VlaW5nLiBMZXQncyB0ZWFjaCB0aGUgY29tcHV0ZXIgdG9nZXRoZXIhXG4gICAgICA8L3A+XG5cbiAgICAgIHsvKiBBY3Rpdml0eSBTZWN0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGl0ZW1zLXN0YXJ0IGp1c3RpZnktY2VudGVyIGdhcC02XCI+XG4gICAgICAgIHsvKiBJbWFnZXMgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPkltYWdlczwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIHtpbWFnZXMubWFwKChpbWFnZSkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAga2V5PXtpbWFnZS5pZH1cbiAgICAgICAgICAgICAgICBvbkRyYWdPdmVyPXsoZSkgPT4gZS5wcmV2ZW50RGVmYXVsdCgpfVxuICAgICAgICAgICAgICAgIG9uRHJvcD17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGxhYmVsID0gZS5kYXRhVHJhbnNmZXIuZ2V0RGF0YShcImxhYmVsXCIpO1xuICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcChpbWFnZS5pZCwgbGFiZWwpO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0zMiBoLTMyIGJnLWdyYXktMTAwIGJvcmRlci0yIGJvcmRlci1kYXNoZWQgYm9yZGVyLWdyYXktNDAwIHJvdW5kZWQtbWQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcmVsYXRpdmVcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGltZyBzcmM9e2ltYWdlLnNyY30gYWx0PXtpbWFnZS5sYWJlbH0gY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIiAvPlxuICAgICAgICAgICAgICAgIHttYXRjaGVzW2ltYWdlLmlkXSAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0wIHRleHQtc20gZm9udC1ib2xkIGJnLWdyZWVuLTIwMCBwLTEgcm91bmRlZC1tZFwiPlxuICAgICAgICAgICAgICAgICAgICB7bWF0Y2hlc1tpbWFnZS5pZF19XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTGFiZWxzICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIG1iLTRcIj5MYWJlbHM8L2gyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICB7T2JqZWN0LnZhbHVlcyhjb3JyZWN0TWF0Y2hlcykubWFwKChsYWJlbCkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAga2V5PXtsYWJlbH1cbiAgICAgICAgICAgICAgICBkcmFnZ2FibGVcbiAgICAgICAgICAgICAgICBvbkRyYWdTdGFydD17KGUpID0+IGUuZGF0YVRyYW5zZmVyLnNldERhdGEoXCJsYWJlbFwiLCBsYWJlbCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0zMiBwLTIgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSB0ZXh0LWNlbnRlciBjdXJzb3ItcG9pbnRlciBob3ZlcjpzaGFkb3ctbGdcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2xhYmVsfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQnV0dG9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBzcGFjZS14LTRcIj5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e2NoZWNrQW5zd2Vyc31cbiAgICAgICAgICBjbGFzc05hbWU9XCJweC02IHB5LTMgYmctYmx1ZS01MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNjAwXCJcbiAgICAgICAgPlxuICAgICAgICAgIENoZWNrIEFuc3dlcnNcbiAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXtyZXNldEFjdGl2aXR5fVxuICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1yZWQtNTAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1yZWQtNjAwXCJcbiAgICAgICAgPlxuICAgICAgICAgIFJlc2V0XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBGZWVkYmFjayAqL31cbiAgICAgIHtmZWVkYmFjayAmJiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9e2BtdC02IHAtNCByb3VuZGVkLWxnIHNoYWRvdy1tZCAke1xuICAgICAgICAgICAgZmVlZGJhY2suaW5jbHVkZXMoXCJHcmVhdFwiKVxuICAgICAgICAgICAgICA/IFwiYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tNzAwXCJcbiAgICAgICAgICAgICAgOiBcImJnLXJlZC0xMDAgdGV4dC1yZWQtNzAwXCJcbiAgICAgICAgICB9YH1cbiAgICAgICAgPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkXCI+e2ZlZWRiYWNrfTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogTGVhcm5pbmcgU2VjdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCBiZy15ZWxsb3ctMTAwIHAtNCBib3JkZXItbC00IGJvcmRlci15ZWxsb3ctNTAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIG1iLTJcIj5XaGF0IERpZCBXZSBMZWFybj88L2gyPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+XG4gICAgICAgICAgQ29tcHV0ZXIgdmlzaW9uIHdvcmtzIGJ5IGxlYXJuaW5nIGZyb20gZXhhbXBsZXMsIGp1c3QgbGlrZSB3ZSB0YXVnaHQgdGhlIGNvbXB1dGVyIGluIHRoaXMgYWN0aXZpdHkuIEJ5IGxhYmVsaW5nIGltYWdlcywgd2UgaGVscFxuICAgICAgICAgIGNvbXB1dGVycyB1bmRlcnN0YW5kIG9iamVjdHMgYW5kIG1ha2Ugc21hcnQgZGVjaXNpb25zLiBJbWFnaW5lIGFsbCB0aGUgYW1hemluZyB0aGluZ3MgdGhpcyB0ZWNobm9sb2d5IGNhbiBkbyFcbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDb21wdXRlclZpc2lvbkFjdGl2aXR5O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJDb21wdXRlclZpc2lvbkFjdGl2aXR5IiwibWF0Y2hlcyIsInNldE1hdGNoZXMiLCJmZWVkYmFjayIsInNldEZlZWRiYWNrIiwiaXNIYXBweSIsInNldElzSGFwcHkiLCJpbWFnZXMiLCJpZCIsImxhYmVsIiwic3JjIiwiY29ycmVjdE1hdGNoZXMiLCJjYXQiLCJjYXIiLCJhcHBsZSIsImhhbmRsZURyb3AiLCJpbWFnZUlkIiwicHJldiIsImNoZWNrQW5zd2VycyIsImFsbENvcnJlY3QiLCJPYmplY3QiLCJrZXlzIiwiZXZlcnkiLCJrZXkiLCJyZXNldEFjdGl2aXR5IiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwic3Ryb25nIiwiaW1nIiwiYWx0IiwiaDIiLCJtYXAiLCJpbWFnZSIsIm9uRHJhZ092ZXIiLCJlIiwicHJldmVudERlZmF1bHQiLCJvbkRyb3AiLCJkYXRhVHJhbnNmZXIiLCJnZXREYXRhIiwidmFsdWVzIiwiZHJhZ2dhYmxlIiwib25EcmFnU3RhcnQiLCJzZXREYXRhIiwiYnV0dG9uIiwib25DbGljayIsImluY2x1ZGVzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/4.1WhatIsComputerVision.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/4.2HowComputerVisionWorks.js":
/*!***************************************************************!*\
  !*** ./src/pages/lessons/level1/4.2HowComputerVisionWorks.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst HowComputerVisionWorks = ()=>{\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const steps = [\n        {\n            id: 1,\n            title: \"Seeing the World\",\n            description: \"Cameras and sensors capture images or videos, just like taking a picture with your phone!\",\n            action: \"Click to see the captured image!\",\n            image: \"/lvl1_img/Smart453ForFour_RVC_Installed.png\"\n        },\n        {\n            id: 2,\n            title: \"Understanding the Image\",\n            description: \"The computer breaks the image into tiny pieces (pixels) and analyzes them. These pixels are just tiny colored dots!\",\n            action: \"Click to see the pixelated version of the image!\",\n            image: \"/lvl1_img/download (1).png\"\n        },\n        {\n            id: 3,\n            title: \"Learning to Recognize\",\n            description: \"The computer compares this image with thousands of examples to understand what it is looking at. It's like studying for a test!\",\n            action: \"Click to see the computer's guess!\",\n            image: \"/lvl1_img/pxArt (1).png\"\n        },\n        {\n            id: 4,\n            title: \"Making Decisions\",\n            description: \"Finally, the computer identifies the object and decides what action to take, like recognizing a stop sign and stopping the car.\",\n            action: \"Click to see the computer's decision!\",\n            image: \"/lvl1_img/stop-stamp-7.png\"\n        }\n    ];\n    const handleNextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const handleReset = ()=>{\n        setCurrentStep(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-blue-50 via-white to-green-100 p-8 text-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-extrabold mb-6 text-center text-purple-700\",\n                children: \"How Does Computer Vision Work?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-8\",\n                children: 'Computer Vision works in several steps to \"see\" and understand the world. Let’s explore each step!'\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-blue-600 mb-4 text-center\",\n                        children: steps[currentStep].title\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 text-center mb-6\",\n                        children: steps[currentStep].description\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: steps[currentStep].image,\n                                alt: steps[currentStep].title,\n                                className: \"w-48 h-48 mb-6 border-2 border-gray-200 rounded-lg shadow-md\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleNextStep,\n                                className: `px-6 py-3 rounded-lg text-lg font-semibold ${currentStep === steps.length - 1 ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-blue-500 text-white hover:bg-blue-600\"}`,\n                                disabled: currentStep === steps.length - 1,\n                                children: steps[currentStep].action\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            currentStep === steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleReset,\n                    className: \"px-6 py-3 bg-red-500 text-white rounded-lg font-bold hover:bg-red-600\",\n                    children: \"Start Over\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12 bg-green-100 p-6 rounded-lg border-l-4 border-green-500 shadow-md max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-green-800 mb-2\",\n                        children: \"Real-Life Example\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-700\",\n                        children: \"In a self-driving car, computer vision can recognize a stop sign and tell the car to stop. It goes through all these steps in just seconds!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HowComputerVisionWorks);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/4.2HowComputerVisionWorks.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/4.3ComputerVisionApplications.js":
/*!*******************************************************************!*\
  !*** ./src/pages/lessons/level1/4.3ComputerVisionApplications.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst GuessTheVision = ()=>{\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const questions = [\n        {\n            id: 1,\n            image: \"/lvl1_img/download (1).png\",\n            question: \"What should the self-driving car do when it sees this?\",\n            options: [\n                \"Stop\",\n                \"Speed Up\",\n                \"Turn\"\n            ],\n            correct: \"Stop\"\n        },\n        {\n            id: 2,\n            image: \"/lvl1_img/caerra.jpeg\",\n            question: \"What is the traffic camera likely analyzing?\",\n            options: [\n                \"Weather\",\n                \"Traffic Flow\",\n                \"Birds\"\n            ],\n            correct: \"Traffic Flow\"\n        },\n        {\n            id: 3,\n            image: \"/lvl1_img/download (5).jpeg\",\n            question: \"What can computer vision help doctors do with this X-ray?\",\n            options: [\n                \"Diagnose Diseases\",\n                \"Take Photos\",\n                \"Print Reports\"\n            ],\n            correct: \"Diagnose Diseases\"\n        },\n        {\n            id: 4,\n            image: \"/lvl1_img/download (6).jpeg\",\n            question: \"What can computer vision do in a factory?\",\n            options: [\n                \"Check Product Quality\",\n                \"Print Instructions\",\n                \"Turn Off Machines\"\n            ],\n            correct: \"Check Product Quality\"\n        },\n        {\n            id: 5,\n            image: \"/lvl1_img/download (7).jpeg\",\n            question: \"What is computer vision likely doing in this store?\",\n            options: [\n                \"Tracking Shopper Behavior\",\n                \"Counting Birds\",\n                \"Watching TV\"\n            ],\n            correct: \"Tracking Shopper Behavior\"\n        }\n    ];\n    const handleAnswer = (option)=>{\n        if (option === questions[currentQuestion].correct) {\n            setFeedback(\"✅ Correct! Great job!\");\n        } else {\n            setFeedback(\"❌ Incorrect. Try again!\");\n        }\n    };\n    const nextQuestion = ()=>{\n        setFeedback(\"\");\n        setCurrentQuestion((prev)=>Math.min(prev + 1, questions.length - 1));\n    };\n    const resetActivity = ()=>{\n        setFeedback(\"\");\n        setCurrentQuestion(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-blue-50 via-white to-green-100 p-8 text-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-extrabold text-center mb-6 text-purple-700\",\n                children: \"Guess the Vision\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-6\",\n                children: \"Can you guess what the computer should do in these scenarios? Let’s find out!\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            questions[currentQuestion].image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: questions[currentQuestion].image,\n                                alt: \"Question Image\",\n                                className: \"w-48 h-48 mb-4 border border-gray-300 rounded-md shadow-md\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                children: questions[currentQuestion].question\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-4\",\n                        children: questions[currentQuestion].options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAnswer(option),\n                                className: \"w-full px-6 py-3 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600\",\n                                children: option\n                            }, index, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `max-w-4xl mx-auto mt-6 p-4 rounded-lg shadow-md ${feedback.startsWith(\"✅\") ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-lg font-bold\",\n                    children: feedback\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined),\n            currentQuestion < questions.length - 1 && feedback.startsWith(\"✅\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: nextQuestion,\n                    className: \"px-6 py-3 bg-green-500 text-white rounded-lg shadow-md hover:bg-green-600\",\n                    children: \"Next Question\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                    lineNumber: 117,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined),\n            currentQuestion === questions.length - 1 && feedback.startsWith(\"✅\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 max-w-4xl mx-auto bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-bold text-center\",\n                        children: \"\\uD83C\\uDF89 You've completed the activity! Great job!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetActivity,\n                            className: \"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                            children: \"Play Again\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GuessTheVision);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/4.3ComputerVisionApplications.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/5.1Summary.js":
/*!************************************************!*\
  !*** ./src/pages/lessons/level1/5.1Summary.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Summary = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartQuiz = ()=>{\n        router.push(\"/lessons/quiz\"); // Navigate to the Quiz page\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-blue-50 via-white to-purple-100 p-8 text-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-extrabold mb-6 text-purple-700 text-center\",\n                    children: \"Summary of Key Concepts\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg mb-6 text-center\",\n                    children: \"Here's a recap of what we've learned in this course:\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc pl-6 mb-6 text-lg space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Computers:\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Powerful machines that process data using input, memory, processing, and output.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Programs:\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Sets of instructions that tell computers what to do.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Internet of Things (IoT):\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Connects everyday objects to the internet, creating smart homes, cities, and workplaces.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Computer Vision:\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined),\n                                ' Enables computers to \"see\" and make decisions based on images or videos.'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Scratch:\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" A platform for creating fun, interactive games and animations using simple coding blocks.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-100 p-4 border-l-4 border-blue-500 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-center font-semibold\",\n                        children: \"Reflect on these topics to prepare for the quiz!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleStartQuiz,\n                        className: \"px-6 py-3 bg-purple-500 text-white rounded-lg shadow-md hover:bg-purple-600\",\n                        children: \"Take the Quiz\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.1Summary.js\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Summary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/5.1Summary.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/5.3CompletionMessage.js":
/*!**********************************************************!*\
  !*** ./src/pages/lessons/level1/5.3CompletionMessage.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst CompletionMessage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-blue-100 via-white to-green-100 p-8 text-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-3xl mx-auto bg-white shadow-lg rounded-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-5xl font-extrabold mb-6 text-purple-700 animate-pulse\",\n                    children: \"\\uD83C\\uDF89 Congratulations! \\uD83C\\uDF89\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg mb-6\",\n                    children: \"You’ve successfully completed the Level 1 course! Here’s what you’ve accomplished:\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc pl-6 mb-6 text-left text-lg space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Learned how computers work\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" and the basics of input, processing, and output.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Explored programming concepts\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" and created fun projects using Scratch.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Discovered IoT\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" and how devices communicate over the internet.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Understood Computer Vision\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined),\n                                ' and its role in making computers \"see.\"'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-semibold\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Creative Activity:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, undefined),\n                            \" Draw or design your own certificate of completion and celebrate your achievement!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg mb-6\",\n                    children: \"Keep practicing and learning to master new skills. The sky's the limit!\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/5.3CompletionMessage.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompletionMessage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9sZXNzb25zL2xldmVsMS81LjNDb21wbGV0aW9uTWVzc2FnZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFMUIsTUFBTUMsb0JBQW9CO0lBQ3hCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUdELFdBQVU7OEJBQTZEOzs7Ozs7OEJBRzNFLDhEQUFDRTtvQkFBRUYsV0FBVTs4QkFBZTs7Ozs7OzhCQUk1Qiw4REFBQ0c7b0JBQUdILFdBQVU7O3NDQUNaLDhEQUFDSTs7OENBQ0MsOERBQUNDOzhDQUFPOzs7Ozs7Z0NBQW1DOzs7Ozs7O3NDQUU3Qyw4REFBQ0Q7OzhDQUNDLDhEQUFDQzs4Q0FBTzs7Ozs7O2dDQUFzQzs7Ozs7OztzQ0FFaEQsOERBQUNEOzs4Q0FDQyw4REFBQ0M7OENBQU87Ozs7OztnQ0FBdUI7Ozs7Ozs7c0NBRWpDLDhEQUFDRDs7OENBQ0MsOERBQUNDOzhDQUFPOzs7Ozs7Z0NBQW1DOzs7Ozs7Ozs7Ozs7OzhCQUkvQyw4REFBQ047b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUFFRixXQUFVOzswQ0FDWCw4REFBQ0s7MENBQU87Ozs7Ozs0QkFBMkI7Ozs7Ozs7Ozs7Ozs4QkFJdkMsOERBQUNIO29CQUFFRixXQUFVOzhCQUFlOzs7Ozs7Ozs7Ozs7Ozs7OztBQVFwQztBQUVBLGlFQUFlRixpQkFBaUJBLEVBQUMiLCJzb3VyY2VzIjpbIi9tbnQvZC9BYW1pci9jb2Rlc3ByaW50L2NvZGVfc3ByaW50L2Zyb250ZW5kL3NyYy9wYWdlcy9sZXNzb25zL2xldmVsMS81LjNDb21wbGV0aW9uTWVzc2FnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5cbmNvbnN0IENvbXBsZXRpb25NZXNzYWdlID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1ibHVlLTEwMCB2aWEtd2hpdGUgdG8tZ3JlZW4tMTAwIHAtOCB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTN4bCBteC1hdXRvIGJnLXdoaXRlIHNoYWRvdy1sZyByb3VuZGVkLWxnIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC01eGwgZm9udC1leHRyYWJvbGQgbWItNiB0ZXh0LXB1cnBsZS03MDAgYW5pbWF0ZS1wdWxzZVwiPlxuICAgICAgICAgIPCfjokgQ29uZ3JhdHVsYXRpb25zISDwn46JXG4gICAgICAgIDwvaDE+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgbWItNlwiPlxuICAgICAgICAgIFlvdeKAmXZlIHN1Y2Nlc3NmdWxseSBjb21wbGV0ZWQgdGhlIExldmVsIDEgY291cnNlISBIZXJl4oCZcyB3aGF0IHlvdeKAmXZlIGFjY29tcGxpc2hlZDpcbiAgICAgICAgPC9wPlxuXG4gICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgcGwtNiBtYi02IHRleHQtbGVmdCB0ZXh0LWxnIHNwYWNlLXktM1wiPlxuICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgIDxzdHJvbmc+TGVhcm5lZCBob3cgY29tcHV0ZXJzIHdvcms8L3N0cm9uZz4gYW5kIHRoZSBiYXNpY3Mgb2YgaW5wdXQsIHByb2Nlc3NpbmcsIGFuZCBvdXRwdXQuXG4gICAgICAgICAgPC9saT5cbiAgICAgICAgICA8bGk+XG4gICAgICAgICAgICA8c3Ryb25nPkV4cGxvcmVkIHByb2dyYW1taW5nIGNvbmNlcHRzPC9zdHJvbmc+IGFuZCBjcmVhdGVkIGZ1biBwcm9qZWN0cyB1c2luZyBTY3JhdGNoLlxuICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgPGxpPlxuICAgICAgICAgICAgPHN0cm9uZz5EaXNjb3ZlcmVkIElvVDwvc3Ryb25nPiBhbmQgaG93IGRldmljZXMgY29tbXVuaWNhdGUgb3ZlciB0aGUgaW50ZXJuZXQuXG4gICAgICAgICAgPC9saT5cbiAgICAgICAgICA8bGk+XG4gICAgICAgICAgICA8c3Ryb25nPlVuZGVyc3Rvb2QgQ29tcHV0ZXIgVmlzaW9uPC9zdHJvbmc+IGFuZCBpdHMgcm9sZSBpbiBtYWtpbmcgY29tcHV0ZXJzIFwic2VlLlwiXG4gICAgICAgICAgPC9saT5cbiAgICAgICAgPC91bD5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy0xMDAgcC00IGJvcmRlci1sLTQgYm9yZGVyLXllbGxvdy01MDAgcm91bmRlZC1sZyBtYi02XCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZFwiPlxuICAgICAgICAgICAgPHN0cm9uZz5DcmVhdGl2ZSBBY3Rpdml0eTo8L3N0cm9uZz4gRHJhdyBvciBkZXNpZ24geW91ciBvd24gY2VydGlmaWNhdGUgb2YgY29tcGxldGlvbiBhbmQgY2VsZWJyYXRlIHlvdXIgYWNoaWV2ZW1lbnQhXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIG1iLTZcIj5cbiAgICAgICAgICBLZWVwIHByYWN0aWNpbmcgYW5kIGxlYXJuaW5nIHRvIG1hc3RlciBuZXcgc2tpbGxzLiBUaGUgc2t5J3MgdGhlIGxpbWl0IVxuICAgICAgICA8L3A+XG5cbiAgICAgICBcbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ29tcGxldGlvbk1lc3NhZ2U7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb21wbGV0aW9uTWVzc2FnZSIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsInVsIiwibGkiLCJzdHJvbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/5.3CompletionMessage.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "@splinetool/react-spline":
/*!*******************************************!*\
  !*** external "@splinetool/react-spline" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@splinetool/react-spline");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel-1&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel-1.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
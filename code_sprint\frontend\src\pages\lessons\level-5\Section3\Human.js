import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../../level-4/Section1/useTypingAnimation";
import styles from "../../level-4/Section1/useTypingAnimation";
import { Player } from "@lottiefiles/react-lottie-player";

const TheGoodTheBadAndTheElif = () => {
  const [code, setCode] = useState(`Say something: \n`);
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const outputRef = useRef(null);
  const [buttonClicked, setButtonClicked] = useState(false);
  const typedText = useTypingAnimation("HELLO, HUMAN!", 100);

  const [animatedCode, setAnimatedCode] = useState("");

  const codeBlockStyle = {
    backgroundColor: "#f7fafc", // Light gray background
    padding: "16px", // Padding for space around the code
    borderRadius: "8px", // Rounded corners
    color: "#2d3748", // Text color
    marginTop: "16px", // Top margin for spacing
    whiteSpace: "pre-wrap", // Preserve spaces and newlines
    wordWrap: "break-word", // Allow long words to break into the next line
    fontFamily: "monospace", // Monospace font for code
    fontSize: "16px", // Slightly larger font size for readability
  };

  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);

  const runCode = () => {
    try {
      const greeting = userInput.trim().toLowerCase();

      if (!greeting) {
        setOutput("Error: Please provide an input.");
        setShowOutput(true);
        setShowConfetti(false);
        return;
      }

      let confetti = false;

      if (greeting.includes("hello")) {
        setOutput("Hello, human! 👋");
        confetti = true; // Enable confetti if the output is "Hello, human! 👋"
      } else {
        setOutput("I don't understand, but I'm learning! 🤖");
        confetti = false; // No confetti for other outputs
      }

      scrollToOutput();
      setShowOutput(true);
      setShowConfetti(confetti); // Trigger confetti if applicable

      if (confetti) {
        // Stop confetti after 1 second
        setTimeout(() => setShowConfetti(false), 1000);
      }
    } catch (error) {
      setOutput("Error: An unexpected error occurred.");
      setShowOutput(true);
      setShowConfetti(false);
      scrollToOutput();
    }
  };

  const scrollToOutput = () => {
    setTimeout(() => {
      if (outputRef.current) {
        outputRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {
      const lines = newCode.split("\n");
      const prompt = "Say something: ";
      const inputWithoutPrompt = lines.slice(1).join("\n");
      setUserInput(inputWithoutPrompt);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #92b4a7, #81667a)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #92b4a7, #81667a)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <p className="text-lg text-gray-700 mb-6">
          Let's start by building a basic{" "}
          <code className="font-mono text-blue-500">NLP</code>, program that
          simply recognizes a greeting.{" "}
        </p>
        <hr className="my-6 border-t border-gray-300" />
        <p className="mt-6 text-lg font-bold text-black">INSTRUCTIONS</p>
        <ol className="list-decimal list-inside text-md pl-6 mt-2 text-gray-700">
          <li>
            <strong>
              Set up{" "}
              <a href="https://replit.com/" className="text-blue-500 underline">
                Replit
              </a>
            </strong>
            : Create a new Repl and name it{" "}
            <code className="font-mono text-blue-500">hello_nlp.py</code>.
          </li>
          <li>
            <strong>Write the code</strong>:
          </li>
        </ol>
        <pre style={codeBlockStyle}>
          {`greeting = input("Say something: ")

if "hello" in greeting.lower():
    print("Hello, human! 👋")
else:
    print("I don't understand, but I'm learning! 🤖")`}
        </pre>{" "}
        <p className="text-sm text-gray-700 mt-2">
          Run the code: Type in a greeting like{" "}
          <code className="font-mono text-blue-500">"Hello!"</code> and see how
          the program responds.
        </p>
        
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#d0e1b3",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code + userInput : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#e9f5db",
              color: "#d4a373",
              minHeight: "150px",
              borderRadius: "10px",
            }}
          />
        </div>{" "}
        <div>
          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "85%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#432818",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#CCD5AE",
                }}
              >
                Output:
              </h2>
              <pre
                style={{
                  fontSize: "1rem",
                  color: "#CCD5AE",
                  marginTop: "10px",
                }}
              >
                {output}
              </pre>
            </div>
          )}
          <div>
            <button
              onClick={() => {
                if (buttonClicked) {
                  setButtonClicked(false);
                  setUserInput("");
                  setOutput("");
                  setShowOutput(false);
                  setShowConfetti(false);
                  setAnimatedCode("");
                  setIsAnimationCompleted(false);
                } else {
                  runCode();
                  setButtonClicked(true);
                }
              }}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: buttonClicked ? "#ccd5ae" : "#432818",
                color: buttonClicked ? "#432818" : "#ccd5ae",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
                transition: "all 0.3s ease-in-out",
              }}
            >
              {buttonClicked ? "Retry 🔄" : "Run Code 🎉"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TheGoodTheBadAndTheElif;

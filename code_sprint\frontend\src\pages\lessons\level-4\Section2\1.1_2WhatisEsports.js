import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Howitallbegan = () => {
  const typedText = useTypingAnimation("Enter the Arena of Esports", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>Welcome to the World of Esports</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Step into the thrilling universe of esports! Have you ever dominated a
          video game and thought, “I could take on anyone”? Perhaps you&apos;ve even
          imagined competing against the best players in the world. Well, that&apos;s
          the essence of esports!
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Esports, short for &quot;electronic sports,&quot; is a competitive arena where
          players or teams face off in video games. Much like traditional sports
          like football or basketball, esports has its own leagues, tournaments,
          and professional players who battle it out for major prizes. And the
          best part? Esports isn&apos;t just for adults—kids can dive in too!
        </p>
        <hr className="my-6 border-t border-gray-300" />
        <h2 className="text-2xl font-bold text-gray-800 mt-6">
          <strong> The Dawn of Esports: A Brief History</strong>
        </h2>
        <p className="text-lg text-gray-700 mb-4">
          Believe it or not, people have been battling in video games for
          decades! The first-ever video game competition happened in 1972 at
          Stanford University, where students played a game called{" "}
          <strong>Spacewar!</strong> to win a year&apos;s subscription to a magazine.
          Since then, esports has evolved massively, and now, millions around
          the world tune in to watch and compete in esports tournaments.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          In the 1990s, the internet revolutionized gaming, allowing players
          from different corners of the world to compete online. This
          connectivity fueled the rise of esports, with games like{" "}
          <strong>StarCraft</strong> and <strong>Counter-Strike</strong>{" "}
          becoming iconic in the competitive gaming scene. Today, esports events
          are held in massive arenas, drawing millions of viewers online!
        </p>
      </div>
    </div>
  );
};

export default Howitallbegan;

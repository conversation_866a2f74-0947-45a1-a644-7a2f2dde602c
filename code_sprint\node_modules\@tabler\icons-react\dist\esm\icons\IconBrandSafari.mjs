/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandSafari = createReactComponent("outline", "brand-safari", "IconBrandSafari", [["path", { "d": "M8 16l2 -6l6 -2l-2 6l-6 2", "key": "svg-0" }], ["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-1" }]]);

export { IconBrandSafari as default };
//# sourceMappingURL=IconBrandSafari.mjs.map

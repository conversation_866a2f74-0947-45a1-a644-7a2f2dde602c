# Replicate API for AI features (optional)
REPLICATE_API_TOKEN=your_replicate_token_here

# MongoDB Connection String (REQUIRED)
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/database_name
# For Local MongoDB: mongodb://localhost:27017/codesprint
MONGODB_URI=mongodb://localhost:27017/codesprint

# JWT Secret for authentication (REQUIRED)
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random_12345

# PayCaps Payment Integration (optional for payment features)
PAYCAPS_APP_ID=your_paycaps_app_id
PAYCAPS_SECRET=your_paycaps_secret
PAYCAPS_MERCHANT_NAME=your_merchant_name
NEXT_PUBLIC_PAYCAPS_PAYMENT_REQUEST_URL=https://api.paycaps.com/payment-request

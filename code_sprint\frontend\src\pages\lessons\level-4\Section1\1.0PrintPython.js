import React, { useState, useEffect } from "react";
import { Player } from "@lottiefiles/react-lottie-player";

const arrowAnimationUrl =
  "https://fonts.gstatic.com/s/e/notoemoji/latest/1f340/lottie.json";
const ArrowIcon = () => (
  <Player
    autoplay
    loop
    src={arrowAnimationUrl}
    style={{ height: 30, width: 30, marginRight: 10 }}
  />
);

const PrintPython = () => {
  const headings = [
    "Data analysis & visualization",
    "Artificial intelligence (AI)",
    "Machine learning (ML)",
    "Web development",
    "And more!",
  ];

  const bgColors = ["#bfdbfe", "#bbf7d0", "#fef08a", "#fecaca", "#e9d5ff"];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [bgColor, setBgColor] = useState(bgColors[0]);
  const [animationCompleted, setAnimationCompleted] = useState(false);

  const outlineStyle = {
    textShadow: `
      -1px -1px 0 #000, /* Top-left */
      1px -1px 0 #000,  /* Top-right */
      -1px 1px 0 #000,  /* Bottom-left */
      1px 1px 0 #000    /* Bottom-right */
    `,
  };
  
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % headings.length;
        setBgColor(bgColors[nextIndex]);
        return nextIndex;
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const listStyle = {
    listStyle: "none",
    paddingLeft: "8px",
  };

  const listItemStyle = {
    display: "flex",
    alignItems: "center",
    color: "#374151",
    marginBottom: "8px",
  };

  const textStyle = {
    fontFamily: "monospace",
    color: "#3b82f6",
    fontSize: "20px",
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <style>
          {`
          .falling-letter {
            display: inline-block;
            opacity: 0;
            transform: translateY(-100px);
            animation: fall 0.8s ease-in-out forwards;
          }

          @keyframes fall {
            0% {
              opacity: 0;
              transform: translateY(-100px);
            }
            50% {
              opacity: 1;
              transform: translateY(10px);
            }
            100% {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .zooming-letter {
            display: inline-block;
            animation: zoomInOut 1s infinite ease-in-out;
          }

          @keyframes zoomInOut {
            0% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.2);
            }
            100% {
              transform: scale(1);
            }
          }

          .color-transition {
            transition: background-color 1s ease;
          }
        `}
        </style>

        {}
        <h1 className="text-5xl font-semibold mb-6">
          {Array.from("PYTHON").map((letter, index) => (
            <span
              key={index}
              className={
                animationCompleted ? "zooming-letter" : "falling-letter"
              }
              style={{
                animationDelay: `${index * 0.4}s`,
                color: bgColor,
                ...outlineStyle,
              }}
              onAnimationEnd={() => {
                if (index === 5) setAnimationCompleted(true);
              }}
            >
              {letter}
            </span>
          ))}
        </h1>
        {}
        <p className="text-xl text-gray-700 font-medium">
          We&apos;re diving into <span className="text-blue-500">Python</span>, a
          programming language created by Guido van Rossum in the early 90s.
        </p>
        <p className="mt-6 text-lg text-gray-600 mb-3">
          <span className="font-semibold">Python</span> is super easy to read,
          making it perfect for beginners. It&apos;s also really versatile and is
          used for:
        </p>

        {/* <div
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f426_200d_2b1b/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "45%",
                  left: "92%",
                  transform: "translate(-50%, -50%)",
                }}
              />
          </div> */}

        <div
          style={{
            position: "absolute",
            top: "0",
            left: "0",
            width: "100%",
            height: "100%",
            pointerEvents: "none",
            zIndex: "9999",
            backgroundColor: "transparent",
          }}
        >
          <Player
            autoplay
            loop
            src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f331/lottie.json"
            style={{
              width: "100px",
              height: "100px",
              position: "absolute",
              top: "45%",
              left: "84%",
              transform: "translate(-50%, -50%)",
            }}
          />
        </div>

        {/* <div
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f331/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "45%",
                  left: "80%",
                  transform: "translate(-50%, -50%)",
                }}
              />
          </div> */}

        {}
        <div
          className="color-transition p-6 rounded-lg text-left"
          style={{ backgroundColor: bgColor }}
        >
          <ul style={listStyle}>
            <li style={listItemStyle}>
              <ArrowIcon />
              <span style={textStyle}>{headings[currentIndex]}</span>
            </li>
          </ul>
        </div>

        {}
        <p className="mt-6 text-lg text-gray-600">
          In this course, we&apos;ll write all our code in Python files with a{" "}
          <span className="font-mono text-blue-500">.py</span> extension, using
          a code editor.
        </p>
      </div>
    </div>
  );
};

export default PrintPython;

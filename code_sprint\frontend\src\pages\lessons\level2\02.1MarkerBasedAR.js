import React from "react";

export default function MarkerBasedAR() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-l from-blue-400 to-indigo-500 text-white p-10">
      <div className="max-w-3xl text-center">
        <h1 className="text-5xl font-bold mb-6">Marker-Based AR</h1>
        <p className="text-lg mb-6">
          Marker-Based Augmented Reality (AR) uses a physical marker, like a QR code or a special picture, to bring digital magic to life. When you scan the marker with your device, you can see fun animations, videos, or 3D models!
        </p>

        <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg mb-8">
          <h2 className="text-3xl font-bold mb-4 text-yellow-300">How It Works</h2>
          <p className="text-lg text-gray-800">
            Your device’s camera looks for a specific marker. When it recognizes the marker, it shows connected digital content like a 3D dinosaur or a video. This makes learning and playing super fun and interactive!
          </p>
          <img 
            src="path-to-image/marker-based-ar.jpg" 
            alt="Example of Marker-Based AR" 
            className="w-full rounded-lg mt-4"
          />
        </div>

        <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg mb-8">
          <h2 className="text-3xl font-bold mb-4 text-yellow-300">Fun Examples</h2>
          <ul className="list-disc ml-6 space-y-2 text-lg text-gray-800">
            <li>
              Scan a QR code to see a dancing robot appear on your phone screen.
            </li>
            <li>
              Point your device at a picture of the solar system and watch the planets move in 3D!
            </li>
            <li>
              Use interactive coloring books where scanned pages show your artwork in motion.
            </li>
          </ul>
          <img 
            src="path-to-image/ar-examples.jpg" 
            alt="Kids enjoying Marker-Based AR" 
            className="w-full rounded-lg mt-4"
          />
        </div>

        <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg">
          <h2 className="text-3xl font-bold mb-4 text-yellow-300">Why It’s Cool</h2>
          <p className="text-lg text-gray-800">
            Marker-Based AR turns regular objects into exciting, interactive experiences. It’s perfect for games, learning, and exploring new ideas in a fun way. Imagine seeing your favorite cartoon characters come to life!
          </p>
          <img 
            src="path-to-image/ar-cool.jpg" 
            alt="Fun with Marker-Based AR" 
            className="w-full rounded-lg mt-4"
          />
        </div>
      </div>
    </div>
  );
}

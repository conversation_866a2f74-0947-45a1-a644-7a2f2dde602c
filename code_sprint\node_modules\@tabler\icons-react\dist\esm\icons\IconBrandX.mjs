/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandX = createReactComponent("outline", "brand-x", "IconBrandX", [["path", { "d": "M4 4l11.733 16h4.267l-11.733 -16z", "key": "svg-0" }], ["path", { "d": "M4 20l6.768 -6.768m2.46 -2.46l6.772 -6.772", "key": "svg-1" }]]);

export { IconBrandX as default };
//# sourceMappingURL=IconBrandX.mjs.map

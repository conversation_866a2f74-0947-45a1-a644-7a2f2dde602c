/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandKick = createReactComponent("outline", "brand-kick", "IconBrandKick", [["path", { "d": "M4 4h5v4h3v-2h2v-2h6v4h-2v2h-2v4h2v2h2v4h-6v-2h-2v-2h-3v4h-5z", "key": "svg-0" }]]);

export { IconBrandKick as default };
//# sourceMappingURL=IconBrandKick.mjs.map

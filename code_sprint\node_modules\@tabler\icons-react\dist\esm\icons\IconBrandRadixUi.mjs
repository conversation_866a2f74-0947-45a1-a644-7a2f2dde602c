/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandRadixUi = createReactComponent("outline", "brand-radix-ui", "IconBrandRadixUi", [["path", { "d": "M16.5 5.5m-2.5 0a2.5 2.5 0 1 0 5 0a2.5 2.5 0 1 0 -5 0", "key": "svg-0" }], ["path", { "d": "M6 3h5v5h-5z", "key": "svg-1" }], ["path", { "d": "M11 11v10a5 5 0 0 1 -.217 -9.995l.217 -.005z", "key": "svg-2" }]]);

export { IconBrandRadixUi as default };
//# sourceMappingURL=IconBrandRadixUi.mjs.map

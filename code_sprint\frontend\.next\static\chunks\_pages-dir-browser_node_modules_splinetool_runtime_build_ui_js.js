"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_ui_js"],{

/***/ "(pages-dir-browser)/../node_modules/@splinetool/runtime/build/ui.js":
/*!*******************************************************!*\
  !*** ../node_modules/@splinetool/runtime/build/ui.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(pages-dir-browser)/../node_modules/process/browser.js\");\nvar Qr=(ae=>typeof require<\"u\"?require:typeof Proxy<\"u\"?new Proxy(ae,{get:(ge,m)=>(typeof require<\"u\"?require:ge)[m]}):ae)(function(ae){if(typeof require<\"u\")return require.apply(this,arguments);throw new Error('Dynamic require of \"'+ae+'\" is not supported')});var Kn=(ae,ge)=>()=>(ge||ae((ge={exports:{}}).exports,ge),ge.exports);var Jn=Kn((zr,Kt)=>{var Zr=(()=>{var ae=typeof document<\"u\"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<\"u\"&&(ae=ae||__filename),function(ge={}){var m=ge,Jt,et;m.ready=new Promise((e,t)=>{Jt=e,et=t}),function(e){e.Id=e.Id||[],e.Id.push(function(){e.MakeSWCanvasSurface=function(t){var r=t,i=typeof OffscreenCanvas<\"u\"&&r instanceof OffscreenCanvas;if(!(typeof HTMLCanvasElement<\"u\"&&r instanceof HTMLCanvasElement||i||(r=document.getElementById(t),r)))throw\"Canvas with id \"+t+\" was not found\";return(t=e.MakeSurface(r.width,r.height))&&(t.ie=r),t},e.MakeCanvasSurface||(e.MakeCanvasSurface=e.MakeSWCanvasSurface),e.MakeSurface=function(t,r){var i={width:t,height:r,colorType:e.ColorType.RGBA_8888,alphaType:e.AlphaType.Unpremul,colorSpace:e.ColorSpace.SRGB},o=t*r*4,s=e._malloc(o);return(i=e.Surface._makeRasterDirect(i,s,4*t))&&(i.ie=null,i.Pe=t,i.Me=r,i.Ne=o,i.re=s,i.getCanvas().clear(e.TRANSPARENT)),i},e.MakeRasterDirectSurface=function(t,r,i){return e.Surface._makeRasterDirect(t,r.byteOffset,i)},e.Surface.prototype.flush=function(t){if(e.Fd(this.Ed),this._flush(),this.ie){var r=new Uint8ClampedArray(e.HEAPU8.buffer,this.re,this.Ne);r=new ImageData(r,this.Pe,this.Me),t?this.ie.getContext(\"2d\").putImageData(r,0,0,t[0],t[1],t[2]-t[0],t[3]-t[1]):this.ie.getContext(\"2d\").putImageData(r,0,0)}},e.Surface.prototype.dispose=function(){this.re&&e._free(this.re),this.delete()},e.Fd=e.Fd||function(){},e.je=e.je||function(){return null}})}(m),function(e){e.Id=e.Id||[],e.Id.push(function(){function t(f,h,g){return f&&f.hasOwnProperty(h)?f[h]:g}function r(f){var h=ke(re);return re[h]=f,h}function i(f){return f.naturalHeight||f.videoHeight||f.displayHeight||f.height}function o(f){return f.naturalWidth||f.videoWidth||f.displayWidth||f.width}function s(f,h,g,P){return f.bindTexture(f.TEXTURE_2D,h),P||g.alphaType!==e.AlphaType.Premul||f.pixelStorei(f.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),h}function l(f,h,g){g||h.alphaType!==e.AlphaType.Premul||f.pixelStorei(f.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),f.bindTexture(f.TEXTURE_2D,null)}e.GetWebGLContext=function(f,h){if(!f)throw\"null canvas passed into makeWebGLContext\";var g={alpha:t(h,\"alpha\",1),depth:t(h,\"depth\",1),stencil:t(h,\"stencil\",8),antialias:t(h,\"antialias\",0),premultipliedAlpha:t(h,\"premultipliedAlpha\",1),preserveDrawingBuffer:t(h,\"preserveDrawingBuffer\",0),preferLowPowerToHighPerformance:t(h,\"preferLowPowerToHighPerformance\",0),failIfMajorPerformanceCaveat:t(h,\"failIfMajorPerformanceCaveat\",0),enableExtensionsByDefault:t(h,\"enableExtensionsByDefault\",1),explicitSwapControl:t(h,\"explicitSwapControl\",0),renderViaOffscreenBackBuffer:t(h,\"renderViaOffscreenBackBuffer\",0)};if(g.majorVersion=h&&h.majorVersion?h.majorVersion:typeof WebGL2RenderingContext<\"u\"?2:1,g.explicitSwapControl)throw\"explicitSwapControl is not supported\";return f=Mn(f,g),f?(Ir(f),B.Qd.getExtension(\"WEBGL_debug_renderer_info\"),f):0},e.deleteContext=function(f){B===me[f]&&(B=null),typeof JSEvents==\"object\"&&JSEvents.tf(me[f].Qd.canvas),me[f]&&me[f].Qd.canvas&&(me[f].Qd.canvas.Ke=void 0),me[f]=null},e._setTextureCleanup({deleteTexture:function(f,h){var g=re[h];g&&me[f].Qd.deleteTexture(g),re[h]=null}}),e.MakeWebGLContext=function(f){if(!this.Fd(f))return null;var h=this._MakeGrContext();if(!h)return null;h.Ed=f;var g=h.delete.bind(h);return h.delete=function(){e.Fd(this.Ed),g()}.bind(h),B.te=h},e.MakeGrContext=e.MakeWebGLContext,e.GrDirectContext.prototype.getResourceCacheLimitBytes=function(){e.Fd(this.Ed),this._getResourceCacheLimitBytes()},e.GrDirectContext.prototype.getResourceCacheUsageBytes=function(){e.Fd(this.Ed),this._getResourceCacheUsageBytes()},e.GrDirectContext.prototype.releaseResourcesAndAbandonContext=function(){e.Fd(this.Ed),this._releaseResourcesAndAbandonContext()},e.GrDirectContext.prototype.setResourceCacheLimitBytes=function(f){e.Fd(this.Ed),this._setResourceCacheLimitBytes(f)},e.MakeOnScreenGLSurface=function(f,h,g,P,E,T){return!this.Fd(f.Ed)||(h=E===void 0||T===void 0?this._MakeOnScreenGLSurface(f,h,g,P):this._MakeOnScreenGLSurface(f,h,g,P,E,T),!h)?null:(h.Ed=f.Ed,h)},e.MakeRenderTarget=function(){var f=arguments[0];if(!this.Fd(f.Ed))return null;if(arguments.length===3){var h=this._MakeRenderTargetWH(f,arguments[1],arguments[2]);if(!h)return null}else if(arguments.length===2){if(h=this._MakeRenderTargetII(f,arguments[1]),!h)return null}else return null;return h.Ed=f.Ed,h},e.MakeWebGLCanvasSurface=function(f,h,g){h=h||null;var P=f,E=typeof OffscreenCanvas<\"u\"&&P instanceof OffscreenCanvas;if(!(typeof HTMLCanvasElement<\"u\"&&P instanceof HTMLCanvasElement||E||(P=document.getElementById(f),P)))throw\"Canvas with id \"+f+\" was not found\";if(f=this.GetWebGLContext(P,g),!f||0>f)throw\"failed to create webgl context: err \"+f;return f=this.MakeWebGLContext(f),h=this.MakeOnScreenGLSurface(f,P.width,P.height,h),h||(h=P.cloneNode(!0),P.parentNode.replaceChild(h,P),h.classList.add(\"ck-replaced\"),e.MakeSWCanvasSurface(h))},e.MakeCanvasSurface=e.MakeWebGLCanvasSurface,e.Surface.prototype.makeImageFromTexture=function(f,h){return e.Fd(this.Ed),f=r(f),(h=this._makeImageFromTexture(this.Ed,f,h))&&(h.de=f),h},e.Surface.prototype.makeImageFromTextureSource=function(f,h,g){h||(h={height:i(f),width:o(f),colorType:e.ColorType.RGBA_8888,alphaType:g?e.AlphaType.Premul:e.AlphaType.Unpremul}),h.colorSpace||(h.colorSpace=e.ColorSpace.SRGB),e.Fd(this.Ed);var P=B.Qd;return g=s(P,P.createTexture(),h,g),B.version===2?P.texImage2D(P.TEXTURE_2D,0,P.RGBA,h.width,h.height,0,P.RGBA,P.UNSIGNED_BYTE,f):P.texImage2D(P.TEXTURE_2D,0,P.RGBA,P.RGBA,P.UNSIGNED_BYTE,f),l(P,h),this._resetContext(),this.makeImageFromTexture(g,h)},e.Surface.prototype.updateTextureFromSource=function(f,h,g){if(f.de){e.Fd(this.Ed);var P=f.getImageInfo(),E=B.Qd,T=s(E,re[f.de],P,g);B.version===2?E.texImage2D(E.TEXTURE_2D,0,E.RGBA,o(h),i(h),0,E.RGBA,E.UNSIGNED_BYTE,h):E.texImage2D(E.TEXTURE_2D,0,E.RGBA,E.RGBA,E.UNSIGNED_BYTE,h),l(E,P,g),this._resetContext(),re[f.de]=null,f.de=r(T),P.colorSpace=f.getColorSpace(),h=this._makeImageFromTexture(this.Ed,f.de,P),g=f.Dd.Hd,E=f.Dd.Ld,f.Dd.Hd=h.Dd.Hd,f.Dd.Ld=h.Dd.Ld,h.Dd.Hd=g,h.Dd.Ld=E,h.delete(),P.colorSpace.delete()}},e.MakeLazyImageFromTextureSource=function(f,h,g){h||(h={height:i(f),width:o(f),colorType:e.ColorType.RGBA_8888,alphaType:g?e.AlphaType.Premul:e.AlphaType.Unpremul}),h.colorSpace||(h.colorSpace=e.ColorSpace.SRGB);var P={makeTexture:function(){var E=B,T=E.Qd,v=s(T,T.createTexture(),h,g);return E.version===2?T.texImage2D(T.TEXTURE_2D,0,T.RGBA,h.width,h.height,0,T.RGBA,T.UNSIGNED_BYTE,f):T.texImage2D(T.TEXTURE_2D,0,T.RGBA,T.RGBA,T.UNSIGNED_BYTE,f),l(T,h,g),r(v)},freeSrc:function(){}};return f.constructor.name===\"VideoFrame\"&&(P.freeSrc=function(){f.close()}),e.Image._makeFromGenerator(h,P)},e.Fd=function(f){return f?Ir(f):!1},e.je=function(){return B&&B.te&&!B.te.isDeleted()?B.te:null}})}(m),function(e){function t(a){return(o(255*a[3])<<24|o(255*a[0])<<16|o(255*a[1])<<8|o(255*a[2])<<0)>>>0}function r(a){if(a&&a._ck)return a;if(a instanceof Float32Array){for(var n=Math.floor(a.length/4),u=new Uint32Array(n),c=0;c<n;c++)u[c]=t(a.slice(4*c,4*(c+1)));return u}if(a instanceof Uint32Array)return a;if(a instanceof Array&&a[0]instanceof Float32Array)return a.map(t)}function i(a){if(a===void 0)return 1;var n=parseFloat(a);return a&&a.indexOf(\"%\")!==-1?n/100:n}function o(a){return Math.round(Math.max(0,Math.min(a||0,255)))}function s(a,n){n&&n._ck||e._free(a)}function l(a,n,u){if(!a||!a.length)return L;if(a&&a._ck)return a.byteOffset;var c=e[n].BYTES_PER_ELEMENT;return u||(u=e._malloc(a.length*c)),e[n].set(a,u/c),u}function f(a){var n={Nd:L,count:a.length,colorType:e.ColorType.RGBA_F32};if(a instanceof Float32Array)n.Nd=l(a,\"HEAPF32\"),n.count=a.length/4;else if(a instanceof Uint32Array)n.Nd=l(a,\"HEAPU32\"),n.colorType=e.ColorType.RGBA_8888;else if(a instanceof Array){if(a&&a.length){for(var u=e._malloc(16*a.length),c=0,y=u/4,_=0;_<a.length;_++)for(var C=0;4>C;C++)e.HEAPF32[y+c]=a[_][C],c++;a=u}else a=L;n.Nd=a}else throw\"Invalid argument to copyFlexibleColorArray, Not a color array \"+typeof a;return n}function h(a){if(!a)return L;var n=W.toTypedArray();if(a.length){if(a.length===6||a.length===9)return l(a,\"HEAPF32\",x),a.length===6&&e.HEAPF32.set(Xn,6+x/4),x;if(a.length===16)return n[0]=a[0],n[1]=a[1],n[2]=a[3],n[3]=a[4],n[4]=a[5],n[5]=a[7],n[6]=a[12],n[7]=a[13],n[8]=a[15],x;throw\"invalid matrix size\"}if(a.m11===void 0)throw\"invalid matrix argument\";return n[0]=a.m11,n[1]=a.m21,n[2]=a.m41,n[3]=a.m12,n[4]=a.m22,n[5]=a.m42,n[6]=a.m14,n[7]=a.m24,n[8]=a.m44,x}function g(a){if(!a)return L;var n=Y.toTypedArray();if(a.length){if(a.length!==16&&a.length!==6&&a.length!==9)throw\"invalid matrix size\";return a.length===16?l(a,\"HEAPF32\",J):(n.fill(0),n[0]=a[0],n[1]=a[1],n[3]=a[2],n[4]=a[3],n[5]=a[4],n[7]=a[5],n[10]=1,n[12]=a[6],n[13]=a[7],n[15]=a[8],a.length===6&&(n[12]=0,n[13]=0,n[15]=1),J)}if(a.m11===void 0)throw\"invalid matrix argument\";return n[0]=a.m11,n[1]=a.m21,n[2]=a.m31,n[3]=a.m41,n[4]=a.m12,n[5]=a.m22,n[6]=a.m32,n[7]=a.m42,n[8]=a.m13,n[9]=a.m23,n[10]=a.m33,n[11]=a.m43,n[12]=a.m14,n[13]=a.m24,n[14]=a.m34,n[15]=a.m44,J}function P(a,n){return l(a,\"HEAPF32\",n||_e)}function E(a,n,u,c){var y=Ue.toTypedArray();return y[0]=a,y[1]=n,y[2]=u,y[3]=c,_e}function T(a){for(var n=new Float32Array(4),u=0;4>u;u++)n[u]=e.HEAPF32[a/4+u];return n}function v(a,n){return l(a,\"HEAPF32\",n||N)}function M(a,n){return l(a,\"HEAPF32\",n||Nt)}e.Color=function(a,n,u,c){return c===void 0&&(c=1),e.Color4f(o(a)/255,o(n)/255,o(u)/255,c)},e.ColorAsInt=function(a,n,u,c){return c===void 0&&(c=255),(o(c)<<24|o(a)<<16|o(n)<<8|o(u)<<0&268435455)>>>0},e.Color4f=function(a,n,u,c){return c===void 0&&(c=1),Float32Array.of(a,n,u,c)},Object.defineProperty(e,\"TRANSPARENT\",{get:function(){return e.Color4f(0,0,0,0)}}),Object.defineProperty(e,\"BLACK\",{get:function(){return e.Color4f(0,0,0,1)}}),Object.defineProperty(e,\"WHITE\",{get:function(){return e.Color4f(1,1,1,1)}}),Object.defineProperty(e,\"RED\",{get:function(){return e.Color4f(1,0,0,1)}}),Object.defineProperty(e,\"GREEN\",{get:function(){return e.Color4f(0,1,0,1)}}),Object.defineProperty(e,\"BLUE\",{get:function(){return e.Color4f(0,0,1,1)}}),Object.defineProperty(e,\"YELLOW\",{get:function(){return e.Color4f(1,1,0,1)}}),Object.defineProperty(e,\"CYAN\",{get:function(){return e.Color4f(0,1,1,1)}}),Object.defineProperty(e,\"MAGENTA\",{get:function(){return e.Color4f(1,0,1,1)}}),e.getColorComponents=function(a){return[Math.floor(255*a[0]),Math.floor(255*a[1]),Math.floor(255*a[2]),a[3]]},e.parseColorString=function(a,n){if(a=a.toLowerCase(),a.startsWith(\"#\")){switch(n=255,a.length){case 9:n=parseInt(a.slice(7,9),16);case 7:var u=parseInt(a.slice(1,3),16),c=parseInt(a.slice(3,5),16),y=parseInt(a.slice(5,7),16);break;case 5:n=17*parseInt(a.slice(4,5),16);case 4:u=17*parseInt(a.slice(1,2),16),c=17*parseInt(a.slice(2,3),16),y=17*parseInt(a.slice(3,4),16)}return e.Color(u,c,y,n/255)}return a.startsWith(\"rgba\")?(a=a.slice(5,-1),a=a.split(\",\"),e.Color(+a[0],+a[1],+a[2],i(a[3]))):a.startsWith(\"rgb\")?(a=a.slice(4,-1),a=a.split(\",\"),e.Color(+a[0],+a[1],+a[2],i(a[3]))):a.startsWith(\"gray(\")||a.startsWith(\"hsl\")||!n||(a=n[a],a===void 0)?e.BLACK:a},e.multiplyByAlpha=function(a,n){return a=a.slice(),a[3]=Math.max(0,Math.min(a[3]*n,1)),a},e.Malloc=function(a,n){var u=e._malloc(n*a.BYTES_PER_ELEMENT);return{_ck:!0,length:n,byteOffset:u,Xd:null,subarray:function(c,y){return c=this.toTypedArray().subarray(c,y),c._ck=!0,c},toTypedArray:function(){return this.Xd&&this.Xd.length?this.Xd:(this.Xd=new a(e.HEAPU8.buffer,u,n),this.Xd._ck=!0,this.Xd)}}},e.Free=function(a){e._free(a.byteOffset),a.byteOffset=L,a.toTypedArray=null,a.Xd=null};var x=L,W,J=L,Y,_e=L,Ue,de,N=L,Sr,Fe=L,Vr,St=L,Nr,Vt=L,Et,ze=L,Yr,Nt=L,Xr,Kr=L,Xn=Float32Array.of(0,0,1),L=0;e.onRuntimeInitialized=function(){function a(n,u,c,y,_,C,F){C||(C=4*y.width,y.colorType===e.ColorType.RGBA_F16?C*=2:y.colorType===e.ColorType.RGBA_F32&&(C*=4));var D=C*y.height,I=_?_.byteOffset:e._malloc(D);if(F?!n._readPixels(y,I,C,u,c,F):!n._readPixels(y,I,C,u,c))return _||e._free(I),null;if(_)return _.toTypedArray();switch(y.colorType){case e.ColorType.RGBA_8888:case e.ColorType.RGBA_F16:n=new Uint8Array(e.HEAPU8.buffer,I,D).slice();break;case e.ColorType.RGBA_F32:n=new Float32Array(e.HEAPU8.buffer,I,D).slice();break;default:return null}return e._free(I),n}Ue=e.Malloc(Float32Array,4),_e=Ue.byteOffset,Y=e.Malloc(Float32Array,16),J=Y.byteOffset,W=e.Malloc(Float32Array,9),x=W.byteOffset,Yr=e.Malloc(Float32Array,12),Nt=Yr.byteOffset,Xr=e.Malloc(Float32Array,12),Kr=Xr.byteOffset,de=e.Malloc(Float32Array,4),N=de.byteOffset,Sr=e.Malloc(Float32Array,4),Fe=Sr.byteOffset,Vr=e.Malloc(Float32Array,3),St=Vr.byteOffset,Nr=e.Malloc(Float32Array,3),Vt=Nr.byteOffset,Et=e.Malloc(Int32Array,4),ze=Et.byteOffset,e.ColorSpace.SRGB=e.ColorSpace._MakeSRGB(),e.ColorSpace.DISPLAY_P3=e.ColorSpace._MakeDisplayP3(),e.ColorSpace.ADOBE_RGB=e.ColorSpace._MakeAdobeRGB(),e.GlyphRunFlags={IsWhiteSpace:e._GlyphRunFlags_isWhiteSpace},e.Path.MakeFromCmds=function(n){var u=l(n,\"HEAPF32\"),c=e.Path._MakeFromCmds(u,n.length);return s(u,n),c},e.Path.MakeFromVerbsPointsWeights=function(n,u,c){var y=l(n,\"HEAPU8\"),_=l(u,\"HEAPF32\"),C=l(c,\"HEAPF32\"),F=e.Path._MakeFromVerbsPointsWeights(y,n.length,_,u.length,C,c&&c.length||0);return s(y,n),s(_,u),s(C,c),F},e.Path.prototype.addArc=function(n,u,c){return n=v(n),this._addArc(n,u,c),this},e.Path.prototype.addCircle=function(n,u,c,y){return this._addCircle(n,u,c,!!y),this},e.Path.prototype.addOval=function(n,u,c){return c===void 0&&(c=1),n=v(n),this._addOval(n,!!u,c),this},e.Path.prototype.addPath=function(){var n=Array.prototype.slice.call(arguments),u=n[0],c=!1;if(typeof n[n.length-1]==\"boolean\"&&(c=n.pop()),n.length===1)this._addPath(u,1,0,0,0,1,0,0,0,1,c);else if(n.length===2)n=n[1],this._addPath(u,n[0],n[1],n[2],n[3],n[4],n[5],n[6]||0,n[7]||0,n[8]||1,c);else if(n.length===7||n.length===10)this._addPath(u,n[1],n[2],n[3],n[4],n[5],n[6],n[7]||0,n[8]||0,n[9]||1,c);else return null;return this},e.Path.prototype.addPoly=function(n,u){var c=l(n,\"HEAPF32\");return this._addPoly(c,n.length/2,u),s(c,n),this},e.Path.prototype.addRect=function(n,u){return n=v(n),this._addRect(n,!!u),this},e.Path.prototype.addRRect=function(n,u){return n=M(n),this._addRRect(n,!!u),this},e.Path.prototype.addVerbsPointsWeights=function(n,u,c){var y=l(n,\"HEAPU8\"),_=l(u,\"HEAPF32\"),C=l(c,\"HEAPF32\");this._addVerbsPointsWeights(y,n.length,_,u.length,C,c&&c.length||0),s(y,n),s(_,u),s(C,c)},e.Path.prototype.arc=function(n,u,c,y,_,C){return n=e.LTRBRect(n-c,u-c,n+c,u+c),_=(_-y)/Math.PI*180-360*!!C,C=new e.Path,C.addArc(n,y/Math.PI*180,_),this.addPath(C,!0),C.delete(),this},e.Path.prototype.arcToOval=function(n,u,c,y){return n=v(n),this._arcToOval(n,u,c,y),this},e.Path.prototype.arcToRotated=function(n,u,c,y,_,C,F){return this._arcToRotated(n,u,c,!!y,!!_,C,F),this},e.Path.prototype.arcToTangent=function(n,u,c,y,_){return this._arcToTangent(n,u,c,y,_),this},e.Path.prototype.close=function(){return this._close(),this},e.Path.prototype.conicTo=function(n,u,c,y,_){return this._conicTo(n,u,c,y,_),this},e.Path.prototype.computeTightBounds=function(n){this._computeTightBounds(N);var u=de.toTypedArray();return n?(n.set(u),n):u.slice()},e.Path.prototype.cubicTo=function(n,u,c,y,_,C){return this._cubicTo(n,u,c,y,_,C),this},e.Path.prototype.dash=function(n,u,c){return this._dash(n,u,c)?this:null},e.Path.prototype.getBounds=function(n){this._getBounds(N);var u=de.toTypedArray();return n?(n.set(u),n):u.slice()},e.Path.prototype.lineTo=function(n,u){return this._lineTo(n,u),this},e.Path.prototype.moveTo=function(n,u){return this._moveTo(n,u),this},e.Path.prototype.offset=function(n,u){return this._transform(1,0,n,0,1,u,0,0,1),this},e.Path.prototype.quadTo=function(n,u,c,y){return this._quadTo(n,u,c,y),this},e.Path.prototype.rArcTo=function(n,u,c,y,_,C,F){return this._rArcTo(n,u,c,y,_,C,F),this},e.Path.prototype.rConicTo=function(n,u,c,y,_){return this._rConicTo(n,u,c,y,_),this},e.Path.prototype.rCubicTo=function(n,u,c,y,_,C){return this._rCubicTo(n,u,c,y,_,C),this},e.Path.prototype.rLineTo=function(n,u){return this._rLineTo(n,u),this},e.Path.prototype.rMoveTo=function(n,u){return this._rMoveTo(n,u),this},e.Path.prototype.rQuadTo=function(n,u,c,y){return this._rQuadTo(n,u,c,y),this},e.Path.prototype.stroke=function(n){return n=n||{},n.width=n.width||1,n.miter_limit=n.miter_limit||4,n.cap=n.cap||e.StrokeCap.Butt,n.join=n.join||e.StrokeJoin.Miter,n.precision=n.precision||1,this._stroke(n)?this:null},e.Path.prototype.transform=function(){if(arguments.length===1){var n=arguments[0];this._transform(n[0],n[1],n[2],n[3],n[4],n[5],n[6]||0,n[7]||0,n[8]||1)}else if(arguments.length===6||arguments.length===9)n=arguments,this._transform(n[0],n[1],n[2],n[3],n[4],n[5],n[6]||0,n[7]||0,n[8]||1);else throw\"transform expected to take 1 or 9 arguments. Got \"+arguments.length;return this},e.Path.prototype.trim=function(n,u,c){return this._trim(n,u,!!c)?this:null},e.Image.prototype.encodeToBytes=function(n,u){var c=e.je();return n=n||e.ImageFormat.PNG,u=u||100,c?this._encodeToBytes(n,u,c):this._encodeToBytes(n,u)},e.Image.prototype.makeShaderCubic=function(n,u,c,y,_){return _=h(_),this._makeShaderCubic(n,u,c,y,_)},e.Image.prototype.makeShaderOptions=function(n,u,c,y,_){return _=h(_),this._makeShaderOptions(n,u,c,y,_)},e.Image.prototype.readPixels=function(n,u,c,y,_){var C=e.je();return a(this,n,u,c,y,_,C)},e.Canvas.prototype.clear=function(n){e.Fd(this.Ed),n=P(n),this._clear(n)},e.Canvas.prototype.clipRRect=function(n,u,c){e.Fd(this.Ed),n=M(n),this._clipRRect(n,u,c)},e.Canvas.prototype.clipRect=function(n,u,c){e.Fd(this.Ed),n=v(n),this._clipRect(n,u,c)},e.Canvas.prototype.concat=function(n){e.Fd(this.Ed),n=g(n),this._concat(n)},e.Canvas.prototype.drawArc=function(n,u,c,y,_){e.Fd(this.Ed),n=v(n),this._drawArc(n,u,c,y,_)},e.Canvas.prototype.drawAtlas=function(n,u,c,y,_,C,F){if(n&&y&&u&&c&&u.length===c.length){e.Fd(this.Ed),_||(_=e.BlendMode.SrcOver);var D=l(u,\"HEAPF32\"),I=l(c,\"HEAPF32\"),$=c.length/4,V=l(r(C),\"HEAPU32\");if(F&&\"B\"in F&&\"C\"in F)this._drawAtlasCubic(n,I,D,V,$,_,F.B,F.C,y);else{let d=e.FilterMode.Linear,A=e.MipmapMode.None;F&&(d=F.filter,\"mipmap\"in F&&(A=F.mipmap)),this._drawAtlasOptions(n,I,D,V,$,_,d,A,y)}s(D,u),s(I,c),s(V,C)}},e.Canvas.prototype.drawCircle=function(n,u,c,y){e.Fd(this.Ed),this._drawCircle(n,u,c,y)},e.Canvas.prototype.drawColor=function(n,u){e.Fd(this.Ed),n=P(n),u!==void 0?this._drawColor(n,u):this._drawColor(n)},e.Canvas.prototype.drawColorInt=function(n,u){e.Fd(this.Ed),this._drawColorInt(n,u||e.BlendMode.SrcOver)},e.Canvas.prototype.drawColorComponents=function(n,u,c,y,_){e.Fd(this.Ed),n=E(n,u,c,y),_!==void 0?this._drawColor(n,_):this._drawColor(n)},e.Canvas.prototype.drawDRRect=function(n,u,c){e.Fd(this.Ed),n=M(n,Nt),u=M(u,Kr),this._drawDRRect(n,u,c)},e.Canvas.prototype.drawImage=function(n,u,c,y){e.Fd(this.Ed),this._drawImage(n,u,c,y||null)},e.Canvas.prototype.drawImageCubic=function(n,u,c,y,_,C){e.Fd(this.Ed),this._drawImageCubic(n,u,c,y,_,C||null)},e.Canvas.prototype.drawImageOptions=function(n,u,c,y,_,C){e.Fd(this.Ed),this._drawImageOptions(n,u,c,y,_,C||null)},e.Canvas.prototype.drawImageNine=function(n,u,c,y,_){e.Fd(this.Ed),u=l(u,\"HEAP32\",ze),c=v(c),this._drawImageNine(n,u,c,y,_||null)},e.Canvas.prototype.drawImageRect=function(n,u,c,y,_){e.Fd(this.Ed),v(u,N),v(c,Fe),this._drawImageRect(n,N,Fe,y,!!_)},e.Canvas.prototype.drawImageRectCubic=function(n,u,c,y,_,C){e.Fd(this.Ed),v(u,N),v(c,Fe),this._drawImageRectCubic(n,N,Fe,y,_,C||null)},e.Canvas.prototype.drawImageRectOptions=function(n,u,c,y,_,C){e.Fd(this.Ed),v(u,N),v(c,Fe),this._drawImageRectOptions(n,N,Fe,y,_,C||null)},e.Canvas.prototype.drawLine=function(n,u,c,y,_){e.Fd(this.Ed),this._drawLine(n,u,c,y,_)},e.Canvas.prototype.drawOval=function(n,u){e.Fd(this.Ed),n=v(n),this._drawOval(n,u)},e.Canvas.prototype.drawPaint=function(n){e.Fd(this.Ed),this._drawPaint(n)},e.Canvas.prototype.drawParagraph=function(n,u,c){e.Fd(this.Ed),this._drawParagraph(n,u,c)},e.Canvas.prototype.drawPatch=function(n,u,c,y,_){if(24>n.length)throw\"Need 12 cubic points\";if(u&&4>u.length)throw\"Need 4 colors\";if(c&&8>c.length)throw\"Need 4 shader coordinates\";e.Fd(this.Ed);let C=l(n,\"HEAPF32\"),F=u?l(r(u),\"HEAPU32\"):L,D=c?l(c,\"HEAPF32\"):L;y||(y=e.BlendMode.Modulate),this._drawPatch(C,F,D,y,_),s(D,c),s(F,u),s(C,n)},e.Canvas.prototype.drawPath=function(n,u){e.Fd(this.Ed),this._drawPath(n,u)},e.Canvas.prototype.drawPicture=function(n){e.Fd(this.Ed),this._drawPicture(n)},e.Canvas.prototype.drawPoints=function(n,u,c){e.Fd(this.Ed);var y=l(u,\"HEAPF32\");this._drawPoints(n,y,u.length/2,c),s(y,u)},e.Canvas.prototype.drawRRect=function(n,u){e.Fd(this.Ed),n=M(n),this._drawRRect(n,u)},e.Canvas.prototype.drawRect=function(n,u){e.Fd(this.Ed),n=v(n),this._drawRect(n,u)},e.Canvas.prototype.drawRect4f=function(n,u,c,y,_){e.Fd(this.Ed),this._drawRect4f(n,u,c,y,_)},e.Canvas.prototype.drawShadow=function(n,u,c,y,_,C,F){e.Fd(this.Ed);var D=l(_,\"HEAPF32\"),I=l(C,\"HEAPF32\");u=l(u,\"HEAPF32\",St),c=l(c,\"HEAPF32\",Vt),this._drawShadow(n,u,c,y,D,I,F),s(D,_),s(I,C)},e.getShadowLocalBounds=function(n,u,c,y,_,C,F){return n=h(n),c=l(c,\"HEAPF32\",St),y=l(y,\"HEAPF32\",Vt),this._getShadowLocalBounds(n,u,c,y,_,C,N)?(u=de.toTypedArray(),F?(F.set(u),F):u.slice()):null},e.Canvas.prototype.drawTextBlob=function(n,u,c,y){e.Fd(this.Ed),this._drawTextBlob(n,u,c,y)},e.Canvas.prototype.drawVertices=function(n,u,c){e.Fd(this.Ed),this._drawVertices(n,u,c)},e.Canvas.prototype.getDeviceClipBounds=function(n){this._getDeviceClipBounds(ze);var u=Et.toTypedArray();return n?n.set(u):n=u.slice(),n},e.Canvas.prototype.getLocalToDevice=function(){this._getLocalToDevice(J);for(var n=J,u=Array(16),c=0;16>c;c++)u[c]=e.HEAPF32[n/4+c];return u},e.Canvas.prototype.getTotalMatrix=function(){this._getTotalMatrix(x);for(var n=Array(9),u=0;9>u;u++)n[u]=e.HEAPF32[x/4+u];return n},e.Canvas.prototype.makeSurface=function(n){return n=this._makeSurface(n),n.Ed=this.Ed,n},e.Canvas.prototype.readPixels=function(n,u,c,y,_){return e.Fd(this.Ed),a(this,n,u,c,y,_)},e.Canvas.prototype.saveLayer=function(n,u,c,y){return u=v(u),this._saveLayer(n||null,u,c||null,y||0)},e.Canvas.prototype.writePixels=function(n,u,c,y,_,C,F,D){if(n.byteLength%(u*c))throw\"pixels length must be a multiple of the srcWidth * srcHeight\";e.Fd(this.Ed);var I=n.byteLength/(u*c);C=C||e.AlphaType.Unpremul,F=F||e.ColorType.RGBA_8888,D=D||e.ColorSpace.SRGB;var $=I*u;return I=l(n,\"HEAPU8\"),u=this._writePixels({width:u,height:c,colorType:F,alphaType:C,colorSpace:D},I,$,y,_),s(I,n),u},e.ColorFilter.MakeBlend=function(n,u,c){return n=P(n),c=c||e.ColorSpace.SRGB,e.ColorFilter._MakeBlend(n,u,c)},e.ColorFilter.MakeMatrix=function(n){if(!n||n.length!==20)throw\"invalid color matrix\";var u=l(n,\"HEAPF32\"),c=e.ColorFilter._makeMatrix(u);return s(u,n),c},e.ContourMeasure.prototype.getPosTan=function(n,u){return this._getPosTan(n,N),n=de.toTypedArray(),u?(u.set(n),u):n.slice()},e.ImageFilter.prototype.getOutputBounds=function(n,u,c){return n=v(n,N),u=h(u),this._getOutputBounds(n,u,ze),u=Et.toTypedArray(),c?(c.set(u),c):u.slice()},e.ImageFilter.MakeDropShadow=function(n,u,c,y,_,C){return _=P(_,_e),e.ImageFilter._MakeDropShadow(n,u,c,y,_,C)},e.ImageFilter.MakeDropShadowOnly=function(n,u,c,y,_,C){return _=P(_,_e),e.ImageFilter._MakeDropShadowOnly(n,u,c,y,_,C)},e.ImageFilter.MakeImage=function(n,u,c,y){if(c=v(c,N),y=v(y,Fe),\"B\"in u&&\"C\"in u)return e.ImageFilter._MakeImageCubic(n,u.B,u.C,c,y);let _=u.filter,C=e.MipmapMode.None;return\"mipmap\"in u&&(C=u.mipmap),e.ImageFilter._MakeImageOptions(n,_,C,c,y)},e.ImageFilter.MakeMatrixTransform=function(n,u,c){if(n=h(n),\"B\"in u&&\"C\"in u)return e.ImageFilter._MakeMatrixTransformCubic(n,u.B,u.C,c);let y=u.filter,_=e.MipmapMode.None;return\"mipmap\"in u&&(_=u.mipmap),e.ImageFilter._MakeMatrixTransformOptions(n,y,_,c)},e.Paint.prototype.getColor=function(){return this._getColor(_e),T(_e)},e.Paint.prototype.setColor=function(n,u){u=u||null,n=P(n),this._setColor(n,u)},e.Paint.prototype.setColorComponents=function(n,u,c,y,_){_=_||null,n=E(n,u,c,y),this._setColor(n,_)},e.Path.prototype.getPoint=function(n,u){return this._getPoint(n,N),n=de.toTypedArray(),u?(u[0]=n[0],u[1]=n[1],u):n.slice(0,2)},e.Picture.prototype.makeShader=function(n,u,c,y,_){return y=h(y),_=v(_),this._makeShader(n,u,c,y,_)},e.Picture.prototype.cullRect=function(n){this._cullRect(N);var u=de.toTypedArray();return n?(n.set(u),n):u.slice()},e.PictureRecorder.prototype.beginRecording=function(n,u){return n=v(n),this._beginRecording(n,!!u)},e.Surface.prototype.getCanvas=function(){var n=this._getCanvas();return n.Ed=this.Ed,n},e.Surface.prototype.makeImageSnapshot=function(n){return e.Fd(this.Ed),n=l(n,\"HEAP32\",ze),this._makeImageSnapshot(n)},e.Surface.prototype.makeSurface=function(n){return e.Fd(this.Ed),n=this._makeSurface(n),n.Ed=this.Ed,n},e.Surface.prototype.Oe=function(n,u){return this.ce||(this.ce=this.getCanvas()),requestAnimationFrame(function(){e.Fd(this.Ed),n(this.ce),this.flush(u)}.bind(this))},e.Surface.prototype.requestAnimationFrame||(e.Surface.prototype.requestAnimationFrame=e.Surface.prototype.Oe),e.Surface.prototype.Le=function(n,u){this.ce||(this.ce=this.getCanvas()),requestAnimationFrame(function(){e.Fd(this.Ed),n(this.ce),this.flush(u),this.dispose()}.bind(this))},e.Surface.prototype.drawOnce||(e.Surface.prototype.drawOnce=e.Surface.prototype.Le),e.PathEffect.MakeDash=function(n,u){if(u||(u=0),!n.length||n.length%2===1)throw\"Intervals array must have even length\";var c=l(n,\"HEAPF32\");return u=e.PathEffect._MakeDash(c,n.length,u),s(c,n),u},e.PathEffect.MakeLine2D=function(n,u){return u=h(u),e.PathEffect._MakeLine2D(n,u)},e.PathEffect.MakePath2D=function(n,u){return n=h(n),e.PathEffect._MakePath2D(n,u)},e.Shader.MakeColor=function(n,u){return u=u||null,n=P(n),e.Shader._MakeColor(n,u)},e.Shader.Blend=e.Shader.MakeBlend,e.Shader.Color=e.Shader.MakeColor,e.Shader.MakeLinearGradient=function(n,u,c,y,_,C,F,D){D=D||null;var I=f(c),$=l(y,\"HEAPF32\");F=F||0,C=h(C);var V=de.toTypedArray();return V.set(n),V.set(u,2),n=e.Shader._MakeLinearGradient(N,I.Nd,I.colorType,$,I.count,_,F,C,D),s(I.Nd,c),y&&s($,y),n},e.Shader.MakeRadialGradient=function(n,u,c,y,_,C,F,D){D=D||null;var I=f(c),$=l(y,\"HEAPF32\");return F=F||0,C=h(C),n=e.Shader._MakeRadialGradient(n[0],n[1],u,I.Nd,I.colorType,$,I.count,_,F,C,D),s(I.Nd,c),y&&s($,y),n},e.Shader.MakeSweepGradient=function(n,u,c,y,_,C,F,D,I,$){$=$||null;var V=f(c),d=l(y,\"HEAPF32\");return F=F||0,D=D||0,I=I||360,C=h(C),n=e.Shader._MakeSweepGradient(n,u,V.Nd,V.colorType,d,V.count,_,D,I,F,C,$),s(V.Nd,c),y&&s(d,y),n},e.Shader.MakeTwoPointConicalGradient=function(n,u,c,y,_,C,F,D,I,$){$=$||null;var V=f(_),d=l(C,\"HEAPF32\");I=I||0,D=h(D);var A=de.toTypedArray();return A.set(n),A.set(c,2),n=e.Shader._MakeTwoPointConicalGradient(N,u,y,V.Nd,V.colorType,d,V.count,F,I,D,$),s(V.Nd,_),C&&s(d,C),n},e.Vertices.prototype.bounds=function(n){this._bounds(N);var u=de.toTypedArray();return n?(n.set(u),n):u.slice()},e.Id&&e.Id.forEach(function(n){n()})},e.computeTonalColors=function(a){var n=l(a.ambient,\"HEAPF32\"),u=l(a.spot,\"HEAPF32\");this._computeTonalColors(n,u);var c={ambient:T(n),spot:T(u)};return s(n,a.ambient),s(u,a.spot),c},e.LTRBRect=function(a,n,u,c){return Float32Array.of(a,n,u,c)},e.XYWHRect=function(a,n,u,c){return Float32Array.of(a,n,a+u,n+c)},e.LTRBiRect=function(a,n,u,c){return Int32Array.of(a,n,u,c)},e.XYWHiRect=function(a,n,u,c){return Int32Array.of(a,n,a+u,n+c)},e.RRectXY=function(a,n,u){return Float32Array.of(a[0],a[1],a[2],a[3],n,u,n,u,n,u,n,u)},e.MakeAnimatedImageFromEncoded=function(a){a=new Uint8Array(a);var n=e._malloc(a.byteLength);return e.HEAPU8.set(a,n),(a=e._decodeAnimatedImage(n,a.byteLength))?a:null},e.MakeImageFromEncoded=function(a){a=new Uint8Array(a);var n=e._malloc(a.byteLength);return e.HEAPU8.set(a,n),(a=e._decodeImage(n,a.byteLength))?a:null};var qe=null;e.MakeImageFromCanvasImageSource=function(a){var n=a.width,u=a.height;qe||(qe=document.createElement(\"canvas\")),qe.width=n,qe.height=u;var c=qe.getContext(\"2d\",{willReadFrequently:!0});return c.drawImage(a,0,0),a=c.getImageData(0,0,n,u),e.MakeImage({width:n,height:u,alphaType:e.AlphaType.Unpremul,colorType:e.ColorType.RGBA_8888,colorSpace:e.ColorSpace.SRGB},a.data,4*n)},e.MakeImage=function(a,n,u){var c=e._malloc(n.length);return e.HEAPU8.set(n,c),e._MakeImage(a,c,n.length,u)},e.MakeVertices=function(a,n,u,c,y,_){var C=y&&y.length||0,F=0;return u&&u.length&&(F|=1),c&&c.length&&(F|=2),_===void 0||_||(F|=4),a=new e._VerticesBuilder(a,n.length/2,C,F),l(n,\"HEAPF32\",a.positions()),a.texCoords()&&l(u,\"HEAPF32\",a.texCoords()),a.colors()&&l(r(c),\"HEAPU32\",a.colors()),a.indices()&&l(y,\"HEAPU16\",a.indices()),a.detach()},function(a){a.Id=a.Id||[],a.Id.push(function(){function n(d){return d&&(d.dir=d.dir===0?a.TextDirection.RTL:a.TextDirection.LTR),d}function u(d){if(!d||!d.length)return[];for(var A=[],U=0;U<d.length;U+=5){var X=a.LTRBRect(d[U],d[U+1],d[U+2],d[U+3]),Ce=a.TextDirection.LTR;d[U+4]===0&&(Ce=a.TextDirection.RTL),A.push({rect:X,dir:Ce})}return a._free(d.byteOffset),A}function c(d){return d=d||{},d.weight===void 0&&(d.weight=a.FontWeight.Normal),d.width=d.width||a.FontWidth.Normal,d.slant=d.slant||a.FontSlant.Upright,d}function y(d){if(!d||!d.length)return L;for(var A=[],U=0;U<d.length;U++){var X=_(d[U]);A.push(X)}return l(A,\"HEAPU32\")}function _(d){if(D[d])return D[d];var A=le(d)+1,U=a._malloc(A);return se(d,G,U,A),D[d]=U}function C(d){if(d._colorPtr=P(d.color),d._foregroundColorPtr=L,d._backgroundColorPtr=L,d._decorationColorPtr=L,d.foregroundColor&&(d._foregroundColorPtr=P(d.foregroundColor,I)),d.backgroundColor&&(d._backgroundColorPtr=P(d.backgroundColor,$)),d.decorationColor&&(d._decorationColorPtr=P(d.decorationColor,V)),Array.isArray(d.fontFamilies)&&d.fontFamilies.length?(d._fontFamiliesPtr=y(d.fontFamilies),d._fontFamiliesLen=d.fontFamilies.length):(d._fontFamiliesPtr=L,d._fontFamiliesLen=0),d.locale){var A=d.locale;d._localePtr=_(A),d._localeLen=le(A)+1}else d._localePtr=L,d._localeLen=0;if(Array.isArray(d.shadows)&&d.shadows.length){A=d.shadows;var U=A.map(function(he){return he.color||a.BLACK}),X=A.map(function(he){return he.blurRadius||0});d._shadowLen=A.length;for(var Ce=a._malloc(8*A.length),Yt=Ce/4,Xt=0;Xt<A.length;Xt++){var Jr=A[Xt].offset||[0,0];a.HEAPF32[Yt]=Jr[0],a.HEAPF32[Yt+1]=Jr[1],Yt+=2}d._shadowColorsPtr=f(U).Nd,d._shadowOffsetsPtr=Ce,d._shadowBlurRadiiPtr=l(X,\"HEAPF32\")}else d._shadowLen=0,d._shadowColorsPtr=L,d._shadowOffsetsPtr=L,d._shadowBlurRadiiPtr=L;Array.isArray(d.fontFeatures)&&d.fontFeatures.length?(A=d.fontFeatures,U=A.map(function(he){return he.name}),X=A.map(function(he){return he.value}),d._fontFeatureLen=A.length,d._fontFeatureNamesPtr=y(U),d._fontFeatureValuesPtr=l(X,\"HEAPU32\")):(d._fontFeatureLen=0,d._fontFeatureNamesPtr=L,d._fontFeatureValuesPtr=L),Array.isArray(d.fontVariations)&&d.fontVariations.length?(A=d.fontVariations,U=A.map(function(he){return he.axis}),X=A.map(function(he){return he.value}),d._fontVariationLen=A.length,d._fontVariationAxesPtr=y(U),d._fontVariationValuesPtr=l(X,\"HEAPF32\")):(d._fontVariationLen=0,d._fontVariationAxesPtr=L,d._fontVariationValuesPtr=L)}function F(d){a._free(d._fontFamiliesPtr),a._free(d._shadowColorsPtr),a._free(d._shadowOffsetsPtr),a._free(d._shadowBlurRadiiPtr),a._free(d._fontFeatureNamesPtr),a._free(d._fontFeatureValuesPtr),a._free(d._fontVariationAxesPtr),a._free(d._fontVariationValuesPtr)}a.Paragraph.prototype.getRectsForRange=function(d,A,U,X){return d=this._getRectsForRange(d,A,U,X),u(d)},a.Paragraph.prototype.getRectsForPlaceholders=function(){var d=this._getRectsForPlaceholders();return u(d)},a.Paragraph.prototype.getGlyphInfoAt=function(d){return n(this._getGlyphInfoAt(d))},a.Paragraph.prototype.getClosestGlyphInfoAtCoordinate=function(d,A){return n(this._getClosestGlyphInfoAtCoordinate(d,A))},a.TypefaceFontProvider.prototype.registerFont=function(d,A){if(d=a.Typeface.MakeFreeTypeFaceFromData(d),!d)return null;A=_(A),this._registerFont(d,A)},a.ParagraphStyle=function(d){if(d.disableHinting=d.disableHinting||!1,d.ellipsis){var A=d.ellipsis;d._ellipsisPtr=_(A),d._ellipsisLen=le(A)+1}else d._ellipsisPtr=L,d._ellipsisLen=0;return d.heightMultiplier==null&&(d.heightMultiplier=-1),d.maxLines=d.maxLines||0,d.replaceTabCharacters=d.replaceTabCharacters||!1,A=(A=d.strutStyle)||{},A.strutEnabled=A.strutEnabled||!1,A.strutEnabled&&Array.isArray(A.fontFamilies)&&A.fontFamilies.length?(A._fontFamiliesPtr=y(A.fontFamilies),A._fontFamiliesLen=A.fontFamilies.length):(A._fontFamiliesPtr=L,A._fontFamiliesLen=0),A.fontStyle=c(A.fontStyle),A.fontSize==null&&(A.fontSize=-1),A.heightMultiplier==null&&(A.heightMultiplier=-1),A.halfLeading=A.halfLeading||!1,A.leading=A.leading||0,A.forceStrutHeight=A.forceStrutHeight||!1,d.strutStyle=A,d.textAlign=d.textAlign||a.TextAlign.Start,d.textDirection=d.textDirection||a.TextDirection.LTR,d.textHeightBehavior=d.textHeightBehavior||a.TextHeightBehavior.All,d.textStyle=a.TextStyle(d.textStyle),d.applyRoundingHack=d.applyRoundingHack!==!1,d},a.TextStyle=function(d){return d.color||(d.color=a.BLACK),d.decoration=d.decoration||0,d.decorationThickness=d.decorationThickness||0,d.decorationStyle=d.decorationStyle||a.DecorationStyle.Solid,d.textBaseline=d.textBaseline||a.TextBaseline.Alphabetic,d.fontSize==null&&(d.fontSize=-1),d.letterSpacing=d.letterSpacing||0,d.wordSpacing=d.wordSpacing||0,d.heightMultiplier==null&&(d.heightMultiplier=-1),d.halfLeading=d.halfLeading||!1,d.fontStyle=c(d.fontStyle),d};var D={},I=a._malloc(16),$=a._malloc(16),V=a._malloc(16);a.ParagraphBuilder.Make=function(d,A){return C(d.textStyle),A=a.ParagraphBuilder._Make(d,A),F(d.textStyle),A},a.ParagraphBuilder.MakeFromFontProvider=function(d,A){return C(d.textStyle),A=a.ParagraphBuilder._MakeFromFontProvider(d,A),F(d.textStyle),A},a.ParagraphBuilder.MakeFromFontCollection=function(d,A){return C(d.textStyle),A=a.ParagraphBuilder._MakeFromFontCollection(d,A),F(d.textStyle),A},a.ParagraphBuilder.ShapeText=function(d,A,U){let X=0;for(let Ce of A)X+=Ce.length;if(X!==d.length)throw\"Accumulated block lengths must equal text.length\";return a.ParagraphBuilder._ShapeText(d,A,U)},a.ParagraphBuilder.prototype.pushStyle=function(d){C(d),this._pushStyle(d),F(d)},a.ParagraphBuilder.prototype.pushPaintStyle=function(d,A,U){C(d),this._pushPaintStyle(d,A,U),F(d)},a.ParagraphBuilder.prototype.addPlaceholder=function(d,A,U,X,Ce){U=U||a.PlaceholderAlignment.Baseline,X=X||a.TextBaseline.Alphabetic,this._addPlaceholder(d||0,A||0,U,X,Ce||0)},a.ParagraphBuilder.prototype.setWordsUtf8=function(d){var A=l(d,\"HEAPU32\");this._setWordsUtf8(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setWordsUtf16=function(d){var A=l(d,\"HEAPU32\");this._setWordsUtf16(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setGraphemeBreaksUtf8=function(d){var A=l(d,\"HEAPU32\");this._setGraphemeBreaksUtf8(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setGraphemeBreaksUtf16=function(d){var A=l(d,\"HEAPU32\");this._setGraphemeBreaksUtf16(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setLineBreaksUtf8=function(d){var A=l(d,\"HEAPU32\");this._setLineBreaksUtf8(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setLineBreaksUtf16=function(d){var A=l(d,\"HEAPU32\");this._setLineBreaksUtf16(A,d&&d.length||0),s(A,d)}})}(m),e.Id=e.Id||[],e.Id.push(function(){e.Path.prototype.op=function(a,n){return this._op(a,n)?this:null},e.Path.prototype.simplify=function(){return this._simplify()?this:null}}),e.Id=e.Id||[],e.Id.push(function(){e.Canvas.prototype.drawText=function(a,n,u,c,y){var _=le(a),C=e._malloc(_+1);se(a,G,C,_+1),this._drawSimpleText(C,_,n,u,y,c),e._free(C)},e.Canvas.prototype.drawGlyphs=function(a,n,u,c,y,_){if(!(2*a.length<=n.length))throw\"Not enough positions for the array of gyphs\";e.Fd(this.Ed);let C=l(a,\"HEAPU16\"),F=l(n,\"HEAPF32\");this._drawGlyphs(a.length,C,F,u,c,y,_),s(F,n),s(C,a)},e.Font.prototype.getGlyphBounds=function(a,n,u){var c=l(a,\"HEAPU16\"),y=e._malloc(16*a.length);return this._getGlyphWidthBounds(c,a.length,L,y,n||null),n=new Float32Array(e.HEAPU8.buffer,y,4*a.length),s(c,a),u?(u.set(n),e._free(y),u):(a=Float32Array.from(n),e._free(y),a)},e.Font.prototype.getGlyphIDs=function(a,n,u){n||(n=a.length);var c=le(a)+1,y=e._malloc(c);return se(a,G,y,c),a=e._malloc(2*n),n=this._getGlyphIDs(y,c-1,n,a),e._free(y),0>n?(e._free(a),null):(y=new Uint16Array(e.HEAPU8.buffer,a,n),u?(u.set(y),e._free(a),u):(u=Uint16Array.from(y),e._free(a),u))},e.Font.prototype.getGlyphIntercepts=function(a,n,u,c){var y=l(a,\"HEAPU16\"),_=l(n,\"HEAPF32\");return this._getGlyphIntercepts(y,a.length,!(a&&a._ck),_,n.length,!(n&&n._ck),u,c)},e.Font.prototype.getGlyphWidths=function(a,n,u){var c=l(a,\"HEAPU16\"),y=e._malloc(4*a.length);return this._getGlyphWidthBounds(c,a.length,y,L,n||null),n=new Float32Array(e.HEAPU8.buffer,y,a.length),s(c,a),u?(u.set(n),e._free(y),u):(a=Float32Array.from(n),e._free(y),a)},e.FontMgr.FromData=function(){if(!arguments.length)return null;var a=arguments;if(a.length===1&&Array.isArray(a[0])&&(a=arguments[0]),!a.length)return null;for(var n=[],u=[],c=0;c<a.length;c++){var y=new Uint8Array(a[c]),_=l(y,\"HEAPU8\");n.push(_),u.push(y.byteLength)}return n=l(n,\"HEAPU32\"),u=l(u,\"HEAPU32\"),a=e.FontMgr._fromData(n,u,a.length),e._free(n),e._free(u),a},e.Typeface.MakeFreeTypeFaceFromData=function(a){a=new Uint8Array(a);var n=l(a,\"HEAPU8\");return(a=e.Typeface._MakeFreeTypeFaceFromData(n,a.byteLength))?a:null},e.Typeface.prototype.getGlyphIDs=function(a,n,u){n||(n=a.length);var c=le(a)+1,y=e._malloc(c);return se(a,G,y,c),a=e._malloc(2*n),n=this._getGlyphIDs(y,c-1,n,a),e._free(y),0>n?(e._free(a),null):(y=new Uint16Array(e.HEAPU8.buffer,a,n),u?(u.set(y),e._free(a),u):(u=Uint16Array.from(y),e._free(a),u))},e.TextBlob.MakeOnPath=function(a,n,u,c){if(a&&a.length&&n&&n.countPoints()){if(n.countPoints()===1)return this.MakeFromText(a,u);c||(c=0);var y=u.getGlyphIDs(a);y=u.getGlyphWidths(y);var _=[];n=new e.ContourMeasureIter(n,!1,1);for(var C=n.next(),F=new Float32Array(4),D=0;D<a.length&&C;D++){var I=y[D];if(c+=I/2,c>C.length()){if(C.delete(),C=n.next(),!C){a=a.substring(0,D);break}c=I/2}C.getPosTan(c,F);var $=F[2],V=F[3];_.push($,V,F[0]-I/2*$,F[1]-I/2*V),c+=I/2}return a=this.MakeFromRSXform(a,_,u),C&&C.delete(),n.delete(),a}},e.TextBlob.MakeFromRSXform=function(a,n,u){var c=le(a)+1,y=e._malloc(c);return se(a,G,y,c),a=l(n,\"HEAPF32\"),u=e.TextBlob._MakeFromRSXform(y,c-1,a,u),e._free(y),u||null},e.TextBlob.MakeFromRSXformGlyphs=function(a,n,u){var c=l(a,\"HEAPU16\");return n=l(n,\"HEAPF32\"),u=e.TextBlob._MakeFromRSXformGlyphs(c,2*a.length,n,u),s(c,a),u||null},e.TextBlob.MakeFromGlyphs=function(a,n){var u=l(a,\"HEAPU16\");return n=e.TextBlob._MakeFromGlyphs(u,2*a.length,n),s(u,a),n||null},e.TextBlob.MakeFromText=function(a,n){var u=le(a)+1,c=e._malloc(u);return se(a,G,c,u),a=e.TextBlob._MakeFromText(c,u-1,n),e._free(c),a||null},e.MallocGlyphIDs=function(a){return e.Malloc(Uint16Array,a)}}),e.Id=e.Id||[],e.Id.push(function(){e.MakePicture=function(a){a=new Uint8Array(a);var n=e._malloc(a.byteLength);return e.HEAPU8.set(a,n),(a=e._MakePicture(n,a.byteLength))?a:null}}),e.Id=e.Id||[],e.Id.push(function(){e.RuntimeEffect.Make=function(a,n){return e.RuntimeEffect._Make(a,{onError:n||function(u){console.log(\"RuntimeEffect error\",u)}})},e.RuntimeEffect.MakeForBlender=function(a,n){return e.RuntimeEffect._MakeForBlender(a,{onError:n||function(u){console.log(\"RuntimeEffect error\",u)}})},e.RuntimeEffect.prototype.makeShader=function(a,n){var u=!a._ck,c=l(a,\"HEAPF32\");return n=h(n),this._makeShader(c,4*a.length,u,n)},e.RuntimeEffect.prototype.makeShaderWithChildren=function(a,n,u){var c=!a._ck,y=l(a,\"HEAPF32\");u=h(u);for(var _=[],C=0;C<n.length;C++)_.push(n[C].Dd.Hd);return n=l(_,\"HEAPU32\"),this._makeShaderWithChildren(y,4*a.length,c,n,_.length,u)},e.RuntimeEffect.prototype.makeBlender=function(a){var n=!a._ck,u=l(a,\"HEAPF32\");return this._makeBlender(u,4*a.length,n)}})}(m);var Qt=Object.assign({},m),wt=\"./this.program\",Zt=typeof window==\"object\",Oe=typeof importScripts==\"function\",zt=typeof process==\"object\"&&typeof process.versions==\"object\"&&typeof process.versions.node==\"string\",z=\"\",Tt,tt,rt;if(zt){var qt=Qr(\"fs\"),Ft=Qr(\"path\");z=Oe?Ft.dirname(z)+\"/\":__dirname+\"/\",Tt=(e,t)=>(e=e.startsWith(\"file://\")?new URL(e):Ft.normalize(e),qt.readFileSync(e,t?void 0:\"utf8\")),rt=e=>(e=Tt(e,!0),e.buffer||(e=new Uint8Array(e)),e),tt=(e,t,r,i=!0)=>{e=e.startsWith(\"file://\")?new URL(e):Ft.normalize(e),qt.readFile(e,i?void 0:\"utf8\",(o,s)=>{o?r(o):t(i?s.buffer:s)})},!m.thisProgram&&1<process.argv.length&&(wt=process.argv[1].replace(/\\\\/g,\"/\")),process.argv.slice(2),m.inspect=()=>\"[Emscripten Module object]\"}else(Zt||Oe)&&(Oe?z=self.location.href:typeof document<\"u\"&&document.currentScript&&(z=document.currentScript.src),ae&&(z=ae),z.indexOf(\"blob:\")!==0?z=z.substr(0,z.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1):z=\"\",Tt=e=>{var t=new XMLHttpRequest;return t.open(\"GET\",e,!1),t.send(null),t.responseText},Oe&&(rt=e=>{var t=new XMLHttpRequest;return t.open(\"GET\",e,!1),t.responseType=\"arraybuffer\",t.send(null),new Uint8Array(t.response)}),tt=(e,t,r)=>{var i=new XMLHttpRequest;i.open(\"GET\",e,!0),i.responseType=\"arraybuffer\",i.onload=()=>{i.status==200||i.status==0&&i.response?t(i.response):r()},i.onerror=r,i.send(null)});var qr=m.print||console.log.bind(console),Ae=m.printErr||console.error.bind(console);Object.assign(m,Qt),Qt=null,m.thisProgram&&(wt=m.thisProgram);var je;m.wasmBinary&&(je=m.wasmBinary);var Qn=m.noExitRuntime||!0;typeof WebAssembly!=\"object\"&&Rt(\"no native wasm support detected\");var nt,b,er=!1,pe,G,Ee,Be,w,O,R,tr;function rr(){var e=nt.buffer;m.HEAP8=pe=new Int8Array(e),m.HEAP16=Ee=new Int16Array(e),m.HEAP32=w=new Int32Array(e),m.HEAPU8=G=new Uint8Array(e),m.HEAPU16=Be=new Uint16Array(e),m.HEAPU32=O=new Uint32Array(e),m.HEAPF32=R=new Float32Array(e),m.HEAPF64=tr=new Float64Array(e)}var Q,nr=[],ir=[],or=[];function en(){var e=m.preRun.shift();nr.unshift(e)}var Me=0,Mt=null,We=null;function Rt(e){throw m.onAbort&&m.onAbort(e),e=\"Aborted(\"+e+\")\",Ae(e),er=!0,e=new WebAssembly.RuntimeError(e+\". Build with -sASSERTIONS for more info.\"),et(e),e}function ar(e){return e.startsWith(\"data:application/octet-stream;base64,\")}var Ge;if(Ge=\"canvaskit.wasm\",!ar(Ge)){var ur=Ge;Ge=m.locateFile?m.locateFile(ur,z):z+ur}function sr(e){if(e==Ge&&je)return new Uint8Array(je);if(rt)return rt(e);throw\"both async and sync fetching of the wasm failed\"}function tn(e){if(!je&&(Zt||Oe)){if(typeof fetch==\"function\"&&!e.startsWith(\"file://\"))return fetch(e,{credentials:\"same-origin\"}).then(t=>{if(!t.ok)throw\"failed to load wasm binary file at '\"+e+\"'\";return t.arrayBuffer()}).catch(()=>sr(e));if(tt)return new Promise((t,r)=>{tt(e,i=>t(new Uint8Array(i)),r)})}return Promise.resolve().then(()=>sr(e))}function lr(e,t,r){return tn(e).then(i=>WebAssembly.instantiate(i,t)).then(i=>i).then(r,i=>{Ae(\"failed to asynchronously prepare wasm: \"+i),Rt(i)})}function rn(e,t){var r=Ge;return je||typeof WebAssembly.instantiateStreaming!=\"function\"||ar(r)||r.startsWith(\"file://\")||zt||typeof fetch!=\"function\"?lr(r,e,t):fetch(r,{credentials:\"same-origin\"}).then(i=>WebAssembly.instantiateStreaming(i,e).then(t,function(o){return Ae(\"wasm streaming compile failed: \"+o),Ae(\"falling back to ArrayBuffer instantiation\"),lr(r,e,t)}))}var xt=e=>{for(;0<e.length;)e.shift()(m)},fr=typeof TextDecoder<\"u\"?new TextDecoder(\"utf8\"):void 0,Re=(e,t,r)=>{var i=t+r;for(r=t;e[r]&&!(r>=i);)++r;if(16<r-t&&e.buffer&&fr)return fr.decode(e.subarray(t,r));for(i=\"\";t<r;){var o=e[t++];if(o&128){var s=e[t++]&63;if((o&224)==192)i+=String.fromCharCode((o&31)<<6|s);else{var l=e[t++]&63;o=(o&240)==224?(o&15)<<12|s<<6|l:(o&7)<<18|s<<12|l<<6|e[t++]&63,65536>o?i+=String.fromCharCode(o):(o-=65536,i+=String.fromCharCode(55296|o>>10,56320|o&1023))}}else i+=String.fromCharCode(o)}return i},it={};function It(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function $e(e){return this.fromWireType(w[e>>2])}var Le={},xe={},ot={},cr=void 0;function at(e){throw new cr(e)}function ue(e,t,r){function i(f){f=r(f),f.length!==e.length&&at(\"Mismatched type converter count\");for(var h=0;h<e.length;++h)ye(e[h],f[h])}e.forEach(function(f){ot[f]=t});var o=Array(t.length),s=[],l=0;t.forEach((f,h)=>{xe.hasOwnProperty(f)?o[h]=xe[f]:(s.push(f),Le.hasOwnProperty(f)||(Le[f]=[]),Le[f].push(()=>{o[h]=xe[f],++l,l===s.length&&i(o)}))}),s.length===0&&i(o)}function ut(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${e}`)}}var dr=void 0;function S(e){for(var t=\"\";G[e];)t+=dr[G[e++]];return t}var be=void 0;function k(e){throw new be(e)}function nn(e,t,r={}){var i=t.name;if(e||k(`type \"${i}\" must have a positive integer typeid pointer`),xe.hasOwnProperty(e)){if(r.af)return;k(`Cannot register type '${i}' twice`)}xe[e]=t,delete ot[e],Le.hasOwnProperty(e)&&(t=Le[e],delete Le[e],t.forEach(o=>o()))}function ye(e,t,r={}){if(!(\"argPackAdvance\"in t))throw new TypeError(\"registerType registeredInstance requires argPackAdvance\");nn(e,t,r)}function Dt(e){k(e.Dd.Jd.Gd.name+\" instance already deleted\")}var Bt=!1;function hr(){}function pr(e){--e.count.value,e.count.value===0&&(e.Ld?e.Pd.Td(e.Ld):e.Jd.Gd.Td(e.Hd))}function yr(e,t,r){return t===r?e:r.Md===void 0?null:(e=yr(e,t,r.Md),e===null?null:r.Te(e))}var vr={},Se=[];function Gt(){for(;Se.length;){var e=Se.pop();e.Dd.$d=!1,e.delete()}}var Ve=void 0,Ne={};function on(e,t){for(t===void 0&&k(\"ptr should not be undefined\");e.Md;)t=e.ge(t),e=e.Md;return Ne[t]}function st(e,t){return t.Jd&&t.Hd||at(\"makeClassHandle requires ptr and ptrType\"),!!t.Pd!=!!t.Ld&&at(\"Both smartPtrType and smartPtr must be specified\"),t.count={value:1},Ye(Object.create(e,{Dd:{value:t}}))}function Ye(e){return typeof FinalizationRegistry>\"u\"?(Ye=t=>t,e):(Bt=new FinalizationRegistry(t=>{pr(t.Dd)}),Ye=t=>{var r=t.Dd;return r.Ld&&Bt.register(t,{Dd:r},t),t},hr=t=>{Bt.unregister(t)},Ye(e))}function we(){}function mr(e){if(e===void 0)return\"_unknown\";e=e.replace(/[^a-zA-Z0-9_]/g,\"$\");var t=e.charCodeAt(0);return 48<=t&&57>=t?`_${e}`:e}function Lt(e,t){return e=mr(e),{[e]:function(){return t.apply(this,arguments)}}[e]}function bt(e,t,r){if(e[t].Kd===void 0){var i=e[t];e[t]=function(){return e[t].Kd.hasOwnProperty(arguments.length)||k(`Function '${r}' called with an invalid number of arguments (${arguments.length}) - expects one of (${e[t].Kd})!`),e[t].Kd[arguments.length].apply(this,arguments)},e[t].Kd=[],e[t].Kd[i.Yd]=i}}function kt(e,t,r){m.hasOwnProperty(e)?((r===void 0||m[e].Kd!==void 0&&m[e].Kd[r]!==void 0)&&k(`Cannot register public name '${e}' twice`),bt(m,e,e),m.hasOwnProperty(r)&&k(`Cannot register multiple overloads of a function with the same number of arguments (${r})!`),m[e].Kd[r]=t):(m[e]=t,r!==void 0&&(m[e].sf=r))}function an(e,t,r,i,o,s,l,f){this.name=e,this.constructor=t,this.ae=r,this.Td=i,this.Md=o,this.We=s,this.ge=l,this.Te=f,this.ef=[]}function Ht(e,t,r){for(;t!==r;)t.ge||k(`Expected null or instance of ${r.name}, got an instance of ${t.name}`),e=t.ge(e),t=t.Md;return e}function un(e,t){return t===null?(this.ue&&k(`null is not a valid ${this.name}`),0):(t.Dd||k(`Cannot pass \"${Ot(t)}\" as a ${this.name}`),t.Dd.Hd||k(`Cannot pass deleted object as a pointer of type ${this.name}`),Ht(t.Dd.Hd,t.Dd.Jd.Gd,this.Gd))}function sn(e,t){if(t===null){if(this.ue&&k(`null is not a valid ${this.name}`),this.le){var r=this.ve();return e!==null&&e.push(this.Td,r),r}return 0}if(t.Dd||k(`Cannot pass \"${Ot(t)}\" as a ${this.name}`),t.Dd.Hd||k(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.ke&&t.Dd.Jd.ke&&k(`Cannot convert argument of type ${t.Dd.Pd?t.Dd.Pd.name:t.Dd.Jd.name} to parameter type ${this.name}`),r=Ht(t.Dd.Hd,t.Dd.Jd.Gd,this.Gd),this.le)switch(t.Dd.Ld===void 0&&k(\"Passing raw pointer to smart pointer is illegal\"),this.kf){case 0:t.Dd.Pd===this?r=t.Dd.Ld:k(`Cannot convert argument of type ${t.Dd.Pd?t.Dd.Pd.name:t.Dd.Jd.name} to parameter type ${this.name}`);break;case 1:r=t.Dd.Ld;break;case 2:if(t.Dd.Pd===this)r=t.Dd.Ld;else{var i=t.clone();r=this.ff(r,te(function(){i.delete()})),e!==null&&e.push(this.Td,r)}break;default:k(\"Unsupporting sharing policy\")}return r}function ln(e,t){return t===null?(this.ue&&k(`null is not a valid ${this.name}`),0):(t.Dd||k(`Cannot pass \"${Ot(t)}\" as a ${this.name}`),t.Dd.Hd||k(`Cannot pass deleted object as a pointer of type ${this.name}`),t.Dd.Jd.ke&&k(`Cannot convert argument of type ${t.Dd.Jd.name} to parameter type ${this.name}`),Ht(t.Dd.Hd,t.Dd.Jd.Gd,this.Gd))}function ve(e,t,r,i,o,s,l,f,h,g,P){this.name=e,this.Gd=t,this.ue=r,this.ke=i,this.le=o,this.df=s,this.kf=l,this.Ee=f,this.ve=h,this.ff=g,this.Td=P,o||t.Md!==void 0?this.toWireType=sn:(this.toWireType=i?un:ln,this.Od=null)}function _r(e,t,r){m.hasOwnProperty(e)||at(\"Replacing nonexistant public symbol\"),m[e].Kd!==void 0&&r!==void 0?m[e].Kd[r]=t:(m[e]=t,m[e].Yd=r)}var fn=(e,t)=>{var r=[];return function(){if(r.length=0,Object.assign(r,arguments),e.includes(\"j\")){var i=m[\"dynCall_\"+e];i=r&&r.length?i.apply(null,[t].concat(r)):i.call(null,t)}else i=Q.get(t).apply(null,r);return i}};function K(e,t){e=S(e);var r=e.includes(\"j\")?fn(e,t):Q.get(t);return typeof r!=\"function\"&&k(`unknown function pointer with signature ${e}: ${t}`),r}var gr=void 0;function Pr(e){e=Wr(e);var t=S(e);return Pe(e),t}function Xe(e,t){function r(s){o[s]||xe[s]||(ot[s]?ot[s].forEach(r):(i.push(s),o[s]=!0))}var i=[],o={};throw t.forEach(r),new gr(`${e}: `+i.map(Pr).join([\", \"]))}function lt(e,t,r,i,o){var s=t.length;2>s&&k(\"argTypes array size mismatch! Must at least get return value and 'this' types!\");var l=t[1]!==null&&r!==null,f=!1;for(r=1;r<t.length;++r)if(t[r]!==null&&t[r].Od===void 0){f=!0;break}var h=t[0].name!==\"void\",g=s-2,P=Array(g),E=[],T=[];return function(){if(arguments.length!==g&&k(`function ${e} called with ${arguments.length} arguments, expected ${g} args!`),T.length=0,E.length=l?2:1,E[0]=o,l){var v=t[1].toWireType(T,this);E[1]=v}for(var M=0;M<g;++M)P[M]=t[M+2].toWireType(T,arguments[M]),E.push(P[M]);if(M=i.apply(null,E),f)It(T);else for(var x=l?1:2;x<t.length;x++){var W=x===1?v:P[x-2];t[x].Od!==null&&t[x].Od(W)}return v=h?t[0].fromWireType(M):void 0,v}}function ft(e,t){for(var r=[],i=0;i<e;i++)r.push(O[t+4*i>>2]);return r}function Cr(){this.Sd=[void 0],this.Ce=[]}var q=new Cr;function Ut(e){e>=q.be&&--q.get(e).Fe===0&&q.Je(e)}var ee=e=>(e||k(\"Cannot use deleted val. handle = \"+e),q.get(e).value),te=e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return q.Ie({Fe:1,value:e})}};function cn(e,t,r){switch(t){case 0:return function(i){return this.fromWireType((r?pe:G)[i])};case 1:return function(i){return this.fromWireType((r?Ee:Be)[i>>1])};case 2:return function(i){return this.fromWireType((r?w:O)[i>>2])};default:throw new TypeError(\"Unknown integer type: \"+e)}}function Ke(e,t){var r=xe[e];return r===void 0&&k(t+\" has unknown type \"+Pr(e)),r}function Ot(e){if(e===null)return\"null\";var t=typeof e;return t===\"object\"||t===\"array\"||t===\"function\"?e.toString():\"\"+e}function dn(e,t){switch(t){case 2:return function(r){return this.fromWireType(R[r>>2])};case 3:return function(r){return this.fromWireType(tr[r>>3])};default:throw new TypeError(\"Unknown float type: \"+e)}}function hn(e,t,r){switch(t){case 0:return r?function(i){return pe[i]}:function(i){return G[i]};case 1:return r?function(i){return Ee[i>>1]}:function(i){return Be[i>>1]};case 2:return r?function(i){return w[i>>2]}:function(i){return O[i>>2]};default:throw new TypeError(\"Unknown integer type: \"+e)}}var se=(e,t,r,i)=>{if(!(0<i))return 0;var o=r;i=r+i-1;for(var s=0;s<e.length;++s){var l=e.charCodeAt(s);if(55296<=l&&57343>=l){var f=e.charCodeAt(++s);l=65536+((l&1023)<<10)|f&1023}if(127>=l){if(r>=i)break;t[r++]=l}else{if(2047>=l){if(r+1>=i)break;t[r++]=192|l>>6}else{if(65535>=l){if(r+2>=i)break;t[r++]=224|l>>12}else{if(r+3>=i)break;t[r++]=240|l>>18,t[r++]=128|l>>12&63}t[r++]=128|l>>6&63}t[r++]=128|l&63}}return t[r]=0,r-o},le=e=>{for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);127>=i?t++:2047>=i?t+=2:55296<=i&&57343>=i?(t+=4,++r):t+=3}return t},Ar=typeof TextDecoder<\"u\"?new TextDecoder(\"utf-16le\"):void 0,pn=(e,t)=>{for(var r=e>>1,i=r+t/2;!(r>=i)&&Be[r];)++r;if(r<<=1,32<r-e&&Ar)return Ar.decode(G.subarray(e,r));for(r=\"\",i=0;!(i>=t/2);++i){var o=Ee[e+2*i>>1];if(o==0)break;r+=String.fromCharCode(o)}return r},yn=(e,t,r)=>{if(r===void 0&&(r=2147483647),2>r)return 0;r-=2;var i=t;r=r<2*e.length?r/2:e.length;for(var o=0;o<r;++o)Ee[t>>1]=e.charCodeAt(o),t+=2;return Ee[t>>1]=0,t-i},vn=e=>2*e.length,mn=(e,t)=>{for(var r=0,i=\"\";!(r>=t/4);){var o=w[e+4*r>>2];if(o==0)break;++r,65536<=o?(o-=65536,i+=String.fromCharCode(55296|o>>10,56320|o&1023)):i+=String.fromCharCode(o)}return i},_n=(e,t,r)=>{if(r===void 0&&(r=2147483647),4>r)return 0;var i=t;r=i+r-4;for(var o=0;o<e.length;++o){var s=e.charCodeAt(o);if(55296<=s&&57343>=s){var l=e.charCodeAt(++o);s=65536+((s&1023)<<10)|l&1023}if(w[t>>2]=s,t+=4,t+4>r)break}return w[t>>2]=0,t-i},gn=e=>{for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);55296<=i&&57343>=i&&++r,t+=4}return t},Pn={};function ct(e){var t=Pn[e];return t===void 0?S(e):t}var dt=[];function Er(){function e(t){t.$$$embind_global$$$=t;var r=typeof $$$embind_global$$$==\"object\"&&t.$$$embind_global$$$==t;return r||delete t.$$$embind_global$$$,r}if(typeof globalThis==\"object\")return globalThis;if(typeof $$$embind_global$$$==\"object\"||(typeof global==\"object\"&&e(global)?$$$embind_global$$$=global:typeof self==\"object\"&&e(self)&&($$$embind_global$$$=self),typeof $$$embind_global$$$==\"object\"))return $$$embind_global$$$;throw Error(\"unable to get global object.\")}function Cn(e){var t=dt.length;return dt.push(e),t}function An(e,t){for(var r=Array(e),i=0;i<e;++i)r[i]=Ke(O[t+4*i>>2],\"parameter \"+i);return r}var wr=[];function En(e){var t=Array(e+1);return function(r,i,o){t[0]=r;for(var s=0;s<e;++s){var l=Ke(O[i+4*s>>2],\"parameter \"+s);t[s+1]=l.readValueFromPointer(o),o+=l.argPackAdvance}return r=new(r.bind.apply(r,t)),te(r)}}var Tr={};function wn(e){var t=e.getExtension(\"ANGLE_instanced_arrays\");t&&(e.vertexAttribDivisor=function(r,i){t.vertexAttribDivisorANGLE(r,i)},e.drawArraysInstanced=function(r,i,o,s){t.drawArraysInstancedANGLE(r,i,o,s)},e.drawElementsInstanced=function(r,i,o,s,l){t.drawElementsInstancedANGLE(r,i,o,s,l)})}function Tn(e){var t=e.getExtension(\"OES_vertex_array_object\");t&&(e.createVertexArray=function(){return t.createVertexArrayOES()},e.deleteVertexArray=function(r){t.deleteVertexArrayOES(r)},e.bindVertexArray=function(r){t.bindVertexArrayOES(r)},e.isVertexArray=function(r){return t.isVertexArrayOES(r)})}function Fn(e){var t=e.getExtension(\"WEBGL_draw_buffers\");t&&(e.drawBuffers=function(r,i){t.drawBuffersWEBGL(r,i)})}var Fr=1,ht=[],fe=[],pt=[],Je=[],re=[],ce=[],yt=[],me=[],Ie=[],De=[],Mr={},Rr={},xr=4;function j(e){vt||(vt=e)}function ke(e){for(var t=Fr++,r=e.length;r<t;r++)e[r]=null;return t}function Mn(e,t){e.be||(e.be=e.getContext,e.getContext=function(i,o){return o=e.be(i,o),i==\"webgl\"==o instanceof WebGLRenderingContext?o:null});var r=1<t.majorVersion?e.getContext(\"webgl2\",t):e.getContext(\"webgl\",t);return r?Rn(r,t):0}function Rn(e,t){var r=ke(me),i={handle:r,attributes:t,version:t.majorVersion,Qd:e};return e.canvas&&(e.canvas.Ke=i),me[r]=i,(typeof t.Ue>\"u\"||t.Ue)&&xn(i),r}function Ir(e){return B=me[e],m.qf=p=B&&B.Qd,!(e&&!p)}function xn(e){if(e||(e=B),!e.bf){e.bf=!0;var t=e.Qd;wn(t),Tn(t),Fn(t),t.ze=t.getExtension(\"WEBGL_draw_instanced_base_vertex_base_instance\"),t.De=t.getExtension(\"WEBGL_multi_draw_instanced_base_vertex_base_instance\"),2<=e.version&&(t.Ae=t.getExtension(\"EXT_disjoint_timer_query_webgl2\")),(2>e.version||!t.Ae)&&(t.Ae=t.getExtension(\"EXT_disjoint_timer_query\")),t.rf=t.getExtension(\"WEBGL_multi_draw\"),(t.getSupportedExtensions()||[]).forEach(function(r){r.includes(\"lose_context\")||r.includes(\"debug\")||t.getExtension(r)})}}var B,vt,jt={},Dr=()=>{if(!Wt){var e={USER:\"web_user\",LOGNAME:\"web_user\",PATH:\"/\",PWD:\"/\",HOME:\"/home/<USER>",LANG:(typeof navigator==\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\",_:wt||\"./this.program\"},t;for(t in jt)jt[t]===void 0?delete e[t]:e[t]=jt[t];var r=[];for(t in e)r.push(`${t}=${e[t]}`);Wt=r}return Wt},Wt,In=[null,[],[]];function Br(e){p.bindVertexArray(yt[e])}function Gr(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2];p.deleteVertexArray(yt[i]),yt[i]=null}}var mt=[];function Lr(e,t,r,i){p.drawElements(e,t,r,i)}function He(e,t,r,i){for(var o=0;o<e;o++){var s=p[r](),l=s&&ke(i);s?(s.name=l,i[l]=s):j(1282),w[t+4*o>>2]=l}}function br(e,t){He(e,t,\"createVertexArray\",yt)}function kr(e,t,r){if(t){var i=void 0;switch(e){case 36346:i=1;break;case 36344:r!=0&&r!=1&&j(1280);return;case 34814:case 36345:i=0;break;case 34466:var o=p.getParameter(34467);i=o?o.length:0;break;case 33309:if(2>B.version){j(1282);return}i=2*(p.getSupportedExtensions()||[]).length;break;case 33307:case 33308:if(2>B.version){j(1280);return}i=e==33307?3:0}if(i===void 0)switch(o=p.getParameter(e),typeof o){case\"number\":i=o;break;case\"boolean\":i=o?1:0;break;case\"string\":j(1280);return;case\"object\":if(o===null)switch(e){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:i=0;break;default:j(1280);return}else{if(o instanceof Float32Array||o instanceof Uint32Array||o instanceof Int32Array||o instanceof Array){for(e=0;e<o.length;++e)switch(r){case 0:w[t+4*e>>2]=o[e];break;case 2:R[t+4*e>>2]=o[e];break;case 4:pe[t+e>>0]=o[e]?1:0}return}try{i=o.name|0}catch(s){j(1280),Ae(\"GL_INVALID_ENUM in glGet\"+r+\"v: Unknown object returned from WebGL getParameter(\"+e+\")! (error: \"+s+\")\");return}}break;default:j(1280),Ae(\"GL_INVALID_ENUM in glGet\"+r+\"v: Native code calling glGet\"+r+\"v(\"+e+\") and it returns \"+o+\" of type \"+typeof o+\"!\");return}switch(r){case 1:r=i,O[t>>2]=r,O[t+4>>2]=(r-O[t>>2])/4294967296;break;case 0:w[t>>2]=i;break;case 2:R[t>>2]=i;break;case 4:pe[t>>0]=i?1:0}}else j(1281)}var Qe=e=>{var t=le(e)+1,r=Ct(t);return r&&se(e,G,r,t),r};function Hr(e){return e.slice(-1)==\"]\"&&e.lastIndexOf(\"[\")}function _t(e){return e-=5120,e==0?pe:e==1?G:e==2?Ee:e==4?w:e==6?R:e==5||e==28922||e==28520||e==30779||e==30782?O:Be}function $t(e,t,r,i,o){e=_t(e);var s=31-Math.clz32(e.BYTES_PER_ELEMENT),l=xr;return e.subarray(o>>s,o+i*(r*({5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4}[t-6402]||1)*(1<<s)+l-1&-l)>>s)}function H(e){var t=p.Re;if(t){var r=t.fe[e];return typeof r==\"number\"&&(t.fe[e]=r=p.getUniformLocation(t,t.Ge[e]+(0<r?\"[\"+r+\"]\":\"\"))),r}j(1282)}var Te=[],Ze=[],gt=e=>e%4===0&&(e%100!==0||e%400===0),Ur=[31,29,31,30,31,30,31,31,30,31,30,31],Or=[31,28,31,30,31,30,31,31,30,31,30,31];function Dn(e){var t=Array(le(e)+1);return se(e,t,0,t.length),t}var Bn=(e,t,r,i)=>{function o(v,M,x){for(v=typeof v==\"number\"?v.toString():v||\"\";v.length<M;)v=x[0]+v;return v}function s(v,M){return o(v,M,\"0\")}function l(v,M){function x(J){return 0>J?-1:0<J?1:0}var W;return(W=x(v.getFullYear()-M.getFullYear()))===0&&(W=x(v.getMonth()-M.getMonth()))===0&&(W=x(v.getDate()-M.getDate())),W}function f(v){switch(v.getDay()){case 0:return new Date(v.getFullYear()-1,11,29);case 1:return v;case 2:return new Date(v.getFullYear(),0,3);case 3:return new Date(v.getFullYear(),0,2);case 4:return new Date(v.getFullYear(),0,1);case 5:return new Date(v.getFullYear()-1,11,31);case 6:return new Date(v.getFullYear()-1,11,30)}}function h(v){var M=v.Vd;for(v=new Date(new Date(v.Wd+1900,0,1).getTime());0<M;){var x=v.getMonth(),W=(gt(v.getFullYear())?Ur:Or)[x];if(M>W-v.getDate())M-=W-v.getDate()+1,v.setDate(1),11>x?v.setMonth(x+1):(v.setMonth(0),v.setFullYear(v.getFullYear()+1));else{v.setDate(v.getDate()+M);break}}return x=new Date(v.getFullYear()+1,0,4),M=f(new Date(v.getFullYear(),0,4)),x=f(x),0>=l(M,v)?0>=l(x,v)?v.getFullYear()+1:v.getFullYear():v.getFullYear()-1}var g=w[i+40>>2];i={nf:w[i>>2],mf:w[i+4>>2],pe:w[i+8>>2],we:w[i+12>>2],qe:w[i+16>>2],Wd:w[i+20>>2],Rd:w[i+24>>2],Vd:w[i+28>>2],uf:w[i+32>>2],lf:w[i+36>>2],pf:g&&g?Re(G,g):\"\"},r=r?Re(G,r):\"\",g={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var P in g)r=r.replace(new RegExp(P,\"g\"),g[P]);var E=\"Sunday Monday Tuesday Wednesday Thursday Friday Saturday\".split(\" \"),T=\"January February March April May June July August September October November December\".split(\" \");g={\"%a\":v=>E[v.Rd].substring(0,3),\"%A\":v=>E[v.Rd],\"%b\":v=>T[v.qe].substring(0,3),\"%B\":v=>T[v.qe],\"%C\":v=>s((v.Wd+1900)/100|0,2),\"%d\":v=>s(v.we,2),\"%e\":v=>o(v.we,2,\" \"),\"%g\":v=>h(v).toString().substring(2),\"%G\":v=>h(v),\"%H\":v=>s(v.pe,2),\"%I\":v=>(v=v.pe,v==0?v=12:12<v&&(v-=12),s(v,2)),\"%j\":v=>{for(var M=0,x=0;x<=v.qe-1;M+=(gt(v.Wd+1900)?Ur:Or)[x++]);return s(v.we+M,3)},\"%m\":v=>s(v.qe+1,2),\"%M\":v=>s(v.mf,2),\"%n\":()=>`\n`,\"%p\":v=>0<=v.pe&&12>v.pe?\"AM\":\"PM\",\"%S\":v=>s(v.nf,2),\"%t\":()=>\"\t\",\"%u\":v=>v.Rd||7,\"%U\":v=>s(Math.floor((v.Vd+7-v.Rd)/7),2),\"%V\":v=>{var M=Math.floor((v.Vd+7-(v.Rd+6)%7)/7);if(2>=(v.Rd+371-v.Vd-2)%7&&M++,M)M==53&&(x=(v.Rd+371-v.Vd)%7,x==4||x==3&&gt(v.Wd)||(M=1));else{M=52;var x=(v.Rd+7-v.Vd-1)%7;(x==4||x==5&&gt(v.Wd%400-1))&&M++}return s(M,2)},\"%w\":v=>v.Rd,\"%W\":v=>s(Math.floor((v.Vd+7-(v.Rd+6)%7)/7),2),\"%y\":v=>(v.Wd+1900).toString().substring(2),\"%Y\":v=>v.Wd+1900,\"%z\":v=>{v=v.lf;var M=0<=v;return v=Math.abs(v)/60,(M?\"+\":\"-\")+String(\"0000\"+(v/60*100+v%60)).slice(-4)},\"%Z\":v=>v.pf,\"%%\":()=>\"%\"},r=r.replace(/%%/g,\"\\0\\0\");for(P in g)r.includes(P)&&(r=r.replace(new RegExp(P,\"g\"),g[P](i)));return r=r.replace(/\\0\\0/g,\"%\"),P=Dn(r),P.length>t?0:(pe.set(P,e),P.length-1)};cr=m.InternalError=class extends Error{constructor(e){super(e),this.name=\"InternalError\"}};for(var jr=Array(256),Pt=0;256>Pt;++Pt)jr[Pt]=String.fromCharCode(Pt);dr=jr,be=m.BindingError=class extends Error{constructor(e){super(e),this.name=\"BindingError\"}},we.prototype.isAliasOf=function(e){if(!(this instanceof we&&e instanceof we))return!1;var t=this.Dd.Jd.Gd,r=this.Dd.Hd,i=e.Dd.Jd.Gd;for(e=e.Dd.Hd;t.Md;)r=t.ge(r),t=t.Md;for(;i.Md;)e=i.ge(e),i=i.Md;return t===i&&r===e},we.prototype.clone=function(){if(this.Dd.Hd||Dt(this),this.Dd.ee)return this.Dd.count.value+=1,this;var e=Ye,t=Object,r=t.create,i=Object.getPrototypeOf(this),o=this.Dd;return e=e(r.call(t,i,{Dd:{value:{count:o.count,$d:o.$d,ee:o.ee,Hd:o.Hd,Jd:o.Jd,Ld:o.Ld,Pd:o.Pd}}})),e.Dd.count.value+=1,e.Dd.$d=!1,e},we.prototype.delete=function(){this.Dd.Hd||Dt(this),this.Dd.$d&&!this.Dd.ee&&k(\"Object already scheduled for deletion\"),hr(this),pr(this.Dd),this.Dd.ee||(this.Dd.Ld=void 0,this.Dd.Hd=void 0)},we.prototype.isDeleted=function(){return!this.Dd.Hd},we.prototype.deleteLater=function(){return this.Dd.Hd||Dt(this),this.Dd.$d&&!this.Dd.ee&&k(\"Object already scheduled for deletion\"),Se.push(this),Se.length===1&&Ve&&Ve(Gt),this.Dd.$d=!0,this},m.getInheritedInstanceCount=function(){return Object.keys(Ne).length},m.getLiveInheritedInstances=function(){var e=[],t;for(t in Ne)Ne.hasOwnProperty(t)&&e.push(Ne[t]);return e},m.flushPendingDeletes=Gt,m.setDelayFunction=function(e){Ve=e,Se.length&&Ve&&Ve(Gt)},ve.prototype.Xe=function(e){return this.Ee&&(e=this.Ee(e)),e},ve.prototype.ye=function(e){this.Td&&this.Td(e)},ve.prototype.argPackAdvance=8,ve.prototype.readValueFromPointer=$e,ve.prototype.deleteObject=function(e){e!==null&&e.delete()},ve.prototype.fromWireType=function(e){function t(){return this.le?st(this.Gd.ae,{Jd:this.df,Hd:r,Pd:this,Ld:e}):st(this.Gd.ae,{Jd:this,Hd:e})}var r=this.Xe(e);if(!r)return this.ye(e),null;var i=on(this.Gd,r);if(i!==void 0)return i.Dd.count.value===0?(i.Dd.Hd=r,i.Dd.Ld=e,i.clone()):(i=i.clone(),this.ye(e),i);if(i=this.Gd.We(r),i=vr[i],!i)return t.call(this);i=this.ke?i.Qe:i.pointerType;var o=yr(r,this.Gd,i.Gd);return o===null?t.call(this):this.le?st(i.Gd.ae,{Jd:i,Hd:o,Pd:this,Ld:e}):st(i.Gd.ae,{Jd:i,Hd:o})},gr=m.UnboundTypeError=function(e,t){var r=Lt(t,function(i){this.name=t,this.message=i,i=Error(i).stack,i!==void 0&&(this.stack=this.toString()+`\n`+i.replace(/^Error(:[^\\n]*)?\\n/,\"\"))});return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return this.message===void 0?this.name:`${this.name}: ${this.message}`},r}(Error,\"UnboundTypeError\"),Object.assign(Cr.prototype,{get(e){return this.Sd[e]},has(e){return this.Sd[e]!==void 0},Ie(e){var t=this.Ce.pop()||this.Sd.length;return this.Sd[t]=e,t},Je(e){this.Sd[e]=void 0,this.Ce.push(e)}}),q.Sd.push({value:void 0},{value:null},{value:!0},{value:!1}),q.be=q.Sd.length,m.count_emval_handles=function(){for(var e=0,t=q.be;t<q.Sd.length;++t)q.Sd[t]!==void 0&&++e;return e};for(var p,Z=0;32>Z;++Z)mt.push(Array(Z));var Gn=new Float32Array(288);for(Z=0;288>Z;++Z)Te[Z]=Gn.subarray(0,Z+1);var Ln=new Int32Array(288);for(Z=0;288>Z;++Z)Ze[Z]=Ln.subarray(0,Z+1);var bn={Q:function(){return 0},Ab:()=>{},Cb:function(){return 0},xb:()=>{},yb:()=>{},R:function(){},zb:()=>{},v:function(e){var t=it[e];delete it[e];var r=t.ve,i=t.Td,o=t.Be,s=o.map(l=>l.$e).concat(o.map(l=>l.hf));ue([e],s,l=>{var f={};return o.forEach((h,g)=>{var P=l[g],E=h.Ye,T=h.Ze,v=l[g+o.length],M=h.gf,x=h.jf;f[h.Ve]={read:W=>P.fromWireType(E(T,W)),write:(W,J)=>{var Y=[];M(x,W,v.toWireType(Y,J)),It(Y)}}}),[{name:t.name,fromWireType:function(h){var g={},P;for(P in f)g[P]=f[P].read(h);return i(h),g},toWireType:function(h,g){for(var P in f)if(!(P in g))throw new TypeError(`Missing field: \"${P}\"`);var E=r();for(P in f)f[P].write(E,g[P]);return h!==null&&h.push(i,E),E},argPackAdvance:8,readValueFromPointer:$e,Od:i}]})},pb:function(){},Gb:function(e,t,r,i,o){var s=ut(r);t=S(t),ye(e,{name:t,fromWireType:function(l){return!!l},toWireType:function(l,f){return f?i:o},argPackAdvance:8,readValueFromPointer:function(l){if(r===1)var f=pe;else if(r===2)f=Ee;else if(r===4)f=w;else throw new TypeError(\"Unknown boolean type size: \"+t);return this.fromWireType(f[l>>s])},Od:null})},k:function(e,t,r,i,o,s,l,f,h,g,P,E,T){P=S(P),s=K(o,s),f&&(f=K(l,f)),g&&(g=K(h,g)),T=K(E,T);var v=mr(P);kt(v,function(){Xe(`Cannot construct ${P} due to unbound types`,[i])}),ue([e,t,r],i?[i]:[],function(M){if(M=M[0],i)var x=M.Gd,W=x.ae;else W=we.prototype;M=Lt(v,function(){if(Object.getPrototypeOf(this)!==J)throw new be(\"Use 'new' to construct \"+P);if(Y.Ud===void 0)throw new be(P+\" has no accessible constructor\");var Ue=Y.Ud[arguments.length];if(Ue===void 0)throw new be(`Tried to invoke ctor of ${P} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(Y.Ud).toString()}) parameters instead!`);return Ue.apply(this,arguments)});var J=Object.create(W,{constructor:{value:M}});M.prototype=J;var Y=new an(P,M,J,T,x,s,f,g);Y.Md&&(Y.Md.he===void 0&&(Y.Md.he=[]),Y.Md.he.push(Y)),x=new ve(P,Y,!0,!1,!1),W=new ve(P+\"*\",Y,!1,!1,!1);var _e=new ve(P+\" const*\",Y,!1,!0,!1);return vr[e]={pointerType:W,Qe:_e},_r(v,M),[x,W,_e]})},f:function(e,t,r,i,o,s,l){var f=ft(r,i);t=S(t),s=K(o,s),ue([],[e],function(h){function g(){Xe(`Cannot call ${P} due to unbound types`,f)}h=h[0];var P=`${h.name}.${t}`;t.startsWith(\"@@\")&&(t=Symbol[t.substring(2)]);var E=h.Gd.constructor;return E[t]===void 0?(g.Yd=r-1,E[t]=g):(bt(E,t,P),E[t].Kd[r-1]=g),ue([],f,function(T){if(T=[T[0],null].concat(T.slice(1)),T=lt(P,T,null,s,l),E[t].Kd===void 0?(T.Yd=r-1,E[t]=T):E[t].Kd[r-1]=T,h.Gd.he)for(let v of h.Gd.he)v.constructor.hasOwnProperty(t)||(v.constructor[t]=T);return[]}),[]})},t:function(e,t,r,i,o,s){var l=ft(t,r);o=K(i,o),ue([],[e],function(f){f=f[0];var h=`constructor ${f.name}`;if(f.Gd.Ud===void 0&&(f.Gd.Ud=[]),f.Gd.Ud[t-1]!==void 0)throw new be(`Cannot register multiple constructors with identical number of parameters (${t-1}) for class '${f.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return f.Gd.Ud[t-1]=()=>{Xe(`Cannot construct ${f.name} due to unbound types`,l)},ue([],l,function(g){return g.splice(1,0,null),f.Gd.Ud[t-1]=lt(h,g,null,o,s),[]}),[]})},b:function(e,t,r,i,o,s,l,f){var h=ft(r,i);t=S(t),s=K(o,s),ue([],[e],function(g){function P(){Xe(`Cannot call ${E} due to unbound types`,h)}g=g[0];var E=`${g.name}.${t}`;t.startsWith(\"@@\")&&(t=Symbol[t.substring(2)]),f&&g.Gd.ef.push(t);var T=g.Gd.ae,v=T[t];return v===void 0||v.Kd===void 0&&v.className!==g.name&&v.Yd===r-2?(P.Yd=r-2,P.className=g.name,T[t]=P):(bt(T,t,E),T[t].Kd[r-2]=P),ue([],h,function(M){return M=lt(E,M,g,s,l),T[t].Kd===void 0?(M.Yd=r-2,T[t]=M):T[t].Kd[r-2]=M,[]}),[]})},o:function(e,t,r){e=S(e),ue([],[t],function(i){return i=i[0],m[e]=i.fromWireType(r),[]})},Fb:function(e,t){t=S(t),ye(e,{name:t,fromWireType:function(r){var i=ee(r);return Ut(r),i},toWireType:function(r,i){return te(i)},argPackAdvance:8,readValueFromPointer:$e,Od:null})},j:function(e,t,r,i){function o(){}r=ut(r),t=S(t),o.values={},ye(e,{name:t,constructor:o,fromWireType:function(s){return this.constructor.values[s]},toWireType:function(s,l){return l.value},argPackAdvance:8,readValueFromPointer:cn(t,r,i),Od:null}),kt(t,o)},c:function(e,t,r){var i=Ke(e,\"enum\");t=S(t),e=i.constructor,i=Object.create(i.constructor.prototype,{value:{value:r},constructor:{value:Lt(`${i.name}_${t}`,function(){})}}),e.values[r]=i,e[t]=i},T:function(e,t,r){r=ut(r),t=S(t),ye(e,{name:t,fromWireType:function(i){return i},toWireType:function(i,o){return o},argPackAdvance:8,readValueFromPointer:dn(t,r),Od:null})},r:function(e,t,r,i,o,s){var l=ft(t,r);e=S(e),o=K(i,o),kt(e,function(){Xe(`Cannot call ${e} due to unbound types`,l)},t-1),ue([],l,function(f){return f=[f[0],null].concat(f.slice(1)),_r(e,lt(e,f,null,o,s),t-1),[]})},x:function(e,t,r,i,o){t=S(t),o===-1&&(o=4294967295),o=ut(r);var s=f=>f;if(i===0){var l=32-8*r;s=f=>f<<l>>>l}r=t.includes(\"unsigned\")?function(f,h){return h>>>0}:function(f,h){return h},ye(e,{name:t,fromWireType:s,toWireType:r,argPackAdvance:8,readValueFromPointer:hn(t,o,i!==0),Od:null})},n:function(e,t,r){function i(s){s>>=2;var l=O;return new o(l.buffer,l[s+1],l[s])}var o=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];r=S(r),ye(e,{name:r,fromWireType:i,argPackAdvance:8,readValueFromPointer:i},{af:!0})},m:function(e,t,r,i,o,s,l,f,h,g,P,E){r=S(r),s=K(o,s),f=K(l,f),g=K(h,g),E=K(P,E),ue([e],[t],function(T){return T=T[0],[new ve(r,T.Gd,!1,!1,!0,T,i,s,f,g,E)]})},S:function(e,t){t=S(t);var r=t===\"std::string\";ye(e,{name:t,fromWireType:function(i){var o=O[i>>2],s=i+4;if(r)for(var l=s,f=0;f<=o;++f){var h=s+f;if(f==o||G[h]==0){if(l=l?Re(G,l,h-l):\"\",g===void 0)var g=l;else g+=String.fromCharCode(0),g+=l;l=h+1}}else{for(g=Array(o),f=0;f<o;++f)g[f]=String.fromCharCode(G[s+f]);g=g.join(\"\")}return Pe(i),g},toWireType:function(i,o){o instanceof ArrayBuffer&&(o=new Uint8Array(o));var s=typeof o==\"string\";s||o instanceof Uint8Array||o instanceof Uint8ClampedArray||o instanceof Int8Array||k(\"Cannot pass non-string to std::string\");var l=r&&s?le(o):o.length,f=Ct(4+l+1),h=f+4;if(O[f>>2]=l,r&&s)se(o,G,h,l+1);else if(s)for(s=0;s<l;++s){var g=o.charCodeAt(s);255<g&&(Pe(h),k(\"String has UTF-16 code units that do not fit in 8 bits\")),G[h+s]=g}else for(s=0;s<l;++s)G[h+s]=o[s];return i!==null&&i.push(Pe,f),f},argPackAdvance:8,readValueFromPointer:$e,Od:function(i){Pe(i)}})},K:function(e,t,r){if(r=S(r),t===2)var i=pn,o=yn,s=vn,l=()=>Be,f=1;else t===4&&(i=mn,o=_n,s=gn,l=()=>O,f=2);ye(e,{name:r,fromWireType:function(h){for(var g=O[h>>2],P=l(),E,T=h+4,v=0;v<=g;++v){var M=h+4+v*t;(v==g||P[M>>f]==0)&&(T=i(T,M-T),E===void 0?E=T:(E+=String.fromCharCode(0),E+=T),T=M+t)}return Pe(h),E},toWireType:function(h,g){typeof g!=\"string\"&&k(`Cannot pass non-string to C++ string type ${r}`);var P=s(g),E=Ct(4+P+t);return O[E>>2]=P>>f,o(g,E+4,P+t),h!==null&&h.push(Pe,E),E},argPackAdvance:8,readValueFromPointer:$e,Od:function(h){Pe(h)}})},w:function(e,t,r,i,o,s){it[e]={name:S(t),ve:K(r,i),Td:K(o,s),Be:[]}},e:function(e,t,r,i,o,s,l,f,h,g){it[e].Be.push({Ve:S(t),$e:r,Ye:K(i,o),Ze:s,hf:l,gf:K(f,h),jf:g})},Hb:function(e,t){t=S(t),ye(e,{cf:!0,name:t,argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},Eb:()=>!0,tb:()=>{throw 1/0},y:function(e,t,r){e=ee(e),t=Ke(t,\"emval::as\");var i=[],o=te(i);return O[r>>2]=o,t.toWireType(i,e)},Y:function(e,t,r,i,o){e=dt[e],t=ee(t),r=ct(r);var s=[];return O[i>>2]=te(s),e(t,r,s,o)},q:function(e,t,r,i){e=dt[e],t=ee(t),r=ct(r),e(t,r,null,i)},d:Ut,H:function(e){return e===0?te(Er()):(e=ct(e),te(Er()[e]))},p:function(e,t){var r=An(e,t),i=r[0];t=i.name+\"_$\"+r.slice(1).map(function(l){return l.name}).join(\"_\")+\"$\";var o=wr[t];if(o!==void 0)return o;var s=Array(e-1);return o=Cn((l,f,h,g)=>{for(var P=0,E=0;E<e-1;++E)s[E]=r[E+1].readValueFromPointer(g+P),P+=r[E+1].argPackAdvance;for(l=l[f].apply(l,s),E=0;E<e-1;++E)r[E+1].Se&&r[E+1].Se(s[E]);if(!i.cf)return i.toWireType(h,l)}),wr[t]=o},s:function(e,t){return e=ee(e),t=ee(t),te(e[t])},l:function(e){4<e&&(q.get(e).Fe+=1)},G:function(e,t,r,i){e=ee(e);var o=Tr[t];return o||(o=En(t),Tr[t]=o),o(e,r,i)},C:function(){return te([])},g:function(e){return te(ct(e))},z:function(){return te({})},jb:function(e){return e=ee(e),!e},u:function(e){var t=ee(e);It(t),Ut(e)},i:function(e,t,r){e=ee(e),t=ee(t),r=ee(r),e[t]=r},h:function(e,t){return e=Ke(e,\"_emval_take_value\"),e=e.readValueFromPointer(t),te(e)},mb:function(){return-52},nb:function(){},a:()=>{Rt(\"\")},Db:()=>performance.now(),ub:e=>{var t=G.length;if(e>>>=0,2147483648<e)return!1;for(var r=1;4>=r;r*=2){var i=t*(1+.2/r);i=Math.min(i,e+100663296);var o=Math;i=Math.max(e,i);e:{o=o.min.call(o,2147483648,i+(65536-i%65536)%65536)-nt.buffer.byteLength+65535>>>16;try{nt.grow(o),rr();var s=1;break e}catch{}s=void 0}if(s)return!0}return!1},kb:function(){return B?B.handle:0},vb:(e,t)=>{var r=0;return Dr().forEach(function(i,o){var s=t+r;for(o=O[e+4*o>>2]=s,s=0;s<i.length;++s)pe[o++>>0]=i.charCodeAt(s);pe[o>>0]=0,r+=i.length+1}),0},wb:(e,t)=>{var r=Dr();O[e>>2]=r.length;var i=0;return r.forEach(function(o){i+=o.length+1}),O[t>>2]=i,0},J:()=>52,lb:function(){return 52},Bb:()=>52,ob:function(){return 70},P:(e,t,r,i)=>{for(var o=0,s=0;s<r;s++){var l=O[t>>2],f=O[t+4>>2];t+=8;for(var h=0;h<f;h++){var g=G[l+h],P=In[e];g===0||g===10?((e===1?qr:Ae)(Re(P,0)),P.length=0):P.push(g)}o+=f}return O[i>>2]=o,0},$:function(e){p.activeTexture(e)},aa:function(e,t){p.attachShader(fe[e],ce[t])},ba:function(e,t,r){p.bindAttribLocation(fe[e],t,r?Re(G,r):\"\")},ca:function(e,t){e==35051?p.se=t:e==35052&&(p.Zd=t),p.bindBuffer(e,ht[t])},_:function(e,t){p.bindFramebuffer(e,pt[t])},ac:function(e,t){p.bindRenderbuffer(e,Je[t])},Mb:function(e,t){p.bindSampler(e,Ie[t])},da:function(e,t){p.bindTexture(e,re[t])},uc:Br,xc:Br,ea:function(e,t,r,i){p.blendColor(e,t,r,i)},fa:function(e){p.blendEquation(e)},ga:function(e,t){p.blendFunc(e,t)},Wb:function(e,t,r,i,o,s,l,f,h,g){p.blitFramebuffer(e,t,r,i,o,s,l,f,h,g)},ha:function(e,t,r,i){2<=B.version?r&&t?p.bufferData(e,G,i,r,t):p.bufferData(e,t,i):p.bufferData(e,r?G.subarray(r,r+t):t,i)},ia:function(e,t,r,i){2<=B.version?r&&p.bufferSubData(e,t,G,i,r):p.bufferSubData(e,t,G.subarray(i,i+r))},bc:function(e){return p.checkFramebufferStatus(e)},N:function(e){p.clear(e)},Z:function(e,t,r,i){p.clearColor(e,t,r,i)},O:function(e){p.clearStencil(e)},rb:function(e,t,r,i){return p.clientWaitSync(De[e],t,(r>>>0)+4294967296*i)},ja:function(e,t,r,i){p.colorMask(!!e,!!t,!!r,!!i)},ka:function(e){p.compileShader(ce[e])},la:function(e,t,r,i,o,s,l,f){2<=B.version?p.Zd||!l?p.compressedTexImage2D(e,t,r,i,o,s,l,f):p.compressedTexImage2D(e,t,r,i,o,s,G,f,l):p.compressedTexImage2D(e,t,r,i,o,s,f?G.subarray(f,f+l):null)},ma:function(e,t,r,i,o,s,l,f,h){2<=B.version?p.Zd||!f?p.compressedTexSubImage2D(e,t,r,i,o,s,l,f,h):p.compressedTexSubImage2D(e,t,r,i,o,s,l,G,h,f):p.compressedTexSubImage2D(e,t,r,i,o,s,l,h?G.subarray(h,h+f):null)},Ub:function(e,t,r,i,o){p.copyBufferSubData(e,t,r,i,o)},na:function(e,t,r,i,o,s,l,f){p.copyTexSubImage2D(e,t,r,i,o,s,l,f)},oa:function(){var e=ke(fe),t=p.createProgram();return t.name=e,t.oe=t.me=t.ne=0,t.xe=1,fe[e]=t,e},pa:function(e){var t=ke(ce);return ce[t]=p.createShader(e),t},qa:function(e){p.cullFace(e)},ra:function(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2],o=ht[i];o&&(p.deleteBuffer(o),o.name=0,ht[i]=null,i==p.se&&(p.se=0),i==p.Zd&&(p.Zd=0))}},cc:function(e,t){for(var r=0;r<e;++r){var i=w[t+4*r>>2],o=pt[i];o&&(p.deleteFramebuffer(o),o.name=0,pt[i]=null)}},sa:function(e){if(e){var t=fe[e];t?(p.deleteProgram(t),t.name=0,fe[e]=null):j(1281)}},dc:function(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2],o=Je[i];o&&(p.deleteRenderbuffer(o),o.name=0,Je[i]=null)}},Nb:function(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2],o=Ie[i];o&&(p.deleteSampler(o),o.name=0,Ie[i]=null)}},ta:function(e){if(e){var t=ce[e];t?(p.deleteShader(t),ce[e]=null):j(1281)}},Vb:function(e){if(e){var t=De[e];t?(p.deleteSync(t),t.name=0,De[e]=null):j(1281)}},ua:function(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2],o=re[i];o&&(p.deleteTexture(o),o.name=0,re[i]=null)}},vc:Gr,yc:Gr,va:function(e){p.depthMask(!!e)},wa:function(e){p.disable(e)},xa:function(e){p.disableVertexAttribArray(e)},ya:function(e,t,r){p.drawArrays(e,t,r)},sc:function(e,t,r,i){p.drawArraysInstanced(e,t,r,i)},qc:function(e,t,r,i,o){p.ze.drawArraysInstancedBaseInstanceWEBGL(e,t,r,i,o)},oc:function(e,t){for(var r=mt[e],i=0;i<e;i++)r[i]=w[t+4*i>>2];p.drawBuffers(r)},za:Lr,tc:function(e,t,r,i,o){p.drawElementsInstanced(e,t,r,i,o)},rc:function(e,t,r,i,o,s,l){p.ze.drawElementsInstancedBaseVertexBaseInstanceWEBGL(e,t,r,i,o,s,l)},ic:function(e,t,r,i,o,s){Lr(e,i,o,s)},Aa:function(e){p.enable(e)},Ba:function(e){p.enableVertexAttribArray(e)},Sb:function(e,t){return(e=p.fenceSync(e,t))?(t=ke(De),e.name=t,De[t]=e,t):0},Ca:function(){p.finish()},Da:function(){p.flush()},ec:function(e,t,r,i){p.framebufferRenderbuffer(e,t,r,Je[i])},fc:function(e,t,r,i,o){p.framebufferTexture2D(e,t,r,re[i],o)},Ea:function(e){p.frontFace(e)},Fa:function(e,t){He(e,t,\"createBuffer\",ht)},gc:function(e,t){He(e,t,\"createFramebuffer\",pt)},hc:function(e,t){He(e,t,\"createRenderbuffer\",Je)},Ob:function(e,t){He(e,t,\"createSampler\",Ie)},Ga:function(e,t){He(e,t,\"createTexture\",re)},wc:br,zc:br,Yb:function(e){p.generateMipmap(e)},Ha:function(e,t,r){r?w[r>>2]=p.getBufferParameter(e,t):j(1281)},Ia:function(){var e=p.getError()||vt;return vt=0,e},Ja:function(e,t){kr(e,t,2)},Zb:function(e,t,r,i){e=p.getFramebufferAttachmentParameter(e,t,r),(e instanceof WebGLRenderbuffer||e instanceof WebGLTexture)&&(e=e.name|0),w[i>>2]=e},I:function(e,t){kr(e,t,0)},Ka:function(e,t,r,i){e=p.getProgramInfoLog(fe[e]),e===null&&(e=\"(unknown error)\"),t=0<t&&i?se(e,G,i,t):0,r&&(w[r>>2]=t)},La:function(e,t,r){if(r)if(e>=Fr)j(1281);else if(e=fe[e],t==35716)e=p.getProgramInfoLog(e),e===null&&(e=\"(unknown error)\"),w[r>>2]=e.length+1;else if(t==35719){if(!e.oe)for(t=0;t<p.getProgramParameter(e,35718);++t)e.oe=Math.max(e.oe,p.getActiveUniform(e,t).name.length+1);w[r>>2]=e.oe}else if(t==35722){if(!e.me)for(t=0;t<p.getProgramParameter(e,35721);++t)e.me=Math.max(e.me,p.getActiveAttrib(e,t).name.length+1);w[r>>2]=e.me}else if(t==35381){if(!e.ne)for(t=0;t<p.getProgramParameter(e,35382);++t)e.ne=Math.max(e.ne,p.getActiveUniformBlockName(e,t).length+1);w[r>>2]=e.ne}else w[r>>2]=p.getProgramParameter(e,t);else j(1281)},_b:function(e,t,r){r?w[r>>2]=p.getRenderbufferParameter(e,t):j(1281)},Ma:function(e,t,r,i){e=p.getShaderInfoLog(ce[e]),e===null&&(e=\"(unknown error)\"),t=0<t&&i?se(e,G,i,t):0,r&&(w[r>>2]=t)},Jb:function(e,t,r,i){e=p.getShaderPrecisionFormat(e,t),w[r>>2]=e.rangeMin,w[r+4>>2]=e.rangeMax,w[i>>2]=e.precision},Na:function(e,t,r){r?t==35716?(e=p.getShaderInfoLog(ce[e]),e===null&&(e=\"(unknown error)\"),w[r>>2]=e?e.length+1:0):t==35720?(e=p.getShaderSource(ce[e]),w[r>>2]=e?e.length+1:0):w[r>>2]=p.getShaderParameter(ce[e],t):j(1281)},M:function(e){var t=Mr[e];if(!t){switch(e){case 7939:t=p.getSupportedExtensions()||[],t=t.concat(t.map(function(i){return\"GL_\"+i})),t=Qe(t.join(\" \"));break;case 7936:case 7937:case 37445:case 37446:(t=p.getParameter(e))||j(1280),t=t&&Qe(t);break;case 7938:t=p.getParameter(7938),t=2<=B.version?\"OpenGL ES 3.0 (\"+t+\")\":\"OpenGL ES 2.0 (\"+t+\")\",t=Qe(t);break;case 35724:t=p.getParameter(35724);var r=t.match(/^WebGL GLSL ES ([0-9]\\.[0-9][0-9]?)(?:$| .*)/);r!==null&&(r[1].length==3&&(r[1]+=\"0\"),t=\"OpenGL ES GLSL ES \"+r[1]+\" (\"+t+\")\"),t=Qe(t);break;default:j(1280)}Mr[e]=t}return t},ib:function(e,t){if(2>B.version)return j(1282),0;var r=Rr[e];if(r)return 0>t||t>=r.length?(j(1281),0):r[t];switch(e){case 7939:return r=p.getSupportedExtensions()||[],r=r.concat(r.map(function(i){return\"GL_\"+i})),r=r.map(function(i){return Qe(i)}),r=Rr[e]=r,0>t||t>=r.length?(j(1281),0):r[t];default:return j(1280),0}},Oa:function(e,t){if(t=t?Re(G,t):\"\",e=fe[e]){var r=e,i=r.fe,o=r.He,s;if(!i)for(r.fe=i={},r.Ge={},s=0;s<p.getProgramParameter(r,35718);++s){var l=p.getActiveUniform(r,s),f=l.name;l=l.size;var h=Hr(f);h=0<h?f.slice(0,h):f;var g=r.xe;for(r.xe+=l,o[h]=[l,g],f=0;f<l;++f)i[g]=f,r.Ge[g++]=h}if(r=e.fe,i=0,o=t,s=Hr(t),0<s&&(i=parseInt(t.slice(s+1))>>>0,o=t.slice(0,s)),(o=e.He[o])&&i<o[0]&&(i+=o[1],r[i]=r[i]||p.getUniformLocation(e,t)))return i}else j(1281);return-1},Kb:function(e,t,r){for(var i=mt[t],o=0;o<t;o++)i[o]=w[r+4*o>>2];p.invalidateFramebuffer(e,i)},Lb:function(e,t,r,i,o,s,l){for(var f=mt[t],h=0;h<t;h++)f[h]=w[r+4*h>>2];p.invalidateSubFramebuffer(e,f,i,o,s,l)},Tb:function(e){return p.isSync(De[e])},Pa:function(e){return(e=re[e])?p.isTexture(e):0},Qa:function(e){p.lineWidth(e)},Ra:function(e){e=fe[e],p.linkProgram(e),e.fe=0,e.He={}},mc:function(e,t,r,i,o,s){p.De.multiDrawArraysInstancedBaseInstanceWEBGL(e,w,t>>2,w,r>>2,w,i>>2,O,o>>2,s)},nc:function(e,t,r,i,o,s,l,f){p.De.multiDrawElementsInstancedBaseVertexBaseInstanceWEBGL(e,w,t>>2,r,w,i>>2,w,o>>2,w,s>>2,O,l>>2,f)},Sa:function(e,t){e==3317&&(xr=t),p.pixelStorei(e,t)},pc:function(e){p.readBuffer(e)},Ta:function(e,t,r,i,o,s,l){if(2<=B.version)if(p.se)p.readPixels(e,t,r,i,o,s,l);else{var f=_t(s);p.readPixels(e,t,r,i,o,s,f,l>>31-Math.clz32(f.BYTES_PER_ELEMENT))}else(l=$t(s,o,r,i,l))?p.readPixels(e,t,r,i,o,s,l):j(1280)},$b:function(e,t,r,i){p.renderbufferStorage(e,t,r,i)},Xb:function(e,t,r,i,o){p.renderbufferStorageMultisample(e,t,r,i,o)},Pb:function(e,t,r){p.samplerParameterf(Ie[e],t,r)},Qb:function(e,t,r){p.samplerParameteri(Ie[e],t,r)},Rb:function(e,t,r){p.samplerParameteri(Ie[e],t,w[r>>2])},Ua:function(e,t,r,i){p.scissor(e,t,r,i)},Va:function(e,t,r,i){for(var o=\"\",s=0;s<t;++s){var l=i?w[i+4*s>>2]:-1,f=w[r+4*s>>2];l=f?Re(G,f,0>l?void 0:l):\"\",o+=l}p.shaderSource(ce[e],o)},Wa:function(e,t,r){p.stencilFunc(e,t,r)},Xa:function(e,t,r,i){p.stencilFuncSeparate(e,t,r,i)},Ya:function(e){p.stencilMask(e)},Za:function(e,t){p.stencilMaskSeparate(e,t)},_a:function(e,t,r){p.stencilOp(e,t,r)},$a:function(e,t,r,i){p.stencilOpSeparate(e,t,r,i)},ab:function(e,t,r,i,o,s,l,f,h){if(2<=B.version)if(p.Zd)p.texImage2D(e,t,r,i,o,s,l,f,h);else if(h){var g=_t(f);p.texImage2D(e,t,r,i,o,s,l,f,g,h>>31-Math.clz32(g.BYTES_PER_ELEMENT))}else p.texImage2D(e,t,r,i,o,s,l,f,null);else p.texImage2D(e,t,r,i,o,s,l,f,h?$t(f,l,i,o,h):null)},bb:function(e,t,r){p.texParameterf(e,t,r)},cb:function(e,t,r){p.texParameterf(e,t,R[r>>2])},db:function(e,t,r){p.texParameteri(e,t,r)},eb:function(e,t,r){p.texParameteri(e,t,w[r>>2])},jc:function(e,t,r,i,o){p.texStorage2D(e,t,r,i,o)},fb:function(e,t,r,i,o,s,l,f,h){if(2<=B.version)if(p.Zd)p.texSubImage2D(e,t,r,i,o,s,l,f,h);else if(h){var g=_t(f);p.texSubImage2D(e,t,r,i,o,s,l,f,g,h>>31-Math.clz32(g.BYTES_PER_ELEMENT))}else p.texSubImage2D(e,t,r,i,o,s,l,f,null);else g=null,h&&(g=$t(f,l,o,s,h)),p.texSubImage2D(e,t,r,i,o,s,l,f,g)},gb:function(e,t){p.uniform1f(H(e),t)},hb:function(e,t,r){if(2<=B.version)t&&p.uniform1fv(H(e),R,r>>2,t);else{if(288>=t)for(var i=Te[t-1],o=0;o<t;++o)i[o]=R[r+4*o>>2];else i=R.subarray(r>>2,r+4*t>>2);p.uniform1fv(H(e),i)}},Uc:function(e,t){p.uniform1i(H(e),t)},Vc:function(e,t,r){if(2<=B.version)t&&p.uniform1iv(H(e),w,r>>2,t);else{if(288>=t)for(var i=Ze[t-1],o=0;o<t;++o)i[o]=w[r+4*o>>2];else i=w.subarray(r>>2,r+4*t>>2);p.uniform1iv(H(e),i)}},Wc:function(e,t,r){p.uniform2f(H(e),t,r)},Xc:function(e,t,r){if(2<=B.version)t&&p.uniform2fv(H(e),R,r>>2,2*t);else{if(144>=t)for(var i=Te[2*t-1],o=0;o<2*t;o+=2)i[o]=R[r+4*o>>2],i[o+1]=R[r+(4*o+4)>>2];else i=R.subarray(r>>2,r+8*t>>2);p.uniform2fv(H(e),i)}},Tc:function(e,t,r){p.uniform2i(H(e),t,r)},Sc:function(e,t,r){if(2<=B.version)t&&p.uniform2iv(H(e),w,r>>2,2*t);else{if(144>=t)for(var i=Ze[2*t-1],o=0;o<2*t;o+=2)i[o]=w[r+4*o>>2],i[o+1]=w[r+(4*o+4)>>2];else i=w.subarray(r>>2,r+8*t>>2);p.uniform2iv(H(e),i)}},Rc:function(e,t,r,i){p.uniform3f(H(e),t,r,i)},Qc:function(e,t,r){if(2<=B.version)t&&p.uniform3fv(H(e),R,r>>2,3*t);else{if(96>=t)for(var i=Te[3*t-1],o=0;o<3*t;o+=3)i[o]=R[r+4*o>>2],i[o+1]=R[r+(4*o+4)>>2],i[o+2]=R[r+(4*o+8)>>2];else i=R.subarray(r>>2,r+12*t>>2);p.uniform3fv(H(e),i)}},Pc:function(e,t,r,i){p.uniform3i(H(e),t,r,i)},Oc:function(e,t,r){if(2<=B.version)t&&p.uniform3iv(H(e),w,r>>2,3*t);else{if(96>=t)for(var i=Ze[3*t-1],o=0;o<3*t;o+=3)i[o]=w[r+4*o>>2],i[o+1]=w[r+(4*o+4)>>2],i[o+2]=w[r+(4*o+8)>>2];else i=w.subarray(r>>2,r+12*t>>2);p.uniform3iv(H(e),i)}},Nc:function(e,t,r,i,o){p.uniform4f(H(e),t,r,i,o)},Mc:function(e,t,r){if(2<=B.version)t&&p.uniform4fv(H(e),R,r>>2,4*t);else{if(72>=t){var i=Te[4*t-1],o=R;r>>=2;for(var s=0;s<4*t;s+=4){var l=r+s;i[s]=o[l],i[s+1]=o[l+1],i[s+2]=o[l+2],i[s+3]=o[l+3]}}else i=R.subarray(r>>2,r+16*t>>2);p.uniform4fv(H(e),i)}},Ac:function(e,t,r,i,o){p.uniform4i(H(e),t,r,i,o)},Bc:function(e,t,r){if(2<=B.version)t&&p.uniform4iv(H(e),w,r>>2,4*t);else{if(72>=t)for(var i=Ze[4*t-1],o=0;o<4*t;o+=4)i[o]=w[r+4*o>>2],i[o+1]=w[r+(4*o+4)>>2],i[o+2]=w[r+(4*o+8)>>2],i[o+3]=w[r+(4*o+12)>>2];else i=w.subarray(r>>2,r+16*t>>2);p.uniform4iv(H(e),i)}},Cc:function(e,t,r,i){if(2<=B.version)t&&p.uniformMatrix2fv(H(e),!!r,R,i>>2,4*t);else{if(72>=t)for(var o=Te[4*t-1],s=0;s<4*t;s+=4)o[s]=R[i+4*s>>2],o[s+1]=R[i+(4*s+4)>>2],o[s+2]=R[i+(4*s+8)>>2],o[s+3]=R[i+(4*s+12)>>2];else o=R.subarray(i>>2,i+16*t>>2);p.uniformMatrix2fv(H(e),!!r,o)}},Dc:function(e,t,r,i){if(2<=B.version)t&&p.uniformMatrix3fv(H(e),!!r,R,i>>2,9*t);else{if(32>=t)for(var o=Te[9*t-1],s=0;s<9*t;s+=9)o[s]=R[i+4*s>>2],o[s+1]=R[i+(4*s+4)>>2],o[s+2]=R[i+(4*s+8)>>2],o[s+3]=R[i+(4*s+12)>>2],o[s+4]=R[i+(4*s+16)>>2],o[s+5]=R[i+(4*s+20)>>2],o[s+6]=R[i+(4*s+24)>>2],o[s+7]=R[i+(4*s+28)>>2],o[s+8]=R[i+(4*s+32)>>2];else o=R.subarray(i>>2,i+36*t>>2);p.uniformMatrix3fv(H(e),!!r,o)}},Ec:function(e,t,r,i){if(2<=B.version)t&&p.uniformMatrix4fv(H(e),!!r,R,i>>2,16*t);else{if(18>=t){var o=Te[16*t-1],s=R;i>>=2;for(var l=0;l<16*t;l+=16){var f=i+l;o[l]=s[f],o[l+1]=s[f+1],o[l+2]=s[f+2],o[l+3]=s[f+3],o[l+4]=s[f+4],o[l+5]=s[f+5],o[l+6]=s[f+6],o[l+7]=s[f+7],o[l+8]=s[f+8],o[l+9]=s[f+9],o[l+10]=s[f+10],o[l+11]=s[f+11],o[l+12]=s[f+12],o[l+13]=s[f+13],o[l+14]=s[f+14],o[l+15]=s[f+15]}}else o=R.subarray(i>>2,i+64*t>>2);p.uniformMatrix4fv(H(e),!!r,o)}},Fc:function(e){e=fe[e],p.useProgram(e),p.Re=e},Gc:function(e,t){p.vertexAttrib1f(e,t)},Hc:function(e,t){p.vertexAttrib2f(e,R[t>>2],R[t+4>>2])},Ic:function(e,t){p.vertexAttrib3f(e,R[t>>2],R[t+4>>2],R[t+8>>2])},Jc:function(e,t){p.vertexAttrib4f(e,R[t>>2],R[t+4>>2],R[t+8>>2],R[t+12>>2])},kc:function(e,t){p.vertexAttribDivisor(e,t)},lc:function(e,t,r,i,o){p.vertexAttribIPointer(e,t,r,i,o)},Kc:function(e,t,r,i,o,s){p.vertexAttribPointer(e,t,r,!!i,o,s)},Lc:function(e,t,r,i){p.viewport(e,t,r,i)},qb:function(e,t,r,i){p.waitSync(De[e],t,(r>>>0)+4294967296*i)},W:jn,F:Hn,E:Wn,X:Un,Ib:Nn,V:Sn,U:Yn,A:Vn,B:$n,D:On,L:kn,sb:(e,t,r,i)=>Bn(e,t,r,i)};(function(){function e(r){if(b=r=r.exports,nt=b.Yc,rr(),Q=b._c,ir.unshift(b.Zc),Me--,m.monitorRunDependencies&&m.monitorRunDependencies(Me),Me==0&&(Mt!==null&&(clearInterval(Mt),Mt=null),We)){var i=We;We=null,i()}return r}var t={a:bn};if(Me++,m.monitorRunDependencies&&m.monitorRunDependencies(Me),m.instantiateWasm)try{return m.instantiateWasm(t,e)}catch(r){Ae(\"Module.instantiateWasm callback failed with error: \"+r),et(r)}return rn(t,function(r){e(r.instance)}).catch(et),{}})();var Pe=m._free=e=>(Pe=m._free=b.$c)(e),Ct=m._malloc=e=>(Ct=m._malloc=b.ad)(e),Wr=e=>(Wr=b.bd)(e);m.__embind_initialize_bindings=()=>(m.__embind_initialize_bindings=b.cd)();var ne=(e,t)=>(ne=b.dd)(e,t),ie=()=>(ie=b.ed)(),oe=e=>(oe=b.fd)(e);m.dynCall_viji=(e,t,r,i,o)=>(m.dynCall_viji=b.gd)(e,t,r,i,o),m.dynCall_vijiii=(e,t,r,i,o,s,l)=>(m.dynCall_vijiii=b.hd)(e,t,r,i,o,s,l),m.dynCall_viiiiij=(e,t,r,i,o,s,l,f)=>(m.dynCall_viiiiij=b.id)(e,t,r,i,o,s,l,f),m.dynCall_jii=(e,t,r)=>(m.dynCall_jii=b.jd)(e,t,r),m.dynCall_vij=(e,t,r,i)=>(m.dynCall_vij=b.kd)(e,t,r,i),m.dynCall_iiij=(e,t,r,i,o)=>(m.dynCall_iiij=b.ld)(e,t,r,i,o),m.dynCall_iiiij=(e,t,r,i,o,s)=>(m.dynCall_iiiij=b.md)(e,t,r,i,o,s),m.dynCall_viij=(e,t,r,i,o)=>(m.dynCall_viij=b.nd)(e,t,r,i,o),m.dynCall_viiij=(e,t,r,i,o,s)=>(m.dynCall_viiij=b.od)(e,t,r,i,o,s),m.dynCall_ji=(e,t)=>(m.dynCall_ji=b.pd)(e,t),m.dynCall_iij=(e,t,r,i)=>(m.dynCall_iij=b.qd)(e,t,r,i),m.dynCall_jiiiiii=(e,t,r,i,o,s,l)=>(m.dynCall_jiiiiii=b.rd)(e,t,r,i,o,s,l),m.dynCall_jiiiiji=(e,t,r,i,o,s,l,f)=>(m.dynCall_jiiiiji=b.sd)(e,t,r,i,o,s,l,f),m.dynCall_iijj=(e,t,r,i,o,s)=>(m.dynCall_iijj=b.td)(e,t,r,i,o,s),m.dynCall_iiiji=(e,t,r,i,o,s)=>(m.dynCall_iiiji=b.ud)(e,t,r,i,o,s),m.dynCall_iiji=(e,t,r,i,o)=>(m.dynCall_iiji=b.vd)(e,t,r,i,o),m.dynCall_iijjiii=(e,t,r,i,o,s,l,f,h)=>(m.dynCall_iijjiii=b.wd)(e,t,r,i,o,s,l,f,h),m.dynCall_vijjjii=(e,t,r,i,o,s,l,f,h,g)=>(m.dynCall_vijjjii=b.xd)(e,t,r,i,o,s,l,f,h,g),m.dynCall_jiji=(e,t,r,i,o)=>(m.dynCall_jiji=b.yd)(e,t,r,i,o),m.dynCall_viijii=(e,t,r,i,o,s,l)=>(m.dynCall_viijii=b.zd)(e,t,r,i,o,s,l),m.dynCall_iiiiij=(e,t,r,i,o,s,l)=>(m.dynCall_iiiiij=b.Ad)(e,t,r,i,o,s,l),m.dynCall_iiiiijj=(e,t,r,i,o,s,l,f,h)=>(m.dynCall_iiiiijj=b.Bd)(e,t,r,i,o,s,l,f,h),m.dynCall_iiiiiijj=(e,t,r,i,o,s,l,f,h,g)=>(m.dynCall_iiiiiijj=b.Cd)(e,t,r,i,o,s,l,f,h,g);function kn(e,t,r,i,o){var s=ie();try{Q.get(e)(t,r,i,o)}catch(l){if(oe(s),l!==l+0)throw l;ne(1,0)}}function Hn(e,t,r){var i=ie();try{return Q.get(e)(t,r)}catch(o){if(oe(i),o!==o+0)throw o;ne(1,0)}}function Un(e,t,r,i,o){var s=ie();try{return Q.get(e)(t,r,i,o)}catch(l){if(oe(s),l!==l+0)throw l;ne(1,0)}}function On(e,t,r,i){var o=ie();try{Q.get(e)(t,r,i)}catch(s){if(oe(o),s!==s+0)throw s;ne(1,0)}}function jn(e,t){var r=ie();try{return Q.get(e)(t)}catch(i){if(oe(r),i!==i+0)throw i;ne(1,0)}}function Wn(e,t,r,i){var o=ie();try{return Q.get(e)(t,r,i)}catch(s){if(oe(o),s!==s+0)throw s;ne(1,0)}}function $n(e,t,r){var i=ie();try{Q.get(e)(t,r)}catch(o){if(oe(i),o!==o+0)throw o;ne(1,0)}}function Sn(e,t,r,i,o,s,l,f,h,g){var P=ie();try{return Q.get(e)(t,r,i,o,s,l,f,h,g)}catch(E){if(oe(P),E!==E+0)throw E;ne(1,0)}}function Vn(e,t){var r=ie();try{Q.get(e)(t)}catch(i){if(oe(r),i!==i+0)throw i;ne(1,0)}}function Nn(e,t,r,i,o,s,l){var f=ie();try{return Q.get(e)(t,r,i,o,s,l)}catch(h){if(oe(f),h!==h+0)throw h;ne(1,0)}}function Yn(e){var t=ie();try{Q.get(e)()}catch(r){if(oe(t),r!==r+0)throw r;ne(1,0)}}var At;We=function e(){At||$r(),At||(We=e)};function $r(){function e(){if(!At&&(At=!0,m.calledRun=!0,!er)){if(xt(ir),Jt(m),m.onRuntimeInitialized&&m.onRuntimeInitialized(),m.postRun)for(typeof m.postRun==\"function\"&&(m.postRun=[m.postRun]);m.postRun.length;){var t=m.postRun.shift();or.unshift(t)}xt(or)}}if(!(0<Me)){if(m.preRun)for(typeof m.preRun==\"function\"&&(m.preRun=[m.preRun]);m.preRun.length;)en();xt(nr),0<Me||(m.setStatus?(m.setStatus(\"Running...\"),setTimeout(function(){setTimeout(function(){m.setStatus(\"\")},1),e()},1)):e())}}if(m.preInit)for(typeof m.preInit==\"function\"&&(m.preInit=[m.preInit]);0<m.preInit.length;)m.preInit.pop()();return $r(),ge.ready}})();typeof zr==\"object\"&&typeof Kt==\"object\"?Kt.exports=Zr:typeof define==\"function\"&&define.amd&&define([],()=>Zr)});/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Jn());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../node_modules/@splinetool/runtime/build/ui.js\n"));

/***/ })

}]);
/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBusStop = createReactComponent("outline", "bus-stop", "IconBusStop", [["path", { "d": "M3 3m0 1a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1z", "key": "svg-0" }], ["path", { "d": "M18 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M10 5h7c2.761 0 5 3.134 5 7v5h-2", "key": "svg-2" }], ["path", { "d": "M16 17h-8", "key": "svg-3" }], ["path", { "d": "M16 5l1.5 7h4.5", "key": "svg-4" }], ["path", { "d": "M9.5 10h7.5", "key": "svg-5" }], ["path", { "d": "M12 5v5", "key": "svg-6" }], ["path", { "d": "M5 9v11", "key": "svg-7" }]]);

export { IconBusStop as default };
//# sourceMappingURL=IconBusStop.mjs.map

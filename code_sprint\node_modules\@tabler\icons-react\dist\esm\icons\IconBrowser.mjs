/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrowser = createReactComponent("outline", "browser", "IconBrowser", [["path", { "d": "M4 8h16", "key": "svg-0" }], ["path", { "d": "M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z", "key": "svg-1" }], ["path", { "d": "M8 4v4", "key": "svg-2" }]]);

export { IconBrowser as default };
//# sourceMappingURL=IconBrowser.mjs.map

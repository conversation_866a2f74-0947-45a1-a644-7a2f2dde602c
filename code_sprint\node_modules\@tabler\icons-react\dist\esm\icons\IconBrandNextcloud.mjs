/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandNextcloud = createReactComponent("outline", "brand-nextcloud", "IconBrandNextcloud", [["path", { "d": "M12 12m-5 0a5 5 0 1 0 10 0a5 5 0 1 0 -10 0", "key": "svg-0" }], ["path", { "d": "M4.5 12.5m-2.5 0a2.5 2.5 0 1 0 5 0a2.5 2.5 0 1 0 -5 0", "key": "svg-1" }], ["path", { "d": "M19.5 12.5m-2.5 0a2.5 2.5 0 1 0 5 0a2.5 2.5 0 1 0 -5 0", "key": "svg-2" }]]);

export { IconBrandNextcloud as default };
//# sourceMappingURL=IconBrandNextcloud.mjs.map

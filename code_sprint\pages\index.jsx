import Link from 'next/link';
import Image from 'next/image';
import styles from '../styles/Home.module.css';


export default function Home() {
  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <div className={styles.logo}>
          <Image src="/codesprint-logo.png" alt="CodeSprint Logo" width={150} height={50} />
        </div>
        <nav className={styles.nav}>
          <Link href="/">Home</Link>
          <Link href="/courses">Courses</Link>
          <Link href="/about">About Us</Link>
          <Link href="/faq">FAQ</Link>
          <Link href="/contact">Contact Us</Link>
        </nav>
        <Link href="/login" passHref>
          <button className={styles.loginButton}>Login</button>
        </Link>
      </header>

      <main className={styles.main}>
        <div className={styles.content}>
          <h1 className={styles.title}>
            CODE SPRINT<br />
            BLAH BLAH
          </h1>
          <p className={styles.description}>
            Code Sprint offers an engaging AI-based curriculum for students in grades 1 to 10,
            covering AI, data science, cybersecurity, blockchain, and more. Through interactive
            courses, hands-on projects, and fun competitions, we inspire the next generation of
            tech-savvy problem solvers. Join us in making learning an exciting adventure!
          </p>

          <Link href="/register" passHref>
            <button className={styles.registerButton}>REGISTER NOW!</button>
          </Link>
        </div>

        <div className={styles.mascot}>
          <Image src="/glitch-mascot.png" alt="Glitch Mascot" width={400} height={400} />
          <div className={styles.meetGlitch}>MEET GLITCH</div>
          <div className={styles.ourMascot}>OUR MASCOT!</div>
        </div>
      </main>

      <section className={styles.coursesSection}>
        <div className={styles.coursesIntro}>
          <h2>CHECK OUT OUR <br></br><span className={styles.highlightedText}>COURSES</span></h2>
          <p>
            This is a paragraph about the courses or something like that. It is a paragraph that is
            going to be at least one or two lines. This is a paragraph about the courses or
            something, but it is a paragraph that is going to be at least one or two lines. 
          </p>
          <p>
            It is a paragraph that is going to be at least one or two lines. This is a paragraph
            about the courses or something, but it is a paragraph that is going to be at least one
            or two lines.
          </p>
          <Image src="/glitch-mascot.png" alt="Mascot" width={120} height={160} className={styles.mascotImage} />
        </div>
        <div className={styles.coursesGrid}>
          <Link href="/level1" passHref>
            <div className={`${styles.courseBox} ${styles.purpleBox}`}>
              <h3>name of level 1</h3>
              <Image src="/glitch-mascot.png" alt="Level 1 Icon" width={150} height={50} />
              <span className={styles.addIcon}>+</span>
            </div>
          </Link>
          <Link href="/level2" passHref>
            <div className={`${styles.courseBox} ${styles.yellowBox}`}>
              <h3>name of level 2</h3>
              <Image src="/glitch-mascot.png" alt="Level 2 Icon" width={150} height={50} />
              <span className={styles.addIcon}>+</span>
            </div>
          </Link>
          <Link href="/level3" passHref>
            <div className={`${styles.courseBox} ${styles.redBox}`}>
              <h3>name of level 3</h3>
              <Image src="/glitch-mascot.png" alt="Level 3 Icon" width={150} height={50} />
              <span className={styles.addIcon}>+</span>
            </div>
          </Link>
          <div className={`${styles.courseBox} ${styles.blueBox}`}>
            <h3>name of level 3</h3>
            <Image src="/glitch-mascot.png" alt="Level 4 Icon" width={150} height={50} />
          </div>
          <Link href="/level5" passHref>
            <div className={`${styles.courseBox} ${styles.orangeBox}`}>
              <h3>name of level 4</h3>
              <Image src="/glitch-mascot.png" alt="Level 5 Icon" width={150} height={50} />
              <span className={styles.addIcon}>+</span>
            </div>
          </Link>
        </div>
      </section>
      <section className={styles.aboutSection}>
        <div className={styles.aboutContent}>
          <h2 className={styles.aboutTitle}>ABOUT US</h2>
          <div className={styles.aboutBox}>
            <p className={styles.aboutText}>
              This is a paragraph about the course or something like that. It is a paragraph that is
              going to be at least one or two lines. This is a paragraph about the courses or
              something, but it is a paragraph that is going to be at least one or two lines. 
              It is a paragraph that is going to be at least one or two lines. This is a paragraph
              about the courses or something, but it is a paragraph that is going to be at least one
              or two lines. This is a paragraph about the course or something like that. It is a 
              paragraph that is going to be at least one or two lines. This is a paragraph about the 
              courses or something, but it is a paragraph that is going to be at least one or two lines.
            </p>
            <div className={styles.mascotContainer}>
              <Image 
                src="/glitch-mascot.png" 
                alt="Glitch Mascot" 
                width={200} 
                height={200} 
                className={styles.aboutMascot}
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandTablerFilled = createReactComponent("filled", "brand-tabler-filled", "IconBrandTablerFilled", [["path", { "d": "M17 2a5 5 0 0 1 5 5v10a5 5 0 0 1 -5 5h-10a5 5 0 0 1 -5 -5v-10a5 5 0 0 1 5 -5zm-1 12h-3a1 1 0 0 0 0 2h3a1 1 0 0 0 0 -2m-7.293 -5.707a1 1 0 0 0 -1.414 0l-.083 .094a1 1 0 0 0 .083 1.32l2.292 2.293l-2.292 2.293a1 1 0 0 0 1.414 1.414l3 -3a1 1 0 0 0 0 -1.414z", "key": "svg-0" }]]);

export { IconBrandTablerFilled as default };
//# sourceMappingURL=IconBrandTablerFilled.mjs.map

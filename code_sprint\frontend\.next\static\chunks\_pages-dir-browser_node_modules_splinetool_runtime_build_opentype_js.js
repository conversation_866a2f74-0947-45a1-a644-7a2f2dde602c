"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_opentype_js"],{

/***/ "(pages-dir-browser)/../node_modules/@splinetool/runtime/build/opentype.js":
/*!*************************************************************!*\
  !*** ../node_modules/@splinetool/runtime/build/opentype.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bidi: () => (/* binding */ ne),\n/* harmony export */   BoundingBox: () => (/* binding */ pe),\n/* harmony export */   Font: () => (/* binding */ w),\n/* harmony export */   Glyph: () => (/* binding */ Q),\n/* harmony export */   Path: () => (/* binding */ P),\n/* harmony export */   _parse: () => (/* binding */ k),\n/* harmony export */   \"default\": () => (/* binding */ Ho),\n/* harmony export */   load: () => (/* binding */ Bo),\n/* harmony export */   loadSync: () => (/* binding */ Io),\n/* harmony export */   parse: () => (/* binding */ Dr)\n/* harmony export */ });\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(pages-dir-browser)/../node_modules/buffer/index.js\")[\"Buffer\"];\nvar va=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports);var Ne=va(()=>{});String.prototype.codePointAt||function(){var e=function(){try{var t={},a=Object.defineProperty,n=a(t,t,t)&&a}catch{}return n}(),r=function(t){if(this==null)throw TypeError();var a=String(this),n=a.length,s=t?Number(t):0;if(s!=s&&(s=0),!(s<0||s>=n)){var i=a.charCodeAt(s),u;return i>=55296&&i<=56319&&n>s+1&&(u=a.charCodeAt(s+1),u>=56320&&u<=57343)?(i-55296)*1024+u-56320+65536:i}};e?e(String.prototype,\"codePointAt\",{value:r,configurable:!0,writable:!0}):String.prototype.codePointAt=r}();var Sr=0,xt=-3;function Le(){this.table=new Uint16Array(16),this.trans=new Uint16Array(288)}function da(e,r){this.source=e,this.sourceIndex=0,this.tag=0,this.bitcount=0,this.dest=r,this.destLen=0,this.ltree=new Le,this.dtree=new Le}var bt=new Le,St=new Le,Tr=new Uint8Array(30),kr=new Uint16Array(30),Tt=new Uint8Array(30),kt=new Uint16Array(30),ga=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Br=new Le,se=new Uint8Array(288+32);function Ft(e,r,t,a){var n,s;for(n=0;n<t;++n)e[n]=0;for(n=0;n<30-t;++n)e[n+t]=n/t|0;for(s=a,n=0;n<30;++n)r[n]=s,s+=1<<e[n]}function ma(e,r){var t;for(t=0;t<7;++t)e.table[t]=0;for(e.table[7]=24,e.table[8]=152,e.table[9]=112,t=0;t<24;++t)e.trans[t]=256+t;for(t=0;t<144;++t)e.trans[24+t]=t;for(t=0;t<8;++t)e.trans[24+144+t]=280+t;for(t=0;t<112;++t)e.trans[24+144+8+t]=144+t;for(t=0;t<5;++t)r.table[t]=0;for(r.table[5]=32,t=0;t<32;++t)r.trans[t]=t}var Ir=new Uint16Array(16);function ar(e,r,t,a){var n,s;for(n=0;n<16;++n)e.table[n]=0;for(n=0;n<a;++n)e.table[r[t+n]]++;for(e.table[0]=0,s=0,n=0;n<16;++n)Ir[n]=s,s+=e.table[n];for(n=0;n<a;++n)r[t+n]&&(e.trans[Ir[r[t+n]]++]=n)}function ya(e){e.bitcount--||(e.tag=e.source[e.sourceIndex++],e.bitcount=7);var r=e.tag&1;return e.tag>>>=1,r}function ie(e,r,t){if(!r)return t;for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var a=e.tag&65535>>>16-r;return e.tag>>>=r,e.bitcount-=r,a+t}function hr(e,r){for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var t=0,a=0,n=0,s=e.tag;do a=2*a+(s&1),s>>>=1,++n,t+=r.table[n],a-=r.table[n];while(a>=0);return e.tag=s,e.bitcount-=n,r.trans[t+a]}function xa(e,r,t){var a,n,s,i,u,o;for(a=ie(e,5,257),n=ie(e,5,1),s=ie(e,4,4),i=0;i<19;++i)se[i]=0;for(i=0;i<s;++i){var l=ie(e,3,0);se[ga[i]]=l}for(ar(Br,se,0,19),u=0;u<a+n;){var f=hr(e,Br);switch(f){case 16:var h=se[u-1];for(o=ie(e,2,3);o;--o)se[u++]=h;break;case 17:for(o=ie(e,3,3);o;--o)se[u++]=0;break;case 18:for(o=ie(e,7,11);o;--o)se[u++]=0;break;default:se[u++]=f;break}}ar(r,se,0,a),ar(t,se,a,n)}function Mr(e,r,t){for(;;){var a=hr(e,r);if(a===256)return Sr;if(a<256)e.dest[e.destLen++]=a;else{var n,s,i,u;for(a-=257,n=ie(e,Tr[a],kr[a]),s=hr(e,t),i=e.destLen-ie(e,Tt[s],kt[s]),u=i;u<i+n;++u)e.dest[e.destLen++]=e.dest[u]}}}function ba(e){for(var r,t,a;e.bitcount>8;)e.sourceIndex--,e.bitcount-=8;if(r=e.source[e.sourceIndex+1],r=256*r+e.source[e.sourceIndex],t=e.source[e.sourceIndex+3],t=256*t+e.source[e.sourceIndex+2],r!==(~t&65535))return xt;for(e.sourceIndex+=4,a=r;a;--a)e.dest[e.destLen++]=e.source[e.sourceIndex++];return e.bitcount=0,Sr}function Sa(e,r){var t=new da(e,r),a,n,s;do{switch(a=ya(t),n=ie(t,2,0),n){case 0:s=ba(t);break;case 1:s=Mr(t,bt,St);break;case 2:xa(t,t.ltree,t.dtree),s=Mr(t,t.ltree,t.dtree);break;default:s=xt}if(s!==Sr)throw new Error(\"Data error\")}while(!a);return t.destLen<t.dest.length?typeof t.dest.slice==\"function\"?t.dest.slice(0,t.destLen):t.dest.subarray(0,t.destLen):t.dest}ma(bt,St);Ft(Tr,kr,4,3);Ft(Tt,kt,2,1);Tr[28]=0;kr[28]=258;var Ta=Sa;function Te(e,r,t,a,n){return Math.pow(1-n,3)*e+3*Math.pow(1-n,2)*n*r+3*(1-n)*Math.pow(n,2)*t+Math.pow(n,3)*a}function pe(){this.x1=Number.NaN,this.y1=Number.NaN,this.x2=Number.NaN,this.y2=Number.NaN}pe.prototype.isEmpty=function(){return isNaN(this.x1)||isNaN(this.y1)||isNaN(this.x2)||isNaN(this.y2)};pe.prototype.addPoint=function(e,r){typeof e==\"number\"&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=e,this.x2=e),e<this.x1&&(this.x1=e),e>this.x2&&(this.x2=e)),typeof r==\"number\"&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=r,this.y2=r),r<this.y1&&(this.y1=r),r>this.y2&&(this.y2=r))};pe.prototype.addX=function(e){this.addPoint(e,null)};pe.prototype.addY=function(e){this.addPoint(null,e)};pe.prototype.addBezier=function(e,r,t,a,n,s,i,u){var o=[e,r],l=[t,a],f=[n,s],h=[i,u];this.addPoint(e,r),this.addPoint(i,u);for(var p=0;p<=1;p++){var c=6*o[p]-12*l[p]+6*f[p],d=-3*o[p]+9*l[p]-9*f[p]+3*h[p],x=3*l[p]-3*o[p];if(d===0){if(c===0)continue;var m=-x/c;0<m&&m<1&&(p===0&&this.addX(Te(o[p],l[p],f[p],h[p],m)),p===1&&this.addY(Te(o[p],l[p],f[p],h[p],m)));continue}var y=Math.pow(c,2)-4*x*d;if(!(y<0)){var C=(-c+Math.sqrt(y))/(2*d);0<C&&C<1&&(p===0&&this.addX(Te(o[p],l[p],f[p],h[p],C)),p===1&&this.addY(Te(o[p],l[p],f[p],h[p],C)));var S=(-c-Math.sqrt(y))/(2*d);0<S&&S<1&&(p===0&&this.addX(Te(o[p],l[p],f[p],h[p],S)),p===1&&this.addY(Te(o[p],l[p],f[p],h[p],S)))}}};pe.prototype.addQuad=function(e,r,t,a,n,s){var i=e+.6666666666666666*(t-e),u=r+2/3*(a-r),o=i+1/3*(n-e),l=u+1/3*(s-r);this.addBezier(e,r,i,u,o,l,n,s)};function P(){this.commands=[],this.fill=\"black\",this.stroke=null,this.strokeWidth=1}P.prototype.moveTo=function(e,r){this.commands.push({type:\"M\",x:e,y:r})};P.prototype.lineTo=function(e,r){this.commands.push({type:\"L\",x:e,y:r})};P.prototype.curveTo=P.prototype.bezierCurveTo=function(e,r,t,a,n,s){this.commands.push({type:\"C\",x1:e,y1:r,x2:t,y2:a,x:n,y:s})};P.prototype.quadTo=P.prototype.quadraticCurveTo=function(e,r,t,a){this.commands.push({type:\"Q\",x1:e,y1:r,x:t,y:a})};P.prototype.close=P.prototype.closePath=function(){this.commands.push({type:\"Z\"})};P.prototype.extend=function(e){if(e.commands)e=e.commands;else if(e instanceof pe){var r=e;this.moveTo(r.x1,r.y1),this.lineTo(r.x2,r.y1),this.lineTo(r.x2,r.y2),this.lineTo(r.x1,r.y2),this.close();return}Array.prototype.push.apply(this.commands,e)};P.prototype.getBoundingBox=function(){for(var e=new pe,r=0,t=0,a=0,n=0,s=0;s<this.commands.length;s++){var i=this.commands[s];switch(i.type){case\"M\":e.addPoint(i.x,i.y),r=a=i.x,t=n=i.y;break;case\"L\":e.addPoint(i.x,i.y),a=i.x,n=i.y;break;case\"Q\":e.addQuad(a,n,i.x1,i.y1,i.x,i.y),a=i.x,n=i.y;break;case\"C\":e.addBezier(a,n,i.x1,i.y1,i.x2,i.y2,i.x,i.y),a=i.x,n=i.y;break;case\"Z\":a=r,n=t;break;default:throw new Error(\"Unexpected path command \"+i.type)}}return e.isEmpty()&&e.addPoint(0,0),e};P.prototype.draw=function(e){e.beginPath();for(var r=0;r<this.commands.length;r+=1){var t=this.commands[r];t.type===\"M\"?e.moveTo(t.x,t.y):t.type===\"L\"?e.lineTo(t.x,t.y):t.type===\"C\"?e.bezierCurveTo(t.x1,t.y1,t.x2,t.y2,t.x,t.y):t.type===\"Q\"?e.quadraticCurveTo(t.x1,t.y1,t.x,t.y):t.type===\"Z\"&&e.closePath()}this.fill&&(e.fillStyle=this.fill,e.fill()),this.stroke&&(e.strokeStyle=this.stroke,e.lineWidth=this.strokeWidth,e.stroke())};P.prototype.toPathData=function(e){e=e!==void 0?e:2;function r(i){return Math.round(i)===i?\"\"+Math.round(i):i.toFixed(e)}function t(){for(var i=arguments,u=\"\",o=0;o<arguments.length;o+=1){var l=i[o];l>=0&&o>0&&(u+=\" \"),u+=r(l)}return u}for(var a=\"\",n=0;n<this.commands.length;n+=1){var s=this.commands[n];s.type===\"M\"?a+=\"M\"+t(s.x,s.y):s.type===\"L\"?a+=\"L\"+t(s.x,s.y):s.type===\"C\"?a+=\"C\"+t(s.x1,s.y1,s.x2,s.y2,s.x,s.y):s.type===\"Q\"?a+=\"Q\"+t(s.x1,s.y1,s.x,s.y):s.type===\"Z\"&&(a+=\"Z\")}return a};P.prototype.toSVG=function(e){var r='<path d=\"';return r+=this.toPathData(e),r+='\"',this.fill&&this.fill!==\"black\"&&(this.fill===null?r+=' fill=\"none\"':r+=' fill=\"'+this.fill+'\"'),this.stroke&&(r+=' stroke=\"'+this.stroke+'\" stroke-width=\"'+this.strokeWidth+'\"'),r+=\"/>\",r};P.prototype.toDOMElement=function(e){var r=this.toPathData(e),t=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\");return t.setAttribute(\"d\",r),t};function Ut(e){throw new Error(e)}function Pr(e,r){e||Ut(r)}var U={fail:Ut,argument:Pr,assert:Pr},Gr=32768,Nr=2147483648,Fe={},g={},E={};function ae(e){return function(){return e}}g.BYTE=function(e){return U.argument(e>=0&&e<=255,\"Byte value should be between 0 and 255.\"),[e]};E.BYTE=ae(1);g.CHAR=function(e){return[e.charCodeAt(0)]};E.CHAR=ae(1);g.CHARARRAY=function(e){typeof e>\"u\"&&(e=\"\",console.warn(\"Undefined CHARARRAY encountered and treated as an empty string. This is probably caused by a missing glyph name.\"));for(var r=[],t=0;t<e.length;t+=1)r[t]=e.charCodeAt(t);return r};E.CHARARRAY=function(e){return typeof e>\"u\"?0:e.length};g.USHORT=function(e){return[e>>8&255,e&255]};E.USHORT=ae(2);g.SHORT=function(e){return e>=Gr&&(e=-(2*Gr-e)),[e>>8&255,e&255]};E.SHORT=ae(2);g.UINT24=function(e){return[e>>16&255,e>>8&255,e&255]};E.UINT24=ae(3);g.ULONG=function(e){return[e>>24&255,e>>16&255,e>>8&255,e&255]};E.ULONG=ae(4);g.LONG=function(e){return e>=Nr&&(e=-(2*Nr-e)),[e>>24&255,e>>16&255,e>>8&255,e&255]};E.LONG=ae(4);g.FIXED=g.ULONG;E.FIXED=E.ULONG;g.FWORD=g.SHORT;E.FWORD=E.SHORT;g.UFWORD=g.USHORT;E.UFWORD=E.USHORT;g.LONGDATETIME=function(e){return[0,0,0,0,e>>24&255,e>>16&255,e>>8&255,e&255]};E.LONGDATETIME=ae(8);g.TAG=function(e){return U.argument(e.length===4,\"Tag should be exactly 4 ASCII characters.\"),[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]};E.TAG=ae(4);g.Card8=g.BYTE;E.Card8=E.BYTE;g.Card16=g.USHORT;E.Card16=E.USHORT;g.OffSize=g.BYTE;E.OffSize=E.BYTE;g.SID=g.USHORT;E.SID=E.USHORT;g.NUMBER=function(e){return e>=-107&&e<=107?[e+139]:e>=108&&e<=1131?(e=e-108,[(e>>8)+247,e&255]):e>=-1131&&e<=-108?(e=-e-108,[(e>>8)+251,e&255]):e>=-32768&&e<=32767?g.NUMBER16(e):g.NUMBER32(e)};E.NUMBER=function(e){return g.NUMBER(e).length};g.NUMBER16=function(e){return[28,e>>8&255,e&255]};E.NUMBER16=ae(3);g.NUMBER32=function(e){return[29,e>>24&255,e>>16&255,e>>8&255,e&255]};E.NUMBER32=ae(5);g.REAL=function(e){var r=e.toString(),t=/\\.(\\d*?)(?:9{5,20}|0{5,20})\\d{0,2}(?:e(.+)|$)/.exec(r);if(t){var a=parseFloat(\"1e\"+((t[2]?+t[2]:0)+t[1].length));r=(Math.round(e*a)/a).toString()}for(var n=\"\",s=0,i=r.length;s<i;s+=1){var u=r[s];u===\"e\"?n+=r[++s]===\"-\"?\"c\":\"b\":u===\".\"?n+=\"a\":u===\"-\"?n+=\"e\":n+=u}n+=n.length&1?\"f\":\"ff\";for(var o=[30],l=0,f=n.length;l<f;l+=2)o.push(parseInt(n.substr(l,2),16));return o};E.REAL=function(e){return g.REAL(e).length};g.NAME=g.CHARARRAY;E.NAME=E.CHARARRAY;g.STRING=g.CHARARRAY;E.STRING=E.CHARARRAY;Fe.UTF8=function(e,r,t){for(var a=[],n=t,s=0;s<n;s++,r+=1)a[s]=e.getUint8(r);return String.fromCharCode.apply(null,a)};Fe.UTF16=function(e,r,t){for(var a=[],n=t/2,s=0;s<n;s++,r+=2)a[s]=e.getUint16(r);return String.fromCharCode.apply(null,a)};g.UTF16=function(e){for(var r=[],t=0;t<e.length;t+=1){var a=e.charCodeAt(t);r[r.length]=a>>8&255,r[r.length]=a&255}return r};E.UTF16=function(e){return e.length*2};var cr={\"x-mac-croatian\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\u0160\\u2122\\xB4\\xA8\\u2260\\u017D\\xD8\\u221E\\xB1\\u2264\\u2265\\u2206\\xB5\\u2202\\u2211\\u220F\\u0161\\u222B\\xAA\\xBA\\u03A9\\u017E\\xF8\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u0106\\xAB\\u010C\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u0110\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\uF8FF\\xA9\\u2044\\u20AC\\u2039\\u203A\\xC6\\xBB\\u2013\\xB7\\u201A\\u201E\\u2030\\xC2\\u0107\\xC1\\u010D\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\u0111\\xD2\\xDA\\xDB\\xD9\\u0131\\u02C6\\u02DC\\xAF\\u03C0\\xCB\\u02DA\\xB8\\xCA\\xE6\\u02C7\",\"x-mac-cyrillic\":\"\\u0410\\u0411\\u0412\\u0413\\u0414\\u0415\\u0416\\u0417\\u0418\\u0419\\u041A\\u041B\\u041C\\u041D\\u041E\\u041F\\u0420\\u0421\\u0422\\u0423\\u0424\\u0425\\u0426\\u0427\\u0428\\u0429\\u042A\\u042B\\u042C\\u042D\\u042E\\u042F\\u2020\\xB0\\u0490\\xA3\\xA7\\u2022\\xB6\\u0406\\xAE\\xA9\\u2122\\u0402\\u0452\\u2260\\u0403\\u0453\\u221E\\xB1\\u2264\\u2265\\u0456\\xB5\\u0491\\u0408\\u0404\\u0454\\u0407\\u0457\\u0409\\u0459\\u040A\\u045A\\u0458\\u0405\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\u040B\\u045B\\u040C\\u045C\\u0455\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u201E\\u040E\\u045E\\u040F\\u045F\\u2116\\u0401\\u0451\\u044F\\u0430\\u0431\\u0432\\u0433\\u0434\\u0435\\u0436\\u0437\\u0438\\u0439\\u043A\\u043B\\u043C\\u043D\\u043E\\u043F\\u0440\\u0441\\u0442\\u0443\\u0444\\u0445\\u0446\\u0447\\u0448\\u0449\\u044A\\u044B\\u044C\\u044D\\u044E\",\"x-mac-gaelic\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\xC6\\xD8\\u1E02\\xB1\\u2264\\u2265\\u1E03\\u010A\\u010B\\u1E0A\\u1E0B\\u1E1E\\u1E1F\\u0120\\u0121\\u1E40\\xE6\\xF8\\u1E41\\u1E56\\u1E57\\u027C\\u0192\\u017F\\u1E60\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\u1E61\\u1E9B\\xFF\\u0178\\u1E6A\\u20AC\\u2039\\u203A\\u0176\\u0177\\u1E6B\\xB7\\u1EF2\\u1EF3\\u204A\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\u2663\\xD2\\xDA\\xDB\\xD9\\u0131\\xDD\\xFD\\u0174\\u0175\\u1E84\\u1E85\\u1E80\\u1E81\\u1E82\\u1E83\",\"x-mac-greek\":\"\\xC4\\xB9\\xB2\\xC9\\xB3\\xD6\\xDC\\u0385\\xE0\\xE2\\xE4\\u0384\\xA8\\xE7\\xE9\\xE8\\xEA\\xEB\\xA3\\u2122\\xEE\\xEF\\u2022\\xBD\\u2030\\xF4\\xF6\\xA6\\u20AC\\xF9\\xFB\\xFC\\u2020\\u0393\\u0394\\u0398\\u039B\\u039E\\u03A0\\xDF\\xAE\\xA9\\u03A3\\u03AA\\xA7\\u2260\\xB0\\xB7\\u0391\\xB1\\u2264\\u2265\\xA5\\u0392\\u0395\\u0396\\u0397\\u0399\\u039A\\u039C\\u03A6\\u03AB\\u03A8\\u03A9\\u03AC\\u039D\\xAC\\u039F\\u03A1\\u2248\\u03A4\\xAB\\xBB\\u2026\\xA0\\u03A5\\u03A7\\u0386\\u0388\\u0153\\u2013\\u2015\\u201C\\u201D\\u2018\\u2019\\xF7\\u0389\\u038A\\u038C\\u038E\\u03AD\\u03AE\\u03AF\\u03CC\\u038F\\u03CD\\u03B1\\u03B2\\u03C8\\u03B4\\u03B5\\u03C6\\u03B3\\u03B7\\u03B9\\u03BE\\u03BA\\u03BB\\u03BC\\u03BD\\u03BF\\u03C0\\u03CE\\u03C1\\u03C3\\u03C4\\u03B8\\u03C9\\u03C2\\u03C7\\u03C5\\u03B6\\u03CA\\u03CB\\u0390\\u03B0\\xAD\",\"x-mac-icelandic\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\xDD\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\xC6\\xD8\\u221E\\xB1\\u2264\\u2265\\xA5\\xB5\\u2202\\u2211\\u220F\\u03C0\\u222B\\xAA\\xBA\\u03A9\\xE6\\xF8\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\xFF\\u0178\\u2044\\u20AC\\xD0\\xF0\\xDE\\xFE\\xFD\\xB7\\u201A\\u201E\\u2030\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\uF8FF\\xD2\\xDA\\xDB\\xD9\\u0131\\u02C6\\u02DC\\xAF\\u02D8\\u02D9\\u02DA\\xB8\\u02DD\\u02DB\\u02C7\",\"x-mac-inuit\":\"\\u1403\\u1404\\u1405\\u1406\\u140A\\u140B\\u1431\\u1432\\u1433\\u1434\\u1438\\u1439\\u1449\\u144E\\u144F\\u1450\\u1451\\u1455\\u1456\\u1466\\u146D\\u146E\\u146F\\u1470\\u1472\\u1473\\u1483\\u148B\\u148C\\u148D\\u148E\\u1490\\u1491\\xB0\\u14A1\\u14A5\\u14A6\\u2022\\xB6\\u14A7\\xAE\\xA9\\u2122\\u14A8\\u14AA\\u14AB\\u14BB\\u14C2\\u14C3\\u14C4\\u14C5\\u14C7\\u14C8\\u14D0\\u14EF\\u14F0\\u14F1\\u14F2\\u14F4\\u14F5\\u1505\\u14D5\\u14D6\\u14D7\\u14D8\\u14DA\\u14DB\\u14EA\\u1528\\u1529\\u152A\\u152B\\u152D\\u2026\\xA0\\u152E\\u153E\\u1555\\u1556\\u1557\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\u1558\\u1559\\u155A\\u155D\\u1546\\u1547\\u1548\\u1549\\u154B\\u154C\\u1550\\u157F\\u1580\\u1581\\u1582\\u1583\\u1584\\u1585\\u158F\\u1590\\u1591\\u1592\\u1593\\u1594\\u1595\\u1671\\u1672\\u1673\\u1674\\u1675\\u1676\\u1596\\u15A0\\u15A1\\u15A2\\u15A3\\u15A4\\u15A5\\u15A6\\u157C\\u0141\\u0142\",\"x-mac-ce\":\"\\xC4\\u0100\\u0101\\xC9\\u0104\\xD6\\xDC\\xE1\\u0105\\u010C\\xE4\\u010D\\u0106\\u0107\\xE9\\u0179\\u017A\\u010E\\xED\\u010F\\u0112\\u0113\\u0116\\xF3\\u0117\\xF4\\xF6\\xF5\\xFA\\u011A\\u011B\\xFC\\u2020\\xB0\\u0118\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\u0119\\xA8\\u2260\\u0123\\u012E\\u012F\\u012A\\u2264\\u2265\\u012B\\u0136\\u2202\\u2211\\u0142\\u013B\\u013C\\u013D\\u013E\\u0139\\u013A\\u0145\\u0146\\u0143\\xAC\\u221A\\u0144\\u0147\\u2206\\xAB\\xBB\\u2026\\xA0\\u0148\\u0150\\xD5\\u0151\\u014C\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\u014D\\u0154\\u0155\\u0158\\u2039\\u203A\\u0159\\u0156\\u0157\\u0160\\u201A\\u201E\\u0161\\u015A\\u015B\\xC1\\u0164\\u0165\\xCD\\u017D\\u017E\\u016A\\xD3\\xD4\\u016B\\u016E\\xDA\\u016F\\u0170\\u0171\\u0172\\u0173\\xDD\\xFD\\u0137\\u017B\\u0141\\u017C\\u0122\\u02C7\",macintosh:\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\xC6\\xD8\\u221E\\xB1\\u2264\\u2265\\xA5\\xB5\\u2202\\u2211\\u220F\\u03C0\\u222B\\xAA\\xBA\\u03A9\\xE6\\xF8\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\xFF\\u0178\\u2044\\u20AC\\u2039\\u203A\\uFB01\\uFB02\\u2021\\xB7\\u201A\\u201E\\u2030\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\uF8FF\\xD2\\xDA\\xDB\\xD9\\u0131\\u02C6\\u02DC\\xAF\\u02D8\\u02D9\\u02DA\\xB8\\u02DD\\u02DB\\u02C7\",\"x-mac-romanian\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\u0102\\u0218\\u221E\\xB1\\u2264\\u2265\\xA5\\xB5\\u2202\\u2211\\u220F\\u03C0\\u222B\\xAA\\xBA\\u03A9\\u0103\\u0219\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\xFF\\u0178\\u2044\\u20AC\\u2039\\u203A\\u021A\\u021B\\u2021\\xB7\\u201A\\u201E\\u2030\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\uF8FF\\xD2\\xDA\\xDB\\xD9\\u0131\\u02C6\\u02DC\\xAF\\u02D8\\u02D9\\u02DA\\xB8\\u02DD\\u02DB\\u02C7\",\"x-mac-turkish\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\xC6\\xD8\\u221E\\xB1\\u2264\\u2265\\xA5\\xB5\\u2202\\u2211\\u220F\\u03C0\\u222B\\xAA\\xBA\\u03A9\\xE6\\xF8\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\xFF\\u0178\\u011E\\u011F\\u0130\\u0131\\u015E\\u015F\\u2021\\xB7\\u201A\\u201E\\u2030\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\uF8FF\\xD2\\xDA\\xDB\\xD9\\uF8A0\\u02C6\\u02DC\\xAF\\u02D8\\u02D9\\u02DA\\xB8\\u02DD\\u02DB\\u02C7\"};Fe.MACSTRING=function(e,r,t,a){var n=cr[a];if(n!==void 0){for(var s=\"\",i=0;i<t;i++){var u=e.getUint8(r+i);u<=127?s+=String.fromCharCode(u):s+=n[u&127]}return s}};var He=typeof WeakMap==\"function\"&&new WeakMap,ze,ka=function(e){if(!ze){ze={};for(var r in cr)ze[r]=new String(r)}var t=ze[e];if(t!==void 0){if(He){var a=He.get(t);if(a!==void 0)return a}var n=cr[e];if(n!==void 0){for(var s={},i=0;i<n.length;i++)s[n.charCodeAt(i)]=i+128;return He&&He.set(t,s),s}}};g.MACSTRING=function(e,r){var t=ka(r);if(t!==void 0){for(var a=[],n=0;n<e.length;n++){var s=e.charCodeAt(n);if(s>=128&&(s=t[s],s===void 0))return;a[n]=s}return a}};E.MACSTRING=function(e,r){var t=g.MACSTRING(e,r);return t!==void 0?t.length:0};function vr(e){return e>=-128&&e<=127}function Fa(e,r,t){for(var a=0,n=e.length;r<n&&a<64&&e[r]===0;)++r,++a;return t.push(128|a-1),r}function Ua(e,r,t){for(var a=0,n=e.length,s=r;s<n&&a<64;){var i=e[s];if(!vr(i)||i===0&&s+1<n&&e[s+1]===0)break;++s,++a}t.push(a-1);for(var u=r;u<s;++u)t.push(e[u]+256&255);return s}function Ca(e,r,t){for(var a=0,n=e.length,s=r;s<n&&a<64;){var i=e[s];if(i===0||vr(i)&&s+1<n&&vr(e[s+1]))break;++s,++a}t.push(64|a-1);for(var u=r;u<s;++u){var o=e[u];t.push(o+65536>>8&255,o+256&255)}return s}g.VARDELTAS=function(e){for(var r=0,t=[];r<e.length;){var a=e[r];a===0?r=Fa(e,r,t):a>=-128&&a<=127?r=Ua(e,r,t):r=Ca(e,r,t)}return t};g.INDEX=function(e){for(var r=1,t=[r],a=[],n=0;n<e.length;n+=1){var s=g.OBJECT(e[n]);Array.prototype.push.apply(a,s),r+=s.length,t.push(r)}if(a.length===0)return[0,0];for(var i=[],u=1+Math.floor(Math.log(r)/Math.log(2))/8|0,o=[void 0,g.BYTE,g.USHORT,g.UINT24,g.ULONG][u],l=0;l<t.length;l+=1){var f=o(t[l]);Array.prototype.push.apply(i,f)}return Array.prototype.concat(g.Card16(e.length),g.OffSize(u),i,a)};E.INDEX=function(e){return g.INDEX(e).length};g.DICT=function(e){for(var r=[],t=Object.keys(e),a=t.length,n=0;n<a;n+=1){var s=parseInt(t[n],0),i=e[s];r=r.concat(g.OPERAND(i.value,i.type)),r=r.concat(g.OPERATOR(s))}return r};E.DICT=function(e){return g.DICT(e).length};g.OPERATOR=function(e){return e<1200?[e]:[12,e-1200]};g.OPERAND=function(e,r){var t=[];if(Array.isArray(r))for(var a=0;a<r.length;a+=1)U.argument(e.length===r.length,\"Not enough arguments given for type\"+r),t=t.concat(g.OPERAND(e[a],r[a]));else if(r===\"SID\")t=t.concat(g.NUMBER(e));else if(r===\"offset\")t=t.concat(g.NUMBER32(e));else if(r===\"number\")t=t.concat(g.NUMBER(e));else if(r===\"real\")t=t.concat(g.REAL(e));else throw new Error(\"Unknown operand type \"+r);return t};g.OP=g.BYTE;E.OP=E.BYTE;var We=typeof WeakMap==\"function\"&&new WeakMap;g.CHARSTRING=function(e){if(We){var r=We.get(e);if(r!==void 0)return r}for(var t=[],a=e.length,n=0;n<a;n+=1){var s=e[n];t=t.concat(g[s.type](s.value))}return We&&We.set(e,t),t};E.CHARSTRING=function(e){return g.CHARSTRING(e).length};g.OBJECT=function(e){var r=g[e.type];return U.argument(r!==void 0,\"No encoding function for type \"+e.type),r(e.value)};E.OBJECT=function(e){var r=E[e.type];return U.argument(r!==void 0,\"No sizeOf function for type \"+e.type),r(e.value)};g.TABLE=function(e){for(var r=[],t=e.fields.length,a=[],n=[],s=0;s<t;s+=1){var i=e.fields[s],u=g[i.type];U.argument(u!==void 0,\"No encoding function for field type \"+i.type+\" (\"+i.name+\")\");var o=e[i.name];o===void 0&&(o=i.value);var l=u(o);i.type===\"TABLE\"?(n.push(r.length),r=r.concat([0,0]),a.push(l)):r=r.concat(l)}for(var f=0;f<a.length;f+=1){var h=n[f],p=r.length;U.argument(p<65536,\"Table \"+e.tableName+\" too big.\"),r[h]=p>>8,r[h+1]=p&255,r=r.concat(a[f])}return r};E.TABLE=function(e){for(var r=0,t=e.fields.length,a=0;a<t;a+=1){var n=e.fields[a],s=E[n.type];U.argument(s!==void 0,\"No sizeOf function for field type \"+n.type+\" (\"+n.name+\")\");var i=e[n.name];i===void 0&&(i=n.value),r+=s(i),n.type===\"TABLE\"&&(r+=2)}return r};g.RECORD=g.TABLE;E.RECORD=E.TABLE;g.LITERAL=function(e){return e};E.LITERAL=function(e){return e.length};function z(e,r,t){if(r.length&&(r[0].name!==\"coverageFormat\"||r[0].value===1))for(var a=0;a<r.length;a+=1){var n=r[a];this[n.name]=n.value}if(this.tableName=e,this.fields=r,t)for(var s=Object.keys(t),i=0;i<s.length;i+=1){var u=s[i],o=t[u];this[u]!==void 0&&(this[u]=o)}}z.prototype.encode=function(){return g.TABLE(this)};z.prototype.sizeOf=function(){return E.TABLE(this)};function Re(e,r,t){t===void 0&&(t=r.length);var a=new Array(r.length+1);a[0]={name:e+\"Count\",type:\"USHORT\",value:t};for(var n=0;n<r.length;n++)a[n+1]={name:e+n,type:\"USHORT\",value:r[n]};return a}function dr(e,r,t){var a=r.length,n=new Array(a+1);n[0]={name:e+\"Count\",type:\"USHORT\",value:a};for(var s=0;s<a;s++)n[s+1]={name:e+s,type:\"TABLE\",value:t(r[s],s)};return n}function we(e,r,t){var a=r.length,n=[];n[0]={name:e+\"Count\",type:\"USHORT\",value:a};for(var s=0;s<a;s++)n=n.concat(t(r[s],s));return n}function Ye(e){e.format===1?z.call(this,\"coverageTable\",[{name:\"coverageFormat\",type:\"USHORT\",value:1}].concat(Re(\"glyph\",e.glyphs))):e.format===2?z.call(this,\"coverageTable\",[{name:\"coverageFormat\",type:\"USHORT\",value:2}].concat(we(\"rangeRecord\",e.ranges,function(r){return[{name:\"startGlyphID\",type:\"USHORT\",value:r.start},{name:\"endGlyphID\",type:\"USHORT\",value:r.end},{name:\"startCoverageIndex\",type:\"USHORT\",value:r.index}]}))):U.assert(!1,\"Coverage format must be 1 or 2.\")}Ye.prototype=Object.create(z.prototype);Ye.prototype.constructor=Ye;function Ze(e){z.call(this,\"scriptListTable\",we(\"scriptRecord\",e,function(r,t){var a=r.script,n=a.defaultLangSys;return U.assert(!!n,\"Unable to write GSUB: script \"+r.tag+\" has no default language system.\"),[{name:\"scriptTag\"+t,type:\"TAG\",value:r.tag},{name:\"script\"+t,type:\"TABLE\",value:new z(\"scriptTable\",[{name:\"defaultLangSys\",type:\"TABLE\",value:new z(\"defaultLangSys\",[{name:\"lookupOrder\",type:\"USHORT\",value:0},{name:\"reqFeatureIndex\",type:\"USHORT\",value:n.reqFeatureIndex}].concat(Re(\"featureIndex\",n.featureIndexes)))}].concat(we(\"langSys\",a.langSysRecords,function(s,i){var u=s.langSys;return[{name:\"langSysTag\"+i,type:\"TAG\",value:s.tag},{name:\"langSys\"+i,type:\"TABLE\",value:new z(\"langSys\",[{name:\"lookupOrder\",type:\"USHORT\",value:0},{name:\"reqFeatureIndex\",type:\"USHORT\",value:u.reqFeatureIndex}].concat(Re(\"featureIndex\",u.featureIndexes)))}]})))}]}))}Ze.prototype=Object.create(z.prototype);Ze.prototype.constructor=Ze;function Qe(e){z.call(this,\"featureListTable\",we(\"featureRecord\",e,function(r,t){var a=r.feature;return[{name:\"featureTag\"+t,type:\"TAG\",value:r.tag},{name:\"feature\"+t,type:\"TABLE\",value:new z(\"featureTable\",[{name:\"featureParams\",type:\"USHORT\",value:a.featureParams}].concat(Re(\"lookupListIndex\",a.lookupListIndexes)))}]}))}Qe.prototype=Object.create(z.prototype);Qe.prototype.constructor=Qe;function Ke(e,r){z.call(this,\"lookupListTable\",dr(\"lookup\",e,function(t){var a=r[t.lookupType];return U.assert(!!a,\"Unable to write GSUB lookup type \"+t.lookupType+\" tables.\"),new z(\"lookupTable\",[{name:\"lookupType\",type:\"USHORT\",value:t.lookupType},{name:\"lookupFlag\",type:\"USHORT\",value:t.lookupFlag}].concat(dr(\"subtable\",t.subtables,a)))}))}Ke.prototype=Object.create(z.prototype);Ke.prototype.constructor=Ke;var b={Table:z,Record:z,Coverage:Ye,ScriptList:Ze,FeatureList:Qe,LookupList:Ke,ushortList:Re,tableList:dr,recordList:we};function Hr(e,r){return e.getUint8(r)}function Je(e,r){return e.getUint16(r,!1)}function Ea(e,r){return e.getInt16(r,!1)}function Fr(e,r){return e.getUint32(r,!1)}function Ct(e,r){var t=e.getInt16(r,!1),a=e.getUint16(r+2,!1);return t+a/65535}function Oa(e,r){for(var t=\"\",a=r;a<r+4;a+=1)t+=String.fromCharCode(e.getInt8(a));return t}function La(e,r,t){for(var a=0,n=0;n<t;n+=1)a<<=8,a+=e.getUint8(r+n);return a}function Ra(e,r,t){for(var a=[],n=r;n<t;n+=1)a.push(e.getUint8(n));return a}function wa(e){for(var r=\"\",t=0;t<e.length;t+=1)r+=String.fromCharCode(e[t]);return r}var Da={byte:1,uShort:2,short:2,uLong:4,fixed:4,longDateTime:8,tag:4};function v(e,r){this.data=e,this.offset=r,this.relativeOffset=0}v.prototype.parseByte=function(){var e=this.data.getUint8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e};v.prototype.parseChar=function(){var e=this.data.getInt8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e};v.prototype.parseCard8=v.prototype.parseByte;v.prototype.parseUShort=function(){var e=this.data.getUint16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e};v.prototype.parseCard16=v.prototype.parseUShort;v.prototype.parseSID=v.prototype.parseUShort;v.prototype.parseOffset16=v.prototype.parseUShort;v.prototype.parseShort=function(){var e=this.data.getInt16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e};v.prototype.parseF2Dot14=function(){var e=this.data.getInt16(this.offset+this.relativeOffset)/16384;return this.relativeOffset+=2,e};v.prototype.parseULong=function(){var e=Fr(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e};v.prototype.parseOffset32=v.prototype.parseULong;v.prototype.parseFixed=function(){var e=Ct(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e};v.prototype.parseString=function(e){var r=this.data,t=this.offset+this.relativeOffset,a=\"\";this.relativeOffset+=e;for(var n=0;n<e;n++)a+=String.fromCharCode(r.getUint8(t+n));return a};v.prototype.parseTag=function(){return this.parseString(4)};v.prototype.parseLongDateTime=function(){var e=Fr(this.data,this.offset+this.relativeOffset+4);return e-=2082844800,this.relativeOffset+=8,e};v.prototype.parseVersion=function(e){var r=Je(this.data,this.offset+this.relativeOffset),t=Je(this.data,this.offset+this.relativeOffset+2);return this.relativeOffset+=4,e===void 0&&(e=4096),r+t/e/10};v.prototype.skip=function(e,r){r===void 0&&(r=1),this.relativeOffset+=Da[e]*r};v.prototype.parseULongList=function(e){e===void 0&&(e=this.parseULong());for(var r=new Array(e),t=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)r[n]=t.getUint32(a),a+=4;return this.relativeOffset+=e*4,r};v.prototype.parseOffset16List=v.prototype.parseUShortList=function(e){e===void 0&&(e=this.parseUShort());for(var r=new Array(e),t=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)r[n]=t.getUint16(a),a+=2;return this.relativeOffset+=e*2,r};v.prototype.parseShortList=function(e){for(var r=new Array(e),t=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)r[n]=t.getInt16(a),a+=2;return this.relativeOffset+=e*2,r};v.prototype.parseByteList=function(e){for(var r=new Array(e),t=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)r[n]=t.getUint8(a++);return this.relativeOffset+=e,r};v.prototype.parseList=function(e,r){r||(r=e,e=this.parseUShort());for(var t=new Array(e),a=0;a<e;a++)t[a]=r.call(this);return t};v.prototype.parseList32=function(e,r){r||(r=e,e=this.parseULong());for(var t=new Array(e),a=0;a<e;a++)t[a]=r.call(this);return t};v.prototype.parseRecordList=function(e,r){r||(r=e,e=this.parseUShort());for(var t=new Array(e),a=Object.keys(r),n=0;n<e;n++){for(var s={},i=0;i<a.length;i++){var u=a[i],o=r[u];s[u]=o.call(this)}t[n]=s}return t};v.prototype.parseRecordList32=function(e,r){r||(r=e,e=this.parseULong());for(var t=new Array(e),a=Object.keys(r),n=0;n<e;n++){for(var s={},i=0;i<a.length;i++){var u=a[i],o=r[u];s[u]=o.call(this)}t[n]=s}return t};v.prototype.parseStruct=function(e){if(typeof e==\"function\")return e.call(this);for(var r=Object.keys(e),t={},a=0;a<r.length;a++){var n=r[a],s=e[n];t[n]=s.call(this)}return t};v.prototype.parseValueRecord=function(e){if(e===void 0&&(e=this.parseUShort()),e!==0){var r={};return e&1&&(r.xPlacement=this.parseShort()),e&2&&(r.yPlacement=this.parseShort()),e&4&&(r.xAdvance=this.parseShort()),e&8&&(r.yAdvance=this.parseShort()),e&16&&(r.xPlaDevice=void 0,this.parseShort()),e&32&&(r.yPlaDevice=void 0,this.parseShort()),e&64&&(r.xAdvDevice=void 0,this.parseShort()),e&128&&(r.yAdvDevice=void 0,this.parseShort()),r}};v.prototype.parseValueRecordList=function(){for(var e=this.parseUShort(),r=this.parseUShort(),t=new Array(r),a=0;a<r;a++)t[a]=this.parseValueRecord(e);return t};v.prototype.parsePointer=function(e){var r=this.parseOffset16();if(r>0)return new v(this.data,this.offset+r).parseStruct(e)};v.prototype.parsePointer32=function(e){var r=this.parseOffset32();if(r>0)return new v(this.data,this.offset+r).parseStruct(e)};v.prototype.parseListOfLists=function(e){for(var r=this.parseOffset16List(),t=r.length,a=this.relativeOffset,n=new Array(t),s=0;s<t;s++){var i=r[s];if(i===0){n[s]=void 0;continue}if(this.relativeOffset=i,e){for(var u=this.parseOffset16List(),o=new Array(u.length),l=0;l<u.length;l++)this.relativeOffset=i+u[l],o[l]=e.call(this);n[s]=o}else n[s]=this.parseUShortList()}return this.relativeOffset=a,n};v.prototype.parseCoverage=function(){var e=this.offset+this.relativeOffset,r=this.parseUShort(),t=this.parseUShort();if(r===1)return{format:1,glyphs:this.parseUShortList(t)};if(r===2){for(var a=new Array(t),n=0;n<t;n++)a[n]={start:this.parseUShort(),end:this.parseUShort(),index:this.parseUShort()};return{format:2,ranges:a}}throw new Error(\"0x\"+e.toString(16)+\": Coverage format must be 1 or 2.\")};v.prototype.parseClassDef=function(){var e=this.offset+this.relativeOffset,r=this.parseUShort();if(r===1)return{format:1,startGlyph:this.parseUShort(),classes:this.parseUShortList()};if(r===2)return{format:2,ranges:this.parseRecordList({start:v.uShort,end:v.uShort,classId:v.uShort})};throw new Error(\"0x\"+e.toString(16)+\": ClassDef format must be 1 or 2.\")};v.list=function(e,r){return function(){return this.parseList(e,r)}};v.list32=function(e,r){return function(){return this.parseList32(e,r)}};v.recordList=function(e,r){return function(){return this.parseRecordList(e,r)}};v.recordList32=function(e,r){return function(){return this.parseRecordList32(e,r)}};v.pointer=function(e){return function(){return this.parsePointer(e)}};v.pointer32=function(e){return function(){return this.parsePointer32(e)}};v.tag=v.prototype.parseTag;v.byte=v.prototype.parseByte;v.uShort=v.offset16=v.prototype.parseUShort;v.uShortList=v.prototype.parseUShortList;v.uLong=v.offset32=v.prototype.parseULong;v.uLongList=v.prototype.parseULongList;v.struct=v.prototype.parseStruct;v.coverage=v.prototype.parseCoverage;v.classDef=v.prototype.parseClassDef;var zr={reserved:v.uShort,reqFeatureIndex:v.uShort,featureIndexes:v.uShortList};v.prototype.parseScriptList=function(){return this.parsePointer(v.recordList({tag:v.tag,script:v.pointer({defaultLangSys:v.pointer(zr),langSysRecords:v.recordList({tag:v.tag,langSys:v.pointer(zr)})})}))||[]};v.prototype.parseFeatureList=function(){return this.parsePointer(v.recordList({tag:v.tag,feature:v.pointer({featureParams:v.offset16,lookupListIndexes:v.uShortList})}))||[]};v.prototype.parseLookupList=function(e){return this.parsePointer(v.list(v.pointer(function(){var r=this.parseUShort();U.argument(1<=r&&r<=9,\"GPOS/GSUB lookup type \"+r+\" unknown.\");var t=this.parseUShort(),a=t&16;return{lookupType:r,lookupFlag:t,subtables:this.parseList(v.pointer(e[r])),markFilteringSet:a?this.parseUShort():void 0}})))||[]};v.prototype.parseFeatureVariationsList=function(){return this.parsePointer32(function(){var e=this.parseUShort(),r=this.parseUShort();U.argument(e===1&&r<1,\"GPOS/GSUB feature variations table unknown.\");var t=this.parseRecordList32({conditionSetOffset:v.offset32,featureTableSubstitutionOffset:v.offset32});return t})||[]};var k={getByte:Hr,getCard8:Hr,getUShort:Je,getCard16:Je,getShort:Ea,getULong:Fr,getFixed:Ct,getTag:Oa,getOffset:La,getBytes:Ra,bytesToString:wa,Parser:v};function Aa(e,r){r.parseUShort(),e.length=r.parseULong(),e.language=r.parseULong();var t;e.groupCount=t=r.parseULong(),e.glyphIndexMap={};for(var a=0;a<t;a+=1)for(var n=r.parseULong(),s=r.parseULong(),i=r.parseULong(),u=n;u<=s;u+=1)e.glyphIndexMap[u]=i,i++}function Ba(e,r,t,a,n){e.length=r.parseUShort(),e.language=r.parseUShort();var s;e.segCount=s=r.parseUShort()>>1,r.skip(\"uShort\",3),e.glyphIndexMap={};for(var i=new k.Parser(t,a+n+14),u=new k.Parser(t,a+n+16+s*2),o=new k.Parser(t,a+n+16+s*4),l=new k.Parser(t,a+n+16+s*6),f=a+n+16+s*8,h=0;h<s-1;h+=1)for(var p=void 0,c=i.parseUShort(),d=u.parseUShort(),x=o.parseShort(),m=l.parseUShort(),y=d;y<=c;y+=1)m!==0?(f=l.offset+l.relativeOffset-2,f+=m,f+=(y-d)*2,p=k.getUShort(t,f),p!==0&&(p=p+x&65535)):p=y+x&65535,e.glyphIndexMap[y]=p}function Ia(e,r){var t={};t.version=k.getUShort(e,r),U.argument(t.version===0,\"cmap table version should be 0.\"),t.numTables=k.getUShort(e,r+2);for(var a=-1,n=t.numTables-1;n>=0;n-=1){var s=k.getUShort(e,r+4+n*8),i=k.getUShort(e,r+4+n*8+2);if(s===3&&(i===0||i===1||i===10)||s===0&&(i===0||i===1||i===2||i===3||i===4)){a=k.getULong(e,r+4+n*8+4);break}}if(a===-1)throw new Error(\"No valid cmap sub-tables found.\");var u=new k.Parser(e,r+a);if(t.format=u.parseUShort(),t.format===12)Aa(t,u);else if(t.format===4)Ba(t,u,e,r,a);else throw new Error(\"Only format 4 and 12 cmap tables are supported (found format \"+t.format+\").\");return t}function Ma(e,r,t){e.segments.push({end:r,start:r,delta:-(r-t),offset:0,glyphIndex:t})}function Pa(e){e.segments.push({end:65535,start:65535,delta:1,offset:0})}function Ga(e){var r=!0,t;for(t=e.length-1;t>0;t-=1){var a=e.get(t);if(a.unicode>65535){console.log(\"Adding CMAP format 12 (needed!)\"),r=!1;break}}var n=[{name:\"version\",type:\"USHORT\",value:0},{name:\"numTables\",type:\"USHORT\",value:r?1:2},{name:\"platformID\",type:\"USHORT\",value:3},{name:\"encodingID\",type:\"USHORT\",value:1},{name:\"offset\",type:\"ULONG\",value:r?12:12+8}];r||(n=n.concat([{name:\"cmap12PlatformID\",type:\"USHORT\",value:3},{name:\"cmap12EncodingID\",type:\"USHORT\",value:10},{name:\"cmap12Offset\",type:\"ULONG\",value:0}])),n=n.concat([{name:\"format\",type:\"USHORT\",value:4},{name:\"cmap4Length\",type:\"USHORT\",value:0},{name:\"language\",type:\"USHORT\",value:0},{name:\"segCountX2\",type:\"USHORT\",value:0},{name:\"searchRange\",type:\"USHORT\",value:0},{name:\"entrySelector\",type:\"USHORT\",value:0},{name:\"rangeShift\",type:\"USHORT\",value:0}]);var s=new b.Table(\"cmap\",n);for(s.segments=[],t=0;t<e.length;t+=1){for(var i=e.get(t),u=0;u<i.unicodes.length;u+=1)Ma(s,i.unicodes[u],t);s.segments=s.segments.sort(function(C,S){return C.start-S.start})}Pa(s);var o=s.segments.length,l=0,f=[],h=[],p=[],c=[],d=[],x=[];for(t=0;t<o;t+=1){var m=s.segments[t];m.end<=65535&&m.start<=65535?(f=f.concat({name:\"end_\"+t,type:\"USHORT\",value:m.end}),h=h.concat({name:\"start_\"+t,type:\"USHORT\",value:m.start}),p=p.concat({name:\"idDelta_\"+t,type:\"SHORT\",value:m.delta}),c=c.concat({name:\"idRangeOffset_\"+t,type:\"USHORT\",value:m.offset}),m.glyphId!==void 0&&(d=d.concat({name:\"glyph_\"+t,type:\"USHORT\",value:m.glyphId}))):l+=1,!r&&m.glyphIndex!==void 0&&(x=x.concat({name:\"cmap12Start_\"+t,type:\"ULONG\",value:m.start}),x=x.concat({name:\"cmap12End_\"+t,type:\"ULONG\",value:m.end}),x=x.concat({name:\"cmap12Glyph_\"+t,type:\"ULONG\",value:m.glyphIndex}))}if(s.segCountX2=(o-l)*2,s.searchRange=Math.pow(2,Math.floor(Math.log(o-l)/Math.log(2)))*2,s.entrySelector=Math.log(s.searchRange/2)/Math.log(2),s.rangeShift=s.segCountX2-s.searchRange,s.fields=s.fields.concat(f),s.fields.push({name:\"reservedPad\",type:\"USHORT\",value:0}),s.fields=s.fields.concat(h),s.fields=s.fields.concat(p),s.fields=s.fields.concat(c),s.fields=s.fields.concat(d),s.cmap4Length=14+f.length*2+2+h.length*2+p.length*2+c.length*2+d.length*2,!r){var y=16+x.length*4;s.cmap12Offset=12+2*2+4+s.cmap4Length,s.fields=s.fields.concat([{name:\"cmap12Format\",type:\"USHORT\",value:12},{name:\"cmap12Reserved\",type:\"USHORT\",value:0},{name:\"cmap12Length\",type:\"ULONG\",value:y},{name:\"cmap12Language\",type:\"ULONG\",value:0},{name:\"cmap12nGroups\",type:\"ULONG\",value:x.length/3}]),s.fields=s.fields.concat(x)}return s}var Et={parse:Ia,make:Ga},qe=[\".notdef\",\"space\",\"exclam\",\"quotedbl\",\"numbersign\",\"dollar\",\"percent\",\"ampersand\",\"quoteright\",\"parenleft\",\"parenright\",\"asterisk\",\"plus\",\"comma\",\"hyphen\",\"period\",\"slash\",\"zero\",\"one\",\"two\",\"three\",\"four\",\"five\",\"six\",\"seven\",\"eight\",\"nine\",\"colon\",\"semicolon\",\"less\",\"equal\",\"greater\",\"question\",\"at\",\"A\",\"B\",\"C\",\"D\",\"E\",\"F\",\"G\",\"H\",\"I\",\"J\",\"K\",\"L\",\"M\",\"N\",\"O\",\"P\",\"Q\",\"R\",\"S\",\"T\",\"U\",\"V\",\"W\",\"X\",\"Y\",\"Z\",\"bracketleft\",\"backslash\",\"bracketright\",\"asciicircum\",\"underscore\",\"quoteleft\",\"a\",\"b\",\"c\",\"d\",\"e\",\"f\",\"g\",\"h\",\"i\",\"j\",\"k\",\"l\",\"m\",\"n\",\"o\",\"p\",\"q\",\"r\",\"s\",\"t\",\"u\",\"v\",\"w\",\"x\",\"y\",\"z\",\"braceleft\",\"bar\",\"braceright\",\"asciitilde\",\"exclamdown\",\"cent\",\"sterling\",\"fraction\",\"yen\",\"florin\",\"section\",\"currency\",\"quotesingle\",\"quotedblleft\",\"guillemotleft\",\"guilsinglleft\",\"guilsinglright\",\"fi\",\"fl\",\"endash\",\"dagger\",\"daggerdbl\",\"periodcentered\",\"paragraph\",\"bullet\",\"quotesinglbase\",\"quotedblbase\",\"quotedblright\",\"guillemotright\",\"ellipsis\",\"perthousand\",\"questiondown\",\"grave\",\"acute\",\"circumflex\",\"tilde\",\"macron\",\"breve\",\"dotaccent\",\"dieresis\",\"ring\",\"cedilla\",\"hungarumlaut\",\"ogonek\",\"caron\",\"emdash\",\"AE\",\"ordfeminine\",\"Lslash\",\"Oslash\",\"OE\",\"ordmasculine\",\"ae\",\"dotlessi\",\"lslash\",\"oslash\",\"oe\",\"germandbls\",\"onesuperior\",\"logicalnot\",\"mu\",\"trademark\",\"Eth\",\"onehalf\",\"plusminus\",\"Thorn\",\"onequarter\",\"divide\",\"brokenbar\",\"degree\",\"thorn\",\"threequarters\",\"twosuperior\",\"registered\",\"minus\",\"eth\",\"multiply\",\"threesuperior\",\"copyright\",\"Aacute\",\"Acircumflex\",\"Adieresis\",\"Agrave\",\"Aring\",\"Atilde\",\"Ccedilla\",\"Eacute\",\"Ecircumflex\",\"Edieresis\",\"Egrave\",\"Iacute\",\"Icircumflex\",\"Idieresis\",\"Igrave\",\"Ntilde\",\"Oacute\",\"Ocircumflex\",\"Odieresis\",\"Ograve\",\"Otilde\",\"Scaron\",\"Uacute\",\"Ucircumflex\",\"Udieresis\",\"Ugrave\",\"Yacute\",\"Ydieresis\",\"Zcaron\",\"aacute\",\"acircumflex\",\"adieresis\",\"agrave\",\"aring\",\"atilde\",\"ccedilla\",\"eacute\",\"ecircumflex\",\"edieresis\",\"egrave\",\"iacute\",\"icircumflex\",\"idieresis\",\"igrave\",\"ntilde\",\"oacute\",\"ocircumflex\",\"odieresis\",\"ograve\",\"otilde\",\"scaron\",\"uacute\",\"ucircumflex\",\"udieresis\",\"ugrave\",\"yacute\",\"ydieresis\",\"zcaron\",\"exclamsmall\",\"Hungarumlautsmall\",\"dollaroldstyle\",\"dollarsuperior\",\"ampersandsmall\",\"Acutesmall\",\"parenleftsuperior\",\"parenrightsuperior\",\"266 ff\",\"onedotenleader\",\"zerooldstyle\",\"oneoldstyle\",\"twooldstyle\",\"threeoldstyle\",\"fouroldstyle\",\"fiveoldstyle\",\"sixoldstyle\",\"sevenoldstyle\",\"eightoldstyle\",\"nineoldstyle\",\"commasuperior\",\"threequartersemdash\",\"periodsuperior\",\"questionsmall\",\"asuperior\",\"bsuperior\",\"centsuperior\",\"dsuperior\",\"esuperior\",\"isuperior\",\"lsuperior\",\"msuperior\",\"nsuperior\",\"osuperior\",\"rsuperior\",\"ssuperior\",\"tsuperior\",\"ff\",\"ffi\",\"ffl\",\"parenleftinferior\",\"parenrightinferior\",\"Circumflexsmall\",\"hyphensuperior\",\"Gravesmall\",\"Asmall\",\"Bsmall\",\"Csmall\",\"Dsmall\",\"Esmall\",\"Fsmall\",\"Gsmall\",\"Hsmall\",\"Ismall\",\"Jsmall\",\"Ksmall\",\"Lsmall\",\"Msmall\",\"Nsmall\",\"Osmall\",\"Psmall\",\"Qsmall\",\"Rsmall\",\"Ssmall\",\"Tsmall\",\"Usmall\",\"Vsmall\",\"Wsmall\",\"Xsmall\",\"Ysmall\",\"Zsmall\",\"colonmonetary\",\"onefitted\",\"rupiah\",\"Tildesmall\",\"exclamdownsmall\",\"centoldstyle\",\"Lslashsmall\",\"Scaronsmall\",\"Zcaronsmall\",\"Dieresissmall\",\"Brevesmall\",\"Caronsmall\",\"Dotaccentsmall\",\"Macronsmall\",\"figuredash\",\"hypheninferior\",\"Ogoneksmall\",\"Ringsmall\",\"Cedillasmall\",\"questiondownsmall\",\"oneeighth\",\"threeeighths\",\"fiveeighths\",\"seveneighths\",\"onethird\",\"twothirds\",\"zerosuperior\",\"foursuperior\",\"fivesuperior\",\"sixsuperior\",\"sevensuperior\",\"eightsuperior\",\"ninesuperior\",\"zeroinferior\",\"oneinferior\",\"twoinferior\",\"threeinferior\",\"fourinferior\",\"fiveinferior\",\"sixinferior\",\"seveninferior\",\"eightinferior\",\"nineinferior\",\"centinferior\",\"dollarinferior\",\"periodinferior\",\"commainferior\",\"Agravesmall\",\"Aacutesmall\",\"Acircumflexsmall\",\"Atildesmall\",\"Adieresissmall\",\"Aringsmall\",\"AEsmall\",\"Ccedillasmall\",\"Egravesmall\",\"Eacutesmall\",\"Ecircumflexsmall\",\"Edieresissmall\",\"Igravesmall\",\"Iacutesmall\",\"Icircumflexsmall\",\"Idieresissmall\",\"Ethsmall\",\"Ntildesmall\",\"Ogravesmall\",\"Oacutesmall\",\"Ocircumflexsmall\",\"Otildesmall\",\"Odieresissmall\",\"OEsmall\",\"Oslashsmall\",\"Ugravesmall\",\"Uacutesmall\",\"Ucircumflexsmall\",\"Udieresissmall\",\"Yacutesmall\",\"Thornsmall\",\"Ydieresissmall\",\"001.000\",\"001.001\",\"001.002\",\"001.003\",\"Black\",\"Bold\",\"Book\",\"Light\",\"Medium\",\"Regular\",\"Roman\",\"Semibold\"],Na=[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"space\",\"exclam\",\"quotedbl\",\"numbersign\",\"dollar\",\"percent\",\"ampersand\",\"quoteright\",\"parenleft\",\"parenright\",\"asterisk\",\"plus\",\"comma\",\"hyphen\",\"period\",\"slash\",\"zero\",\"one\",\"two\",\"three\",\"four\",\"five\",\"six\",\"seven\",\"eight\",\"nine\",\"colon\",\"semicolon\",\"less\",\"equal\",\"greater\",\"question\",\"at\",\"A\",\"B\",\"C\",\"D\",\"E\",\"F\",\"G\",\"H\",\"I\",\"J\",\"K\",\"L\",\"M\",\"N\",\"O\",\"P\",\"Q\",\"R\",\"S\",\"T\",\"U\",\"V\",\"W\",\"X\",\"Y\",\"Z\",\"bracketleft\",\"backslash\",\"bracketright\",\"asciicircum\",\"underscore\",\"quoteleft\",\"a\",\"b\",\"c\",\"d\",\"e\",\"f\",\"g\",\"h\",\"i\",\"j\",\"k\",\"l\",\"m\",\"n\",\"o\",\"p\",\"q\",\"r\",\"s\",\"t\",\"u\",\"v\",\"w\",\"x\",\"y\",\"z\",\"braceleft\",\"bar\",\"braceright\",\"asciitilde\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"exclamdown\",\"cent\",\"sterling\",\"fraction\",\"yen\",\"florin\",\"section\",\"currency\",\"quotesingle\",\"quotedblleft\",\"guillemotleft\",\"guilsinglleft\",\"guilsinglright\",\"fi\",\"fl\",\"\",\"endash\",\"dagger\",\"daggerdbl\",\"periodcentered\",\"\",\"paragraph\",\"bullet\",\"quotesinglbase\",\"quotedblbase\",\"quotedblright\",\"guillemotright\",\"ellipsis\",\"perthousand\",\"\",\"questiondown\",\"\",\"grave\",\"acute\",\"circumflex\",\"tilde\",\"macron\",\"breve\",\"dotaccent\",\"dieresis\",\"\",\"ring\",\"cedilla\",\"\",\"hungarumlaut\",\"ogonek\",\"caron\",\"emdash\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"AE\",\"\",\"ordfeminine\",\"\",\"\",\"\",\"\",\"Lslash\",\"Oslash\",\"OE\",\"ordmasculine\",\"\",\"\",\"\",\"\",\"\",\"ae\",\"\",\"\",\"\",\"dotlessi\",\"\",\"\",\"lslash\",\"oslash\",\"oe\",\"germandbls\"],Ha=[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"space\",\"exclamsmall\",\"Hungarumlautsmall\",\"\",\"dollaroldstyle\",\"dollarsuperior\",\"ampersandsmall\",\"Acutesmall\",\"parenleftsuperior\",\"parenrightsuperior\",\"twodotenleader\",\"onedotenleader\",\"comma\",\"hyphen\",\"period\",\"fraction\",\"zerooldstyle\",\"oneoldstyle\",\"twooldstyle\",\"threeoldstyle\",\"fouroldstyle\",\"fiveoldstyle\",\"sixoldstyle\",\"sevenoldstyle\",\"eightoldstyle\",\"nineoldstyle\",\"colon\",\"semicolon\",\"commasuperior\",\"threequartersemdash\",\"periodsuperior\",\"questionsmall\",\"\",\"asuperior\",\"bsuperior\",\"centsuperior\",\"dsuperior\",\"esuperior\",\"\",\"\",\"isuperior\",\"\",\"\",\"lsuperior\",\"msuperior\",\"nsuperior\",\"osuperior\",\"\",\"\",\"rsuperior\",\"ssuperior\",\"tsuperior\",\"\",\"ff\",\"fi\",\"fl\",\"ffi\",\"ffl\",\"parenleftinferior\",\"\",\"parenrightinferior\",\"Circumflexsmall\",\"hyphensuperior\",\"Gravesmall\",\"Asmall\",\"Bsmall\",\"Csmall\",\"Dsmall\",\"Esmall\",\"Fsmall\",\"Gsmall\",\"Hsmall\",\"Ismall\",\"Jsmall\",\"Ksmall\",\"Lsmall\",\"Msmall\",\"Nsmall\",\"Osmall\",\"Psmall\",\"Qsmall\",\"Rsmall\",\"Ssmall\",\"Tsmall\",\"Usmall\",\"Vsmall\",\"Wsmall\",\"Xsmall\",\"Ysmall\",\"Zsmall\",\"colonmonetary\",\"onefitted\",\"rupiah\",\"Tildesmall\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"exclamdownsmall\",\"centoldstyle\",\"Lslashsmall\",\"\",\"\",\"Scaronsmall\",\"Zcaronsmall\",\"Dieresissmall\",\"Brevesmall\",\"Caronsmall\",\"\",\"Dotaccentsmall\",\"\",\"\",\"Macronsmall\",\"\",\"\",\"figuredash\",\"hypheninferior\",\"\",\"\",\"Ogoneksmall\",\"Ringsmall\",\"Cedillasmall\",\"\",\"\",\"\",\"onequarter\",\"onehalf\",\"threequarters\",\"questiondownsmall\",\"oneeighth\",\"threeeighths\",\"fiveeighths\",\"seveneighths\",\"onethird\",\"twothirds\",\"\",\"\",\"zerosuperior\",\"onesuperior\",\"twosuperior\",\"threesuperior\",\"foursuperior\",\"fivesuperior\",\"sixsuperior\",\"sevensuperior\",\"eightsuperior\",\"ninesuperior\",\"zeroinferior\",\"oneinferior\",\"twoinferior\",\"threeinferior\",\"fourinferior\",\"fiveinferior\",\"sixinferior\",\"seveninferior\",\"eightinferior\",\"nineinferior\",\"centinferior\",\"dollarinferior\",\"periodinferior\",\"commainferior\",\"Agravesmall\",\"Aacutesmall\",\"Acircumflexsmall\",\"Atildesmall\",\"Adieresissmall\",\"Aringsmall\",\"AEsmall\",\"Ccedillasmall\",\"Egravesmall\",\"Eacutesmall\",\"Ecircumflexsmall\",\"Edieresissmall\",\"Igravesmall\",\"Iacutesmall\",\"Icircumflexsmall\",\"Idieresissmall\",\"Ethsmall\",\"Ntildesmall\",\"Ogravesmall\",\"Oacutesmall\",\"Ocircumflexsmall\",\"Otildesmall\",\"Odieresissmall\",\"OEsmall\",\"Oslashsmall\",\"Ugravesmall\",\"Uacutesmall\",\"Ucircumflexsmall\",\"Udieresissmall\",\"Yacutesmall\",\"Thornsmall\",\"Ydieresissmall\"],xe=[\".notdef\",\".null\",\"nonmarkingreturn\",\"space\",\"exclam\",\"quotedbl\",\"numbersign\",\"dollar\",\"percent\",\"ampersand\",\"quotesingle\",\"parenleft\",\"parenright\",\"asterisk\",\"plus\",\"comma\",\"hyphen\",\"period\",\"slash\",\"zero\",\"one\",\"two\",\"three\",\"four\",\"five\",\"six\",\"seven\",\"eight\",\"nine\",\"colon\",\"semicolon\",\"less\",\"equal\",\"greater\",\"question\",\"at\",\"A\",\"B\",\"C\",\"D\",\"E\",\"F\",\"G\",\"H\",\"I\",\"J\",\"K\",\"L\",\"M\",\"N\",\"O\",\"P\",\"Q\",\"R\",\"S\",\"T\",\"U\",\"V\",\"W\",\"X\",\"Y\",\"Z\",\"bracketleft\",\"backslash\",\"bracketright\",\"asciicircum\",\"underscore\",\"grave\",\"a\",\"b\",\"c\",\"d\",\"e\",\"f\",\"g\",\"h\",\"i\",\"j\",\"k\",\"l\",\"m\",\"n\",\"o\",\"p\",\"q\",\"r\",\"s\",\"t\",\"u\",\"v\",\"w\",\"x\",\"y\",\"z\",\"braceleft\",\"bar\",\"braceright\",\"asciitilde\",\"Adieresis\",\"Aring\",\"Ccedilla\",\"Eacute\",\"Ntilde\",\"Odieresis\",\"Udieresis\",\"aacute\",\"agrave\",\"acircumflex\",\"adieresis\",\"atilde\",\"aring\",\"ccedilla\",\"eacute\",\"egrave\",\"ecircumflex\",\"edieresis\",\"iacute\",\"igrave\",\"icircumflex\",\"idieresis\",\"ntilde\",\"oacute\",\"ograve\",\"ocircumflex\",\"odieresis\",\"otilde\",\"uacute\",\"ugrave\",\"ucircumflex\",\"udieresis\",\"dagger\",\"degree\",\"cent\",\"sterling\",\"section\",\"bullet\",\"paragraph\",\"germandbls\",\"registered\",\"copyright\",\"trademark\",\"acute\",\"dieresis\",\"notequal\",\"AE\",\"Oslash\",\"infinity\",\"plusminus\",\"lessequal\",\"greaterequal\",\"yen\",\"mu\",\"partialdiff\",\"summation\",\"product\",\"pi\",\"integral\",\"ordfeminine\",\"ordmasculine\",\"Omega\",\"ae\",\"oslash\",\"questiondown\",\"exclamdown\",\"logicalnot\",\"radical\",\"florin\",\"approxequal\",\"Delta\",\"guillemotleft\",\"guillemotright\",\"ellipsis\",\"nonbreakingspace\",\"Agrave\",\"Atilde\",\"Otilde\",\"OE\",\"oe\",\"endash\",\"emdash\",\"quotedblleft\",\"quotedblright\",\"quoteleft\",\"quoteright\",\"divide\",\"lozenge\",\"ydieresis\",\"Ydieresis\",\"fraction\",\"currency\",\"guilsinglleft\",\"guilsinglright\",\"fi\",\"fl\",\"daggerdbl\",\"periodcentered\",\"quotesinglbase\",\"quotedblbase\",\"perthousand\",\"Acircumflex\",\"Ecircumflex\",\"Aacute\",\"Edieresis\",\"Egrave\",\"Iacute\",\"Icircumflex\",\"Idieresis\",\"Igrave\",\"Oacute\",\"Ocircumflex\",\"apple\",\"Ograve\",\"Uacute\",\"Ucircumflex\",\"Ugrave\",\"dotlessi\",\"circumflex\",\"tilde\",\"macron\",\"breve\",\"dotaccent\",\"ring\",\"cedilla\",\"hungarumlaut\",\"ogonek\",\"caron\",\"Lslash\",\"lslash\",\"Scaron\",\"scaron\",\"Zcaron\",\"zcaron\",\"brokenbar\",\"Eth\",\"eth\",\"Yacute\",\"yacute\",\"Thorn\",\"thorn\",\"minus\",\"multiply\",\"onesuperior\",\"twosuperior\",\"threesuperior\",\"onehalf\",\"onequarter\",\"threequarters\",\"franc\",\"Gbreve\",\"gbreve\",\"Idotaccent\",\"Scedilla\",\"scedilla\",\"Cacute\",\"cacute\",\"Ccaron\",\"ccaron\",\"dcroat\"];function Ot(e){this.font=e}Ot.prototype.charToGlyphIndex=function(e){var r=e.codePointAt(0),t=this.font.glyphs;if(t){for(var a=0;a<t.length;a+=1)for(var n=t.get(a),s=0;s<n.unicodes.length;s+=1)if(n.unicodes[s]===r)return a}return null};function Lt(e){this.cmap=e}Lt.prototype.charToGlyphIndex=function(e){return this.cmap.glyphIndexMap[e.codePointAt(0)]||0};function je(e,r){this.encoding=e,this.charset=r}je.prototype.charToGlyphIndex=function(e){var r=e.codePointAt(0),t=this.encoding[r];return this.charset.indexOf(t)};function Ur(e){switch(e.version){case 1:this.names=xe.slice();break;case 2:this.names=new Array(e.numberOfGlyphs);for(var r=0;r<e.numberOfGlyphs;r++)e.glyphNameIndex[r]<xe.length?this.names[r]=xe[e.glyphNameIndex[r]]:this.names[r]=e.names[e.glyphNameIndex[r]-xe.length];break;case 2.5:this.names=new Array(e.numberOfGlyphs);for(var t=0;t<e.numberOfGlyphs;t++)this.names[t]=xe[t+e.glyphNameIndex[t]];break;case 3:this.names=[];break;default:this.names=[];break}}Ur.prototype.nameToGlyphIndex=function(e){return this.names.indexOf(e)};Ur.prototype.glyphIndexToName=function(e){return this.names[e]};function za(e){for(var r,t=e.tables.cmap.glyphIndexMap,a=Object.keys(t),n=0;n<a.length;n+=1){var s=a[n],i=t[s];r=e.glyphs.get(i),r.addUnicode(parseInt(s))}for(var u=0;u<e.glyphs.length;u+=1)r=e.glyphs.get(u),e.cffEncoding?e.isCIDFont?r.name=\"gid\"+u:r.name=e.cffEncoding.charset[u]:e.glyphNames.names&&(r.name=e.glyphNames.glyphIndexToName(u))}function Wa(e){e._IndexToUnicodeMap={};for(var r=e.tables.cmap.glyphIndexMap,t=Object.keys(r),a=0;a<t.length;a+=1){var n=t[a],s=r[n];e._IndexToUnicodeMap[s]===void 0?e._IndexToUnicodeMap[s]={unicodes:[parseInt(n)]}:e._IndexToUnicodeMap[s].unicodes.push(parseInt(n))}}function _a(e,r){r.lowMemory?Wa(e):za(e)}function Va(e,r,t,a,n){e.beginPath(),e.moveTo(r,t),e.lineTo(a,n),e.stroke()}var ye={line:Va};function qa(e,r){var t=r||new P;return{configurable:!0,get:function(){return typeof t==\"function\"&&(t=t()),t},set:function(a){t=a}}}function Q(e){this.bindConstructorValues(e)}Q.prototype.bindConstructorValues=function(e){this.index=e.index||0,this.name=e.name||null,this.unicode=e.unicode||void 0,this.unicodes=e.unicodes||e.unicode!==void 0?[e.unicode]:[],\"xMin\"in e&&(this.xMin=e.xMin),\"yMin\"in e&&(this.yMin=e.yMin),\"xMax\"in e&&(this.xMax=e.xMax),\"yMax\"in e&&(this.yMax=e.yMax),\"advanceWidth\"in e&&(this.advanceWidth=e.advanceWidth),Object.defineProperty(this,\"path\",qa(this,e.path))};Q.prototype.addUnicode=function(e){this.unicodes.length===0&&(this.unicode=e),this.unicodes.push(e)};Q.prototype.getBoundingBox=function(){return this.path.getBoundingBox()};Q.prototype.getPath=function(e,r,t,a,n){e=e!==void 0?e:0,r=r!==void 0?r:0,t=t!==void 0?t:72;var s,i;a||(a={});var u=a.xScale,o=a.yScale;if(a.hinting&&n&&n.hinting&&(i=this.path&&n.hinting.exec(this,t)),i)s=n.hinting.getCommands(i),e=Math.round(e),r=Math.round(r),u=o=1;else{s=this.path.commands;var l=1/(this.path.unitsPerEm||1e3)*t;u===void 0&&(u=l),o===void 0&&(o=l)}for(var f=new P,h=0;h<s.length;h+=1){var p=s[h];p.type===\"M\"?f.moveTo(e+p.x*u,r+-p.y*o):p.type===\"L\"?f.lineTo(e+p.x*u,r+-p.y*o):p.type===\"Q\"?f.quadraticCurveTo(e+p.x1*u,r+-p.y1*o,e+p.x*u,r+-p.y*o):p.type===\"C\"?f.curveTo(e+p.x1*u,r+-p.y1*o,e+p.x2*u,r+-p.y2*o,e+p.x*u,r+-p.y*o):p.type===\"Z\"&&f.closePath()}return f};Q.prototype.getContours=function(){if(this.points===void 0)return[];for(var e=[],r=[],t=0;t<this.points.length;t+=1){var a=this.points[t];r.push(a),a.lastPointOfContour&&(e.push(r),r=[])}return U.argument(r.length===0,\"There are still points left in the current contour.\"),e};Q.prototype.getMetrics=function(){for(var e=this.path.commands,r=[],t=[],a=0;a<e.length;a+=1){var n=e[a];n.type!==\"Z\"&&(r.push(n.x),t.push(n.y)),(n.type===\"Q\"||n.type===\"C\")&&(r.push(n.x1),t.push(n.y1)),n.type===\"C\"&&(r.push(n.x2),t.push(n.y2))}var s={xMin:Math.min.apply(null,r),yMin:Math.min.apply(null,t),xMax:Math.max.apply(null,r),yMax:Math.max.apply(null,t),leftSideBearing:this.leftSideBearing};return isFinite(s.xMin)||(s.xMin=0),isFinite(s.xMax)||(s.xMax=this.advanceWidth),isFinite(s.yMin)||(s.yMin=0),isFinite(s.yMax)||(s.yMax=0),s.rightSideBearing=this.advanceWidth-s.leftSideBearing-(s.xMax-s.xMin),s};Q.prototype.draw=function(e,r,t,a,n){this.getPath(r,t,a,n).draw(e)};Q.prototype.drawPoints=function(e,r,t,a){function n(h,p,c,d){e.beginPath();for(var x=0;x<h.length;x+=1)e.moveTo(p+h[x].x*d,c+h[x].y*d),e.arc(p+h[x].x*d,c+h[x].y*d,2,0,Math.PI*2,!1);e.closePath(),e.fill()}r=r!==void 0?r:0,t=t!==void 0?t:0,a=a!==void 0?a:24;for(var s=1/this.path.unitsPerEm*a,i=[],u=[],o=this.path,l=0;l<o.commands.length;l+=1){var f=o.commands[l];f.x!==void 0&&i.push({x:f.x,y:-f.y}),f.x1!==void 0&&u.push({x:f.x1,y:-f.y1}),f.x2!==void 0&&u.push({x:f.x2,y:-f.y2})}e.fillStyle=\"blue\",n(i,r,t,s),e.fillStyle=\"red\",n(u,r,t,s)};Q.prototype.drawMetrics=function(e,r,t,a){var n;r=r!==void 0?r:0,t=t!==void 0?t:0,a=a!==void 0?a:24,n=1/this.path.unitsPerEm*a,e.lineWidth=1,e.strokeStyle=\"black\",ye.line(e,r,-1e4,r,1e4),ye.line(e,-1e4,t,1e4,t);var s=this.xMin||0,i=this.yMin||0,u=this.xMax||0,o=this.yMax||0,l=this.advanceWidth||0;e.strokeStyle=\"blue\",ye.line(e,r+s*n,-1e4,r+s*n,1e4),ye.line(e,r+u*n,-1e4,r+u*n,1e4),ye.line(e,-1e4,t+-i*n,1e4,t+-i*n),ye.line(e,-1e4,t+-o*n,1e4,t+-o*n),e.strokeStyle=\"green\",ye.line(e,r+l*n,-1e4,r+l*n,1e4)};function _e(e,r,t){Object.defineProperty(e,r,{get:function(){return e.path,e[t]},set:function(a){e[t]=a},enumerable:!0,configurable:!0})}function Cr(e,r){if(this.font=e,this.glyphs={},Array.isArray(r))for(var t=0;t<r.length;t++){var a=r[t];a.path.unitsPerEm=e.unitsPerEm,this.glyphs[t]=a}this.length=r&&r.length||0}Cr.prototype.get=function(e){if(this.glyphs[e]===void 0){this.font._push(e),typeof this.glyphs[e]==\"function\"&&(this.glyphs[e]=this.glyphs[e]());var r=this.glyphs[e],t=this.font._IndexToUnicodeMap[e];if(t)for(var a=0;a<t.unicodes.length;a++)r.addUnicode(t.unicodes[a]);this.font.cffEncoding?this.font.isCIDFont?r.name=\"gid\"+e:r.name=this.font.cffEncoding.charset[e]:this.font.glyphNames.names&&(r.name=this.font.glyphNames.glyphIndexToName(e)),this.glyphs[e].advanceWidth=this.font._hmtxTableData[e].advanceWidth,this.glyphs[e].leftSideBearing=this.font._hmtxTableData[e].leftSideBearing}else typeof this.glyphs[e]==\"function\"&&(this.glyphs[e]=this.glyphs[e]());return this.glyphs[e]};Cr.prototype.push=function(e,r){this.glyphs[e]=r,this.length++};function Xa(e,r){return new Q({index:r,font:e})}function Ya(e,r,t,a,n,s){return function(){var i=new Q({index:r,font:e});return i.path=function(){t(i,a,n);var u=s(e.glyphs,i);return u.unitsPerEm=e.unitsPerEm,u},_e(i,\"xMin\",\"_xMin\"),_e(i,\"xMax\",\"_xMax\"),_e(i,\"yMin\",\"_yMin\"),_e(i,\"yMax\",\"_yMax\"),i}}function Za(e,r,t,a){return function(){var n=new Q({index:r,font:e});return n.path=function(){var s=t(e,n,a);return s.unitsPerEm=e.unitsPerEm,s},n}}var ue={GlyphSet:Cr,glyphLoader:Xa,ttfGlyphLoader:Ya,cffGlyphLoader:Za};function Rt(e,r){if(e===r)return!0;if(Array.isArray(e)&&Array.isArray(r)){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t+=1)if(!Rt(e[t],r[t]))return!1;return!0}else return!1}function gr(e){var r;return e.length<1240?r=107:e.length<33900?r=1131:r=32768,r}function ve(e,r,t){var a=[],n=[],s=k.getCard16(e,r),i,u;if(s!==0){var o=k.getByte(e,r+2);i=r+(s+1)*o+2;for(var l=r+3,f=0;f<s+1;f+=1)a.push(k.getOffset(e,l,o)),l+=o;u=i+a[s]}else u=r+2;for(var h=0;h<a.length-1;h+=1){var p=k.getBytes(e,i+a[h],i+a[h+1]);t&&(p=t(p)),n.push(p)}return{objects:n,startOffset:r,endOffset:u}}function Qa(e,r){var t=[],a=k.getCard16(e,r),n,s;if(a!==0){var i=k.getByte(e,r+2);n=r+(a+1)*i+2;for(var u=r+3,o=0;o<a+1;o+=1)t.push(k.getOffset(e,u,i)),u+=i;s=n+t[a]}else s=r+2;return{offsets:t,startOffset:r,endOffset:s}}function Ka(e,r,t,a,n){var s=k.getCard16(t,a),i=0;if(s!==0){var u=k.getByte(t,a+2);i=a+(s+1)*u+2}var o=k.getBytes(t,i+r[e],i+r[e+1]);return n&&(o=n(o)),o}function Ja(e){for(var r=\"\",t=15,a=[\"0\",\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\".\",\"E\",\"E-\",null,\"-\"];;){var n=e.parseByte(),s=n>>4,i=n&15;if(s===t||(r+=a[s],i===t))break;r+=a[i]}return parseFloat(r)}function ja(e,r){var t,a,n,s;if(r===28)return t=e.parseByte(),a=e.parseByte(),t<<8|a;if(r===29)return t=e.parseByte(),a=e.parseByte(),n=e.parseByte(),s=e.parseByte(),t<<24|a<<16|n<<8|s;if(r===30)return Ja(e);if(r>=32&&r<=246)return r-139;if(r>=247&&r<=250)return t=e.parseByte(),(r-247)*256+t+108;if(r>=251&&r<=254)return t=e.parseByte(),-(r-251)*256-t-108;throw new Error(\"Invalid b0 \"+r)}function $a(e){for(var r={},t=0;t<e.length;t+=1){var a=e[t][0],n=e[t][1],s=void 0;if(n.length===1?s=n[0]:s=n,r.hasOwnProperty(a)&&!isNaN(r[a]))throw new Error(\"Object \"+r+\" already has key \"+a);r[a]=s}return r}function wt(e,r,t){r=r!==void 0?r:0;var a=new k.Parser(e,r),n=[],s=[];for(t=t!==void 0?t:e.length;a.relativeOffset<t;){var i=a.parseByte();i<=21?(i===12&&(i=1200+a.parseByte()),n.push([i,s]),s=[]):s.push(ja(a,i))}return $a(n)}function Oe(e,r){return r<=390?r=qe[r]:r=e[r-391],r}function Dt(e,r,t){for(var a={},n,s=0;s<r.length;s+=1){var i=r[s];if(Array.isArray(i.type)){var u=[];u.length=i.type.length;for(var o=0;o<i.type.length;o++)n=e[i.op]!==void 0?e[i.op][o]:void 0,n===void 0&&(n=i.value!==void 0&&i.value[o]!==void 0?i.value[o]:null),i.type[o]===\"SID\"&&(n=Oe(t,n)),u[o]=n;a[i.name]=u}else n=e[i.op],n===void 0&&(n=i.value!==void 0?i.value:null),i.type===\"SID\"&&(n=Oe(t,n)),a[i.name]=n}return a}function en(e,r){var t={};return t.formatMajor=k.getCard8(e,r),t.formatMinor=k.getCard8(e,r+1),t.size=k.getCard8(e,r+2),t.offsetSize=k.getCard8(e,r+3),t.startOffset=r,t.endOffset=r+4,t}var At=[{name:\"version\",op:0,type:\"SID\"},{name:\"notice\",op:1,type:\"SID\"},{name:\"copyright\",op:1200,type:\"SID\"},{name:\"fullName\",op:2,type:\"SID\"},{name:\"familyName\",op:3,type:\"SID\"},{name:\"weight\",op:4,type:\"SID\"},{name:\"isFixedPitch\",op:1201,type:\"number\",value:0},{name:\"italicAngle\",op:1202,type:\"number\",value:0},{name:\"underlinePosition\",op:1203,type:\"number\",value:-100},{name:\"underlineThickness\",op:1204,type:\"number\",value:50},{name:\"paintType\",op:1205,type:\"number\",value:0},{name:\"charstringType\",op:1206,type:\"number\",value:2},{name:\"fontMatrix\",op:1207,type:[\"real\",\"real\",\"real\",\"real\",\"real\",\"real\"],value:[.001,0,0,.001,0,0]},{name:\"uniqueId\",op:13,type:\"number\"},{name:\"fontBBox\",op:5,type:[\"number\",\"number\",\"number\",\"number\"],value:[0,0,0,0]},{name:\"strokeWidth\",op:1208,type:\"number\",value:0},{name:\"xuid\",op:14,type:[],value:null},{name:\"charset\",op:15,type:\"offset\",value:0},{name:\"encoding\",op:16,type:\"offset\",value:0},{name:\"charStrings\",op:17,type:\"offset\",value:0},{name:\"private\",op:18,type:[\"number\",\"offset\"],value:[0,0]},{name:\"ros\",op:1230,type:[\"SID\",\"SID\",\"number\"]},{name:\"cidFontVersion\",op:1231,type:\"number\",value:0},{name:\"cidFontRevision\",op:1232,type:\"number\",value:0},{name:\"cidFontType\",op:1233,type:\"number\",value:0},{name:\"cidCount\",op:1234,type:\"number\",value:8720},{name:\"uidBase\",op:1235,type:\"number\"},{name:\"fdArray\",op:1236,type:\"offset\"},{name:\"fdSelect\",op:1237,type:\"offset\"},{name:\"fontName\",op:1238,type:\"SID\"}],Bt=[{name:\"subrs\",op:19,type:\"offset\",value:0},{name:\"defaultWidthX\",op:20,type:\"number\",value:0},{name:\"nominalWidthX\",op:21,type:\"number\",value:0}];function rn(e,r){var t=wt(e,0,e.byteLength);return Dt(t,At,r)}function It(e,r,t,a){var n=wt(e,r,t);return Dt(n,Bt,a)}function Wr(e,r,t,a){for(var n=[],s=0;s<t.length;s+=1){var i=new DataView(new Uint8Array(t[s]).buffer),u=rn(i,a);u._subrs=[],u._subrsBias=0,u._defaultWidthX=0,u._nominalWidthX=0;var o=u.private[0],l=u.private[1];if(o!==0&&l!==0){var f=It(e,l+r,o,a);if(u._defaultWidthX=f.defaultWidthX,u._nominalWidthX=f.nominalWidthX,f.subrs!==0){var h=l+f.subrs,p=ve(e,h+r);u._subrs=p.objects,u._subrsBias=gr(u._subrs)}u._privateDict=f}n.push(u)}return n}function tn(e,r,t,a){var n,s,i=new k.Parser(e,r);t-=1;var u=[\".notdef\"],o=i.parseCard8();if(o===0)for(var l=0;l<t;l+=1)n=i.parseSID(),u.push(Oe(a,n));else if(o===1)for(;u.length<=t;){n=i.parseSID(),s=i.parseCard8();for(var f=0;f<=s;f+=1)u.push(Oe(a,n)),n+=1}else if(o===2)for(;u.length<=t;){n=i.parseSID(),s=i.parseCard16();for(var h=0;h<=s;h+=1)u.push(Oe(a,n)),n+=1}else throw new Error(\"Unknown charset format \"+o);return u}function an(e,r,t){var a,n={},s=new k.Parser(e,r),i=s.parseCard8();if(i===0)for(var u=s.parseCard8(),o=0;o<u;o+=1)a=s.parseCard8(),n[a]=o;else if(i===1){var l=s.parseCard8();a=1;for(var f=0;f<l;f+=1)for(var h=s.parseCard8(),p=s.parseCard8(),c=h;c<=h+p;c+=1)n[c]=a,a+=1}else throw new Error(\"Unknown encoding format \"+i);return new je(n,t)}function _r(e,r,t){var a,n,s,i,u=new P,o=[],l=0,f=!1,h=!1,p=0,c=0,d,x,m,y;if(e.isCIDFont){var C=e.tables.cff.topDict._fdSelect[r.index],S=e.tables.cff.topDict._fdArray[C];d=S._subrs,x=S._subrsBias,m=S._defaultWidthX,y=S._nominalWidthX}else d=e.tables.cff.topDict._subrs,x=e.tables.cff.topDict._subrsBias,m=e.tables.cff.topDict._defaultWidthX,y=e.tables.cff.topDict._nominalWidthX;var R=m;function O(F,G){h&&u.closePath(),u.moveTo(F,G),h=!0}function D(){var F;F=o.length%2!==0,F&&!f&&(R=o.shift()+y),l+=o.length>>1,o.length=0,f=!0}function L(F){for(var G,Y,Z,j,$,M,N,W,_,V,H,X,A=0;A<F.length;){var q=F[A];switch(A+=1,q){case 1:D();break;case 3:D();break;case 4:o.length>1&&!f&&(R=o.shift()+y,f=!0),c+=o.pop(),O(p,c);break;case 5:for(;o.length>0;)p+=o.shift(),c+=o.shift(),u.lineTo(p,c);break;case 6:for(;o.length>0&&(p+=o.shift(),u.lineTo(p,c),o.length!==0);)c+=o.shift(),u.lineTo(p,c);break;case 7:for(;o.length>0&&(c+=o.shift(),u.lineTo(p,c),o.length!==0);)p+=o.shift(),u.lineTo(p,c);break;case 8:for(;o.length>0;)a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+o.shift(),u.curveTo(a,n,s,i,p,c);break;case 10:$=o.pop()+x,M=d[$],M&&L(M);break;case 11:return;case 12:switch(q=F[A],A+=1,q){case 35:a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),N=s+o.shift(),W=i+o.shift(),_=N+o.shift(),V=W+o.shift(),H=_+o.shift(),X=V+o.shift(),p=H+o.shift(),c=X+o.shift(),o.shift(),u.curveTo(a,n,s,i,N,W),u.curveTo(_,V,H,X,p,c);break;case 34:a=p+o.shift(),n=c,s=a+o.shift(),i=n+o.shift(),N=s+o.shift(),W=i,_=N+o.shift(),V=i,H=_+o.shift(),X=c,p=H+o.shift(),u.curveTo(a,n,s,i,N,W),u.curveTo(_,V,H,X,p,c);break;case 36:a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),N=s+o.shift(),W=i,_=N+o.shift(),V=i,H=_+o.shift(),X=V+o.shift(),p=H+o.shift(),u.curveTo(a,n,s,i,N,W),u.curveTo(_,V,H,X,p,c);break;case 37:a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),N=s+o.shift(),W=i+o.shift(),_=N+o.shift(),V=W+o.shift(),H=_+o.shift(),X=V+o.shift(),Math.abs(H-p)>Math.abs(X-c)?p=H+o.shift():c=X+o.shift(),u.curveTo(a,n,s,i,N,W),u.curveTo(_,V,H,X,p,c);break;default:console.log(\"Glyph \"+r.index+\": unknown operator \"+1200+q),o.length=0}break;case 14:o.length>0&&!f&&(R=o.shift()+y,f=!0),h&&(u.closePath(),h=!1);break;case 18:D();break;case 19:case 20:D(),A+=l+7>>3;break;case 21:o.length>2&&!f&&(R=o.shift()+y,f=!0),c+=o.pop(),p+=o.pop(),O(p,c);break;case 22:o.length>1&&!f&&(R=o.shift()+y,f=!0),p+=o.pop(),O(p,c);break;case 23:D();break;case 24:for(;o.length>2;)a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+o.shift(),u.curveTo(a,n,s,i,p,c);p+=o.shift(),c+=o.shift(),u.lineTo(p,c);break;case 25:for(;o.length>6;)p+=o.shift(),c+=o.shift(),u.lineTo(p,c);a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+o.shift(),u.curveTo(a,n,s,i,p,c);break;case 26:for(o.length%2&&(p+=o.shift());o.length>0;)a=p,n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s,c=i+o.shift(),u.curveTo(a,n,s,i,p,c);break;case 27:for(o.length%2&&(c+=o.shift());o.length>0;)a=p+o.shift(),n=c,s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i,u.curveTo(a,n,s,i,p,c);break;case 28:G=F[A],Y=F[A+1],o.push((G<<24|Y<<16)>>16),A+=2;break;case 29:$=o.pop()+e.gsubrsBias,M=e.gsubrs[$],M&&L(M);break;case 30:for(;o.length>0&&(a=p,n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+(o.length===1?o.shift():0),u.curveTo(a,n,s,i,p,c),o.length!==0);)a=p+o.shift(),n=c,s=a+o.shift(),i=n+o.shift(),c=i+o.shift(),p=s+(o.length===1?o.shift():0),u.curveTo(a,n,s,i,p,c);break;case 31:for(;o.length>0&&(a=p+o.shift(),n=c,s=a+o.shift(),i=n+o.shift(),c=i+o.shift(),p=s+(o.length===1?o.shift():0),u.curveTo(a,n,s,i,p,c),o.length!==0);)a=p,n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+(o.length===1?o.shift():0),u.curveTo(a,n,s,i,p,c);break;default:q<32?console.log(\"Glyph \"+r.index+\": unknown operator \"+q):q<247?o.push(q-139):q<251?(G=F[A],A+=1,o.push((q-247)*256+G+108)):q<255?(G=F[A],A+=1,o.push(-(q-251)*256-G-108)):(G=F[A],Y=F[A+1],Z=F[A+2],j=F[A+3],A+=4,o.push((G<<24|Y<<16|Z<<8|j)/65536))}}}return L(t),r.advanceWidth=R,u}function nn(e,r,t,a){var n=[],s,i=new k.Parser(e,r),u=i.parseCard8();if(u===0)for(var o=0;o<t;o++){if(s=i.parseCard8(),s>=a)throw new Error(\"CFF table CID Font FDSelect has bad FD index value \"+s+\" (FD count \"+a+\")\");n.push(s)}else if(u===3){var l=i.parseCard16(),f=i.parseCard16();if(f!==0)throw new Error(\"CFF Table CID Font FDSelect format 3 range has bad initial GID \"+f);for(var h,p=0;p<l;p++){if(s=i.parseCard8(),h=i.parseCard16(),s>=a)throw new Error(\"CFF table CID Font FDSelect has bad FD index value \"+s+\" (FD count \"+a+\")\");if(h>t)throw new Error(\"CFF Table CID Font FDSelect format 3 range has bad GID \"+h);for(;f<h;f++)n.push(s);f=h}if(h!==t)throw new Error(\"CFF Table CID Font FDSelect format 3 range has bad final GID \"+h)}else throw new Error(\"CFF Table CID Font FDSelect table has unsupported format \"+u);return n}function sn(e,r,t,a){t.tables.cff={};var n=en(e,r),s=ve(e,n.endOffset,k.bytesToString),i=ve(e,s.endOffset),u=ve(e,i.endOffset,k.bytesToString),o=ve(e,u.endOffset);t.gsubrs=o.objects,t.gsubrsBias=gr(t.gsubrs);var l=Wr(e,r,i.objects,u.objects);if(l.length!==1)throw new Error(\"CFF table has too many fonts in 'FontSet' - count of fonts NameIndex.length = \"+l.length);var f=l[0];if(t.tables.cff.topDict=f,f._privateDict&&(t.defaultWidthX=f._privateDict.defaultWidthX,t.nominalWidthX=f._privateDict.nominalWidthX),f.ros[0]!==void 0&&f.ros[1]!==void 0&&(t.isCIDFont=!0),t.isCIDFont){var h=f.fdArray,p=f.fdSelect;if(h===0||p===0)throw new Error(\"Font is marked as a CID font, but FDArray and/or FDSelect information is missing\");h+=r;var c=ve(e,h),d=Wr(e,r,c.objects,u.objects);f._fdArray=d,p+=r,f._fdSelect=nn(e,p,t.numGlyphs,d.length)}var x=r+f.private[1],m=It(e,x,f.private[0],u.objects);if(t.defaultWidthX=m.defaultWidthX,t.nominalWidthX=m.nominalWidthX,m.subrs!==0){var y=x+m.subrs,C=ve(e,y);t.subrs=C.objects,t.subrsBias=gr(t.subrs)}else t.subrs=[],t.subrsBias=0;var S;a.lowMemory?(S=Qa(e,r+f.charStrings),t.nGlyphs=S.offsets.length):(S=ve(e,r+f.charStrings),t.nGlyphs=S.objects.length);var R=tn(e,r+f.charset,t.nGlyphs,u.objects);if(f.encoding===0?t.cffEncoding=new je(Na,R):f.encoding===1?t.cffEncoding=new je(Ha,R):t.cffEncoding=an(e,r+f.encoding,R),t.encoding=t.encoding||t.cffEncoding,t.glyphs=new ue.GlyphSet(t),a.lowMemory)t._push=function(L){var F=Ka(L,S.offsets,e,r+f.charStrings);t.glyphs.push(L,ue.cffGlyphLoader(t,L,_r,F))};else for(var O=0;O<t.nGlyphs;O+=1){var D=S.objects[O];t.glyphs.push(O,ue.cffGlyphLoader(t,O,_r,D))}}function Mt(e,r){var t,a=qe.indexOf(e);return a>=0&&(t=a),a=r.indexOf(e),a>=0?t=a+qe.length:(t=qe.length+r.length,r.push(e)),t}function on(){return new b.Record(\"Header\",[{name:\"major\",type:\"Card8\",value:1},{name:\"minor\",type:\"Card8\",value:0},{name:\"hdrSize\",type:\"Card8\",value:4},{name:\"major\",type:\"Card8\",value:1}])}function un(e){var r=new b.Record(\"Name INDEX\",[{name:\"names\",type:\"INDEX\",value:[]}]);r.names=[];for(var t=0;t<e.length;t+=1)r.names.push({name:\"name_\"+t,type:\"NAME\",value:e[t]});return r}function Pt(e,r,t){for(var a={},n=0;n<e.length;n+=1){var s=e[n],i=r[s.name];i!==void 0&&!Rt(i,s.value)&&(s.type===\"SID\"&&(i=Mt(i,t)),a[s.op]={name:s.name,type:s.type,value:i})}return a}function Vr(e,r){var t=new b.Record(\"Top DICT\",[{name:\"dict\",type:\"DICT\",value:{}}]);return t.dict=Pt(At,e,r),t}function qr(e){var r=new b.Record(\"Top DICT INDEX\",[{name:\"topDicts\",type:\"INDEX\",value:[]}]);return r.topDicts=[{name:\"topDict_0\",type:\"TABLE\",value:e}],r}function ln(e){var r=new b.Record(\"String INDEX\",[{name:\"strings\",type:\"INDEX\",value:[]}]);r.strings=[];for(var t=0;t<e.length;t+=1)r.strings.push({name:\"string_\"+t,type:\"STRING\",value:e[t]});return r}function fn(){return new b.Record(\"Global Subr INDEX\",[{name:\"subrs\",type:\"INDEX\",value:[]}])}function pn(e,r){for(var t=new b.Record(\"Charsets\",[{name:\"format\",type:\"Card8\",value:0}]),a=0;a<e.length;a+=1){var n=e[a],s=Mt(n,r);t.fields.push({name:\"glyph_\"+a,type:\"SID\",value:s})}return t}function hn(e){var r=[],t=e.path;r.push({name:\"width\",type:\"NUMBER\",value:e.advanceWidth});for(var a=0,n=0,s=0;s<t.commands.length;s+=1){var i=void 0,u=void 0,o=t.commands[s];if(o.type===\"Q\"){var l=.3333333333333333,f=2/3;o={type:\"C\",x:o.x,y:o.y,x1:Math.round(l*a+f*o.x1),y1:Math.round(l*n+f*o.y1),x2:Math.round(l*o.x+f*o.x1),y2:Math.round(l*o.y+f*o.y1)}}if(o.type===\"M\")i=Math.round(o.x-a),u=Math.round(o.y-n),r.push({name:\"dx\",type:\"NUMBER\",value:i}),r.push({name:\"dy\",type:\"NUMBER\",value:u}),r.push({name:\"rmoveto\",type:\"OP\",value:21}),a=Math.round(o.x),n=Math.round(o.y);else if(o.type===\"L\")i=Math.round(o.x-a),u=Math.round(o.y-n),r.push({name:\"dx\",type:\"NUMBER\",value:i}),r.push({name:\"dy\",type:\"NUMBER\",value:u}),r.push({name:\"rlineto\",type:\"OP\",value:5}),a=Math.round(o.x),n=Math.round(o.y);else if(o.type===\"C\"){var h=Math.round(o.x1-a),p=Math.round(o.y1-n),c=Math.round(o.x2-o.x1),d=Math.round(o.y2-o.y1);i=Math.round(o.x-o.x2),u=Math.round(o.y-o.y2),r.push({name:\"dx1\",type:\"NUMBER\",value:h}),r.push({name:\"dy1\",type:\"NUMBER\",value:p}),r.push({name:\"dx2\",type:\"NUMBER\",value:c}),r.push({name:\"dy2\",type:\"NUMBER\",value:d}),r.push({name:\"dx\",type:\"NUMBER\",value:i}),r.push({name:\"dy\",type:\"NUMBER\",value:u}),r.push({name:\"rrcurveto\",type:\"OP\",value:8}),a=Math.round(o.x),n=Math.round(o.y)}}return r.push({name:\"endchar\",type:\"OP\",value:14}),r}function cn(e){for(var r=new b.Record(\"CharStrings INDEX\",[{name:\"charStrings\",type:\"INDEX\",value:[]}]),t=0;t<e.length;t+=1){var a=e.get(t),n=hn(a);r.charStrings.push({name:a.name,type:\"CHARSTRING\",value:n})}return r}function vn(e,r){var t=new b.Record(\"Private DICT\",[{name:\"dict\",type:\"DICT\",value:{}}]);return t.dict=Pt(Bt,e,r),t}function dn(e,r){for(var t=new b.Table(\"CFF \",[{name:\"header\",type:\"RECORD\"},{name:\"nameIndex\",type:\"RECORD\"},{name:\"topDictIndex\",type:\"RECORD\"},{name:\"stringIndex\",type:\"RECORD\"},{name:\"globalSubrIndex\",type:\"RECORD\"},{name:\"charsets\",type:\"RECORD\"},{name:\"charStringsIndex\",type:\"RECORD\"},{name:\"privateDict\",type:\"RECORD\"}]),a=1/r.unitsPerEm,n={version:r.version,fullName:r.fullName,familyName:r.familyName,weight:r.weightName,fontBBox:r.fontBBox||[0,0,0,0],fontMatrix:[a,0,0,a,0,0],charset:999,encoding:0,charStrings:999,private:[0,999]},s={},i=[],u,o=1;o<e.length;o+=1)u=e.get(o),i.push(u.name);var l=[];t.header=on(),t.nameIndex=un([r.postScriptName]);var f=Vr(n,l);t.topDictIndex=qr(f),t.globalSubrIndex=fn(),t.charsets=pn(i,l),t.charStringsIndex=cn(e),t.privateDict=vn(s,l),t.stringIndex=ln(l);var h=t.header.sizeOf()+t.nameIndex.sizeOf()+t.topDictIndex.sizeOf()+t.stringIndex.sizeOf()+t.globalSubrIndex.sizeOf();return n.charset=h,n.encoding=0,n.charStrings=n.charset+t.charsets.sizeOf(),n.private[1]=n.charStrings+t.charStringsIndex.sizeOf(),f=Vr(n,l),t.topDictIndex=qr(f),t}var Gt={parse:sn,make:dn};function gn(e,r){var t={},a=new k.Parser(e,r);return t.version=a.parseVersion(),t.fontRevision=Math.round(a.parseFixed()*1e3)/1e3,t.checkSumAdjustment=a.parseULong(),t.magicNumber=a.parseULong(),U.argument(t.magicNumber===1594834165,\"Font header has wrong magic number.\"),t.flags=a.parseUShort(),t.unitsPerEm=a.parseUShort(),t.created=a.parseLongDateTime(),t.modified=a.parseLongDateTime(),t.xMin=a.parseShort(),t.yMin=a.parseShort(),t.xMax=a.parseShort(),t.yMax=a.parseShort(),t.macStyle=a.parseUShort(),t.lowestRecPPEM=a.parseUShort(),t.fontDirectionHint=a.parseShort(),t.indexToLocFormat=a.parseShort(),t.glyphDataFormat=a.parseShort(),t}function mn(e){var r=Math.round(new Date().getTime()/1e3)+2082844800,t=r;return e.createdTimestamp&&(t=e.createdTimestamp+2082844800),new b.Table(\"head\",[{name:\"version\",type:\"FIXED\",value:65536},{name:\"fontRevision\",type:\"FIXED\",value:65536},{name:\"checkSumAdjustment\",type:\"ULONG\",value:0},{name:\"magicNumber\",type:\"ULONG\",value:1594834165},{name:\"flags\",type:\"USHORT\",value:0},{name:\"unitsPerEm\",type:\"USHORT\",value:1e3},{name:\"created\",type:\"LONGDATETIME\",value:t},{name:\"modified\",type:\"LONGDATETIME\",value:r},{name:\"xMin\",type:\"SHORT\",value:0},{name:\"yMin\",type:\"SHORT\",value:0},{name:\"xMax\",type:\"SHORT\",value:0},{name:\"yMax\",type:\"SHORT\",value:0},{name:\"macStyle\",type:\"USHORT\",value:0},{name:\"lowestRecPPEM\",type:\"USHORT\",value:0},{name:\"fontDirectionHint\",type:\"SHORT\",value:2},{name:\"indexToLocFormat\",type:\"SHORT\",value:0},{name:\"glyphDataFormat\",type:\"SHORT\",value:0}],e)}var Nt={parse:gn,make:mn};function yn(e,r){var t={},a=new k.Parser(e,r);return t.version=a.parseVersion(),t.ascender=a.parseShort(),t.descender=a.parseShort(),t.lineGap=a.parseShort(),t.advanceWidthMax=a.parseUShort(),t.minLeftSideBearing=a.parseShort(),t.minRightSideBearing=a.parseShort(),t.xMaxExtent=a.parseShort(),t.caretSlopeRise=a.parseShort(),t.caretSlopeRun=a.parseShort(),t.caretOffset=a.parseShort(),a.relativeOffset+=8,t.metricDataFormat=a.parseShort(),t.numberOfHMetrics=a.parseUShort(),t}function xn(e){return new b.Table(\"hhea\",[{name:\"version\",type:\"FIXED\",value:65536},{name:\"ascender\",type:\"FWORD\",value:0},{name:\"descender\",type:\"FWORD\",value:0},{name:\"lineGap\",type:\"FWORD\",value:0},{name:\"advanceWidthMax\",type:\"UFWORD\",value:0},{name:\"minLeftSideBearing\",type:\"FWORD\",value:0},{name:\"minRightSideBearing\",type:\"FWORD\",value:0},{name:\"xMaxExtent\",type:\"FWORD\",value:0},{name:\"caretSlopeRise\",type:\"SHORT\",value:1},{name:\"caretSlopeRun\",type:\"SHORT\",value:0},{name:\"caretOffset\",type:\"SHORT\",value:0},{name:\"reserved1\",type:\"SHORT\",value:0},{name:\"reserved2\",type:\"SHORT\",value:0},{name:\"reserved3\",type:\"SHORT\",value:0},{name:\"reserved4\",type:\"SHORT\",value:0},{name:\"metricDataFormat\",type:\"SHORT\",value:0},{name:\"numberOfHMetrics\",type:\"USHORT\",value:0}],e)}var Ht={parse:yn,make:xn};function bn(e,r,t,a,n){for(var s,i,u=new k.Parser(e,r),o=0;o<a;o+=1){o<t&&(s=u.parseUShort(),i=u.parseShort());var l=n.get(o);l.advanceWidth=s,l.leftSideBearing=i}}function Sn(e,r,t,a,n){e._hmtxTableData={};for(var s,i,u=new k.Parser(r,t),o=0;o<n;o+=1)o<a&&(s=u.parseUShort(),i=u.parseShort()),e._hmtxTableData[o]={advanceWidth:s,leftSideBearing:i}}function Tn(e,r,t,a,n,s,i){i.lowMemory?Sn(e,r,t,a,n):bn(r,t,a,n,s)}function kn(e){for(var r=new b.Table(\"hmtx\",[]),t=0;t<e.length;t+=1){var a=e.get(t),n=a.advanceWidth||0,s=a.leftSideBearing||0;r.fields.push({name:\"advanceWidth_\"+t,type:\"USHORT\",value:n}),r.fields.push({name:\"leftSideBearing_\"+t,type:\"SHORT\",value:s})}return r}var zt={parse:Tn,make:kn};function Fn(e){for(var r=new b.Table(\"ltag\",[{name:\"version\",type:\"ULONG\",value:1},{name:\"flags\",type:\"ULONG\",value:0},{name:\"numTags\",type:\"ULONG\",value:e.length}]),t=\"\",a=12+e.length*4,n=0;n<e.length;++n){var s=t.indexOf(e[n]);s<0&&(s=t.length,t+=e[n]),r.fields.push({name:\"offset \"+n,type:\"USHORT\",value:a+s}),r.fields.push({name:\"length \"+n,type:\"USHORT\",value:e[n].length})}return r.fields.push({name:\"stringPool\",type:\"CHARARRAY\",value:t}),r}function Un(e,r){var t=new k.Parser(e,r),a=t.parseULong();U.argument(a===1,\"Unsupported ltag table version.\"),t.skip(\"uLong\",1);for(var n=t.parseULong(),s=[],i=0;i<n;i++){for(var u=\"\",o=r+t.parseUShort(),l=t.parseUShort(),f=o;f<o+l;++f)u+=String.fromCharCode(e.getInt8(f));s.push(u)}return s}var Wt={make:Fn,parse:Un};function Cn(e,r){var t={},a=new k.Parser(e,r);return t.version=a.parseVersion(),t.numGlyphs=a.parseUShort(),t.version===1&&(t.maxPoints=a.parseUShort(),t.maxContours=a.parseUShort(),t.maxCompositePoints=a.parseUShort(),t.maxCompositeContours=a.parseUShort(),t.maxZones=a.parseUShort(),t.maxTwilightPoints=a.parseUShort(),t.maxStorage=a.parseUShort(),t.maxFunctionDefs=a.parseUShort(),t.maxInstructionDefs=a.parseUShort(),t.maxStackElements=a.parseUShort(),t.maxSizeOfInstructions=a.parseUShort(),t.maxComponentElements=a.parseUShort(),t.maxComponentDepth=a.parseUShort()),t}function En(e){return new b.Table(\"maxp\",[{name:\"version\",type:\"FIXED\",value:20480},{name:\"numGlyphs\",type:\"USHORT\",value:e}])}var _t={parse:Cn,make:En},Vt=[\"copyright\",\"fontFamily\",\"fontSubfamily\",\"uniqueID\",\"fullName\",\"version\",\"postScriptName\",\"trademark\",\"manufacturer\",\"designer\",\"description\",\"manufacturerURL\",\"designerURL\",\"license\",\"licenseURL\",\"reserved\",\"preferredFamily\",\"preferredSubfamily\",\"compatibleFullName\",\"sampleText\",\"postScriptFindFontName\",\"wwsFamily\",\"wwsSubfamily\"],qt={0:\"en\",1:\"fr\",2:\"de\",3:\"it\",4:\"nl\",5:\"sv\",6:\"es\",7:\"da\",8:\"pt\",9:\"no\",10:\"he\",11:\"ja\",12:\"ar\",13:\"fi\",14:\"el\",15:\"is\",16:\"mt\",17:\"tr\",18:\"hr\",19:\"zh-Hant\",20:\"ur\",21:\"hi\",22:\"th\",23:\"ko\",24:\"lt\",25:\"pl\",26:\"hu\",27:\"es\",28:\"lv\",29:\"se\",30:\"fo\",31:\"fa\",32:\"ru\",33:\"zh\",34:\"nl-BE\",35:\"ga\",36:\"sq\",37:\"ro\",38:\"cz\",39:\"sk\",40:\"si\",41:\"yi\",42:\"sr\",43:\"mk\",44:\"bg\",45:\"uk\",46:\"be\",47:\"uz\",48:\"kk\",49:\"az-Cyrl\",50:\"az-Arab\",51:\"hy\",52:\"ka\",53:\"mo\",54:\"ky\",55:\"tg\",56:\"tk\",57:\"mn-CN\",58:\"mn\",59:\"ps\",60:\"ks\",61:\"ku\",62:\"sd\",63:\"bo\",64:\"ne\",65:\"sa\",66:\"mr\",67:\"bn\",68:\"as\",69:\"gu\",70:\"pa\",71:\"or\",72:\"ml\",73:\"kn\",74:\"ta\",75:\"te\",76:\"si\",77:\"my\",78:\"km\",79:\"lo\",80:\"vi\",81:\"id\",82:\"tl\",83:\"ms\",84:\"ms-Arab\",85:\"am\",86:\"ti\",87:\"om\",88:\"so\",89:\"sw\",90:\"rw\",91:\"rn\",92:\"ny\",93:\"mg\",94:\"eo\",128:\"cy\",129:\"eu\",130:\"ca\",131:\"la\",132:\"qu\",133:\"gn\",134:\"ay\",135:\"tt\",136:\"ug\",137:\"dz\",138:\"jv\",139:\"su\",140:\"gl\",141:\"af\",142:\"br\",143:\"iu\",144:\"gd\",145:\"gv\",146:\"ga\",147:\"to\",148:\"el-polyton\",149:\"kl\",150:\"az\",151:\"nn\"},On={0:0,1:0,2:0,3:0,4:0,5:0,6:0,7:0,8:0,9:0,10:5,11:1,12:4,13:0,14:6,15:0,16:0,17:0,18:0,19:2,20:4,21:9,22:21,23:3,24:29,25:29,26:29,27:29,28:29,29:0,30:0,31:4,32:7,33:25,34:0,35:0,36:0,37:0,38:29,39:29,40:0,41:5,42:7,43:7,44:7,45:7,46:7,47:7,48:7,49:7,50:4,51:24,52:23,53:7,54:7,55:7,56:7,57:27,58:7,59:4,60:4,61:4,62:4,63:26,64:9,65:9,66:9,67:13,68:13,69:11,70:10,71:12,72:17,73:16,74:14,75:15,76:18,77:19,78:20,79:22,80:30,81:0,82:0,83:0,84:4,85:28,86:28,87:28,88:0,89:0,90:0,91:0,92:0,93:0,94:0,128:0,129:0,130:0,131:0,132:0,133:0,134:0,135:7,136:4,137:26,138:0,139:0,140:0,141:0,142:0,143:28,144:0,145:0,146:0,147:0,148:6,149:0,150:0,151:0},Xt={1078:\"af\",1052:\"sq\",1156:\"gsw\",1118:\"am\",5121:\"ar-DZ\",15361:\"ar-BH\",3073:\"ar\",2049:\"ar-IQ\",11265:\"ar-JO\",13313:\"ar-KW\",12289:\"ar-LB\",4097:\"ar-LY\",6145:\"ary\",8193:\"ar-OM\",16385:\"ar-QA\",1025:\"ar-SA\",10241:\"ar-SY\",7169:\"aeb\",14337:\"ar-AE\",9217:\"ar-YE\",1067:\"hy\",1101:\"as\",2092:\"az-Cyrl\",1068:\"az\",1133:\"ba\",1069:\"eu\",1059:\"be\",2117:\"bn\",1093:\"bn-IN\",8218:\"bs-Cyrl\",5146:\"bs\",1150:\"br\",1026:\"bg\",1027:\"ca\",3076:\"zh-HK\",5124:\"zh-MO\",2052:\"zh\",4100:\"zh-SG\",1028:\"zh-TW\",1155:\"co\",1050:\"hr\",4122:\"hr-BA\",1029:\"cs\",1030:\"da\",1164:\"prs\",1125:\"dv\",2067:\"nl-BE\",1043:\"nl\",3081:\"en-AU\",10249:\"en-BZ\",4105:\"en-CA\",9225:\"en-029\",16393:\"en-IN\",6153:\"en-IE\",8201:\"en-JM\",17417:\"en-MY\",5129:\"en-NZ\",13321:\"en-PH\",18441:\"en-SG\",7177:\"en-ZA\",11273:\"en-TT\",2057:\"en-GB\",1033:\"en\",12297:\"en-ZW\",1061:\"et\",1080:\"fo\",1124:\"fil\",1035:\"fi\",2060:\"fr-BE\",3084:\"fr-CA\",1036:\"fr\",5132:\"fr-LU\",6156:\"fr-MC\",4108:\"fr-CH\",1122:\"fy\",1110:\"gl\",1079:\"ka\",3079:\"de-AT\",1031:\"de\",5127:\"de-LI\",4103:\"de-LU\",2055:\"de-CH\",1032:\"el\",1135:\"kl\",1095:\"gu\",1128:\"ha\",1037:\"he\",1081:\"hi\",1038:\"hu\",1039:\"is\",1136:\"ig\",1057:\"id\",1117:\"iu\",2141:\"iu-Latn\",2108:\"ga\",1076:\"xh\",1077:\"zu\",1040:\"it\",2064:\"it-CH\",1041:\"ja\",1099:\"kn\",1087:\"kk\",1107:\"km\",1158:\"quc\",1159:\"rw\",1089:\"sw\",1111:\"kok\",1042:\"ko\",1088:\"ky\",1108:\"lo\",1062:\"lv\",1063:\"lt\",2094:\"dsb\",1134:\"lb\",1071:\"mk\",2110:\"ms-BN\",1086:\"ms\",1100:\"ml\",1082:\"mt\",1153:\"mi\",1146:\"arn\",1102:\"mr\",1148:\"moh\",1104:\"mn\",2128:\"mn-CN\",1121:\"ne\",1044:\"nb\",2068:\"nn\",1154:\"oc\",1096:\"or\",1123:\"ps\",1045:\"pl\",1046:\"pt\",2070:\"pt-PT\",1094:\"pa\",1131:\"qu-BO\",2155:\"qu-EC\",3179:\"qu\",1048:\"ro\",1047:\"rm\",1049:\"ru\",9275:\"smn\",4155:\"smj-NO\",5179:\"smj\",3131:\"se-FI\",1083:\"se\",2107:\"se-SE\",8251:\"sms\",6203:\"sma-NO\",7227:\"sms\",1103:\"sa\",7194:\"sr-Cyrl-BA\",3098:\"sr\",6170:\"sr-Latn-BA\",2074:\"sr-Latn\",1132:\"nso\",1074:\"tn\",1115:\"si\",1051:\"sk\",1060:\"sl\",11274:\"es-AR\",16394:\"es-BO\",13322:\"es-CL\",9226:\"es-CO\",5130:\"es-CR\",7178:\"es-DO\",12298:\"es-EC\",17418:\"es-SV\",4106:\"es-GT\",18442:\"es-HN\",2058:\"es-MX\",19466:\"es-NI\",6154:\"es-PA\",15370:\"es-PY\",10250:\"es-PE\",20490:\"es-PR\",3082:\"es\",1034:\"es\",21514:\"es-US\",14346:\"es-UY\",8202:\"es-VE\",2077:\"sv-FI\",1053:\"sv\",1114:\"syr\",1064:\"tg\",2143:\"tzm\",1097:\"ta\",1092:\"tt\",1098:\"te\",1054:\"th\",1105:\"bo\",1055:\"tr\",1090:\"tk\",1152:\"ug\",1058:\"uk\",1070:\"hsb\",1056:\"ur\",2115:\"uz-Cyrl\",1091:\"uz\",1066:\"vi\",1106:\"cy\",1160:\"wo\",1157:\"sah\",1144:\"ii\",1130:\"yo\"};function Ln(e,r,t){switch(e){case 0:if(r===65535)return\"und\";if(t)return t[r];break;case 1:return qt[r];case 3:return Xt[r]}}var mr=\"utf-16\",Rn={0:\"macintosh\",1:\"x-mac-japanese\",2:\"x-mac-chinesetrad\",3:\"x-mac-korean\",6:\"x-mac-greek\",7:\"x-mac-cyrillic\",9:\"x-mac-devanagai\",10:\"x-mac-gurmukhi\",11:\"x-mac-gujarati\",12:\"x-mac-oriya\",13:\"x-mac-bengali\",14:\"x-mac-tamil\",15:\"x-mac-telugu\",16:\"x-mac-kannada\",17:\"x-mac-malayalam\",18:\"x-mac-sinhalese\",19:\"x-mac-burmese\",20:\"x-mac-khmer\",21:\"x-mac-thai\",22:\"x-mac-lao\",23:\"x-mac-georgian\",24:\"x-mac-armenian\",25:\"x-mac-chinesesimp\",26:\"x-mac-tibetan\",27:\"x-mac-mongolian\",28:\"x-mac-ethiopic\",29:\"x-mac-ce\",30:\"x-mac-vietnamese\",31:\"x-mac-extarabic\"},wn={15:\"x-mac-icelandic\",17:\"x-mac-turkish\",18:\"x-mac-croatian\",24:\"x-mac-ce\",25:\"x-mac-ce\",26:\"x-mac-ce\",27:\"x-mac-ce\",28:\"x-mac-ce\",30:\"x-mac-icelandic\",37:\"x-mac-romanian\",38:\"x-mac-ce\",39:\"x-mac-ce\",40:\"x-mac-ce\",143:\"x-mac-inuit\",146:\"x-mac-gaelic\"};function Yt(e,r,t){switch(e){case 0:return mr;case 1:return wn[t]||Rn[r];case 3:if(r===1||r===10)return mr;break}}function Dn(e,r,t){for(var a={},n=new k.Parser(e,r),s=n.parseUShort(),i=n.parseUShort(),u=n.offset+n.parseUShort(),o=0;o<i;o++){var l=n.parseUShort(),f=n.parseUShort(),h=n.parseUShort(),p=n.parseUShort(),c=Vt[p]||p,d=n.parseUShort(),x=n.parseUShort(),m=Ln(l,h,t),y=Yt(l,f,h);if(y!==void 0&&m!==void 0){var C=void 0;if(y===mr?C=Fe.UTF16(e,u+x,d):C=Fe.MACSTRING(e,u+x,d,y),C){var S=a[c];S===void 0&&(S=a[c]={}),S[m]=C}}}var R=0;return s===1&&(R=n.parseUShort()),a}function nr(e){var r={};for(var t in e)r[e[t]]=parseInt(t);return r}function Xr(e,r,t,a,n,s){return new b.Record(\"NameRecord\",[{name:\"platformID\",type:\"USHORT\",value:e},{name:\"encodingID\",type:\"USHORT\",value:r},{name:\"languageID\",type:\"USHORT\",value:t},{name:\"nameID\",type:\"USHORT\",value:a},{name:\"length\",type:\"USHORT\",value:n},{name:\"offset\",type:\"USHORT\",value:s}])}function An(e,r){var t=e.length,a=r.length-t+1;e:for(var n=0;n<a;n++)for(;n<a;n++){for(var s=0;s<t;s++)if(r[n+s]!==e[s])continue e;return n}return-1}function Yr(e,r){var t=An(e,r);if(t<0){t=r.length;for(var a=0,n=e.length;a<n;++a)r.push(e[a])}return t}function Bn(e,r){var t,a=[],n={},s=nr(Vt);for(var i in e){var u=s[i];if(u===void 0&&(u=i),t=parseInt(u),isNaN(t))throw new Error('Name table entry \"'+i+'\" does not exist, see nameTableNames for complete list.');n[t]=e[i],a.push(t)}for(var o=nr(qt),l=nr(Xt),f=[],h=[],p=0;p<a.length;p++){t=a[p];var c=n[t];for(var d in c){var x=c[d],m=1,y=o[d],C=On[y],S=Yt(m,C,y),R=g.MACSTRING(x,S);R===void 0&&(m=0,y=r.indexOf(d),y<0&&(y=r.length,r.push(d)),C=4,R=g.UTF16(x));var O=Yr(R,h);f.push(Xr(m,C,y,t,R.length,O));var D=l[d];if(D!==void 0){var L=g.UTF16(x),F=Yr(L,h);f.push(Xr(3,1,D,t,L.length,F))}}}f.sort(function(Z,j){return Z.platformID-j.platformID||Z.encodingID-j.encodingID||Z.languageID-j.languageID||Z.nameID-j.nameID});for(var G=new b.Table(\"name\",[{name:\"format\",type:\"USHORT\",value:0},{name:\"count\",type:\"USHORT\",value:f.length},{name:\"stringOffset\",type:\"USHORT\",value:6+f.length*12}]),Y=0;Y<f.length;Y++)G.fields.push({name:\"record_\"+Y,type:\"RECORD\",value:f[Y]});return G.fields.push({name:\"strings\",type:\"LITERAL\",value:h}),G}var Zt={parse:Dn,make:Bn},yr=[{begin:0,end:127},{begin:128,end:255},{begin:256,end:383},{begin:384,end:591},{begin:592,end:687},{begin:688,end:767},{begin:768,end:879},{begin:880,end:1023},{begin:11392,end:11519},{begin:1024,end:1279},{begin:1328,end:1423},{begin:1424,end:1535},{begin:42240,end:42559},{begin:1536,end:1791},{begin:1984,end:2047},{begin:2304,end:2431},{begin:2432,end:2559},{begin:2560,end:2687},{begin:2688,end:2815},{begin:2816,end:2943},{begin:2944,end:3071},{begin:3072,end:3199},{begin:3200,end:3327},{begin:3328,end:3455},{begin:3584,end:3711},{begin:3712,end:3839},{begin:4256,end:4351},{begin:6912,end:7039},{begin:4352,end:4607},{begin:7680,end:7935},{begin:7936,end:8191},{begin:8192,end:8303},{begin:8304,end:8351},{begin:8352,end:8399},{begin:8400,end:8447},{begin:8448,end:8527},{begin:8528,end:8591},{begin:8592,end:8703},{begin:8704,end:8959},{begin:8960,end:9215},{begin:9216,end:9279},{begin:9280,end:9311},{begin:9312,end:9471},{begin:9472,end:9599},{begin:9600,end:9631},{begin:9632,end:9727},{begin:9728,end:9983},{begin:9984,end:10175},{begin:12288,end:12351},{begin:12352,end:12447},{begin:12448,end:12543},{begin:12544,end:12591},{begin:12592,end:12687},{begin:43072,end:43135},{begin:12800,end:13055},{begin:13056,end:13311},{begin:44032,end:55215},{begin:55296,end:57343},{begin:67840,end:67871},{begin:19968,end:40959},{begin:57344,end:63743},{begin:12736,end:12783},{begin:64256,end:64335},{begin:64336,end:65023},{begin:65056,end:65071},{begin:65040,end:65055},{begin:65104,end:65135},{begin:65136,end:65279},{begin:65280,end:65519},{begin:65520,end:65535},{begin:3840,end:4095},{begin:1792,end:1871},{begin:1920,end:1983},{begin:3456,end:3583},{begin:4096,end:4255},{begin:4608,end:4991},{begin:5024,end:5119},{begin:5120,end:5759},{begin:5760,end:5791},{begin:5792,end:5887},{begin:6016,end:6143},{begin:6144,end:6319},{begin:10240,end:10495},{begin:40960,end:42127},{begin:5888,end:5919},{begin:66304,end:66351},{begin:66352,end:66383},{begin:66560,end:66639},{begin:118784,end:119039},{begin:119808,end:120831},{begin:1044480,end:1048573},{begin:65024,end:65039},{begin:917504,end:917631},{begin:6400,end:6479},{begin:6480,end:6527},{begin:6528,end:6623},{begin:6656,end:6687},{begin:11264,end:11359},{begin:11568,end:11647},{begin:19904,end:19967},{begin:43008,end:43055},{begin:65536,end:65663},{begin:65856,end:65935},{begin:66432,end:66463},{begin:66464,end:66527},{begin:66640,end:66687},{begin:66688,end:66735},{begin:67584,end:67647},{begin:68096,end:68191},{begin:119552,end:119647},{begin:73728,end:74751},{begin:119648,end:119679},{begin:7040,end:7103},{begin:7168,end:7247},{begin:7248,end:7295},{begin:43136,end:43231},{begin:43264,end:43311},{begin:43312,end:43359},{begin:43520,end:43615},{begin:65936,end:65999},{begin:66e3,end:66047},{begin:66208,end:66271},{begin:127024,end:127135}];function In(e){for(var r=0;r<yr.length;r+=1){var t=yr[r];if(e>=t.begin&&e<t.end)return r}return-1}function Mn(e,r){var t={},a=new k.Parser(e,r);t.version=a.parseUShort(),t.xAvgCharWidth=a.parseShort(),t.usWeightClass=a.parseUShort(),t.usWidthClass=a.parseUShort(),t.fsType=a.parseUShort(),t.ySubscriptXSize=a.parseShort(),t.ySubscriptYSize=a.parseShort(),t.ySubscriptXOffset=a.parseShort(),t.ySubscriptYOffset=a.parseShort(),t.ySuperscriptXSize=a.parseShort(),t.ySuperscriptYSize=a.parseShort(),t.ySuperscriptXOffset=a.parseShort(),t.ySuperscriptYOffset=a.parseShort(),t.yStrikeoutSize=a.parseShort(),t.yStrikeoutPosition=a.parseShort(),t.sFamilyClass=a.parseShort(),t.panose=[];for(var n=0;n<10;n++)t.panose[n]=a.parseByte();return t.ulUnicodeRange1=a.parseULong(),t.ulUnicodeRange2=a.parseULong(),t.ulUnicodeRange3=a.parseULong(),t.ulUnicodeRange4=a.parseULong(),t.achVendID=String.fromCharCode(a.parseByte(),a.parseByte(),a.parseByte(),a.parseByte()),t.fsSelection=a.parseUShort(),t.usFirstCharIndex=a.parseUShort(),t.usLastCharIndex=a.parseUShort(),t.sTypoAscender=a.parseShort(),t.sTypoDescender=a.parseShort(),t.sTypoLineGap=a.parseShort(),t.usWinAscent=a.parseUShort(),t.usWinDescent=a.parseUShort(),t.version>=1&&(t.ulCodePageRange1=a.parseULong(),t.ulCodePageRange2=a.parseULong()),t.version>=2&&(t.sxHeight=a.parseShort(),t.sCapHeight=a.parseShort(),t.usDefaultChar=a.parseUShort(),t.usBreakChar=a.parseUShort(),t.usMaxContent=a.parseUShort()),t}function Pn(e){return new b.Table(\"OS/2\",[{name:\"version\",type:\"USHORT\",value:3},{name:\"xAvgCharWidth\",type:\"SHORT\",value:0},{name:\"usWeightClass\",type:\"USHORT\",value:0},{name:\"usWidthClass\",type:\"USHORT\",value:0},{name:\"fsType\",type:\"USHORT\",value:0},{name:\"ySubscriptXSize\",type:\"SHORT\",value:650},{name:\"ySubscriptYSize\",type:\"SHORT\",value:699},{name:\"ySubscriptXOffset\",type:\"SHORT\",value:0},{name:\"ySubscriptYOffset\",type:\"SHORT\",value:140},{name:\"ySuperscriptXSize\",type:\"SHORT\",value:650},{name:\"ySuperscriptYSize\",type:\"SHORT\",value:699},{name:\"ySuperscriptXOffset\",type:\"SHORT\",value:0},{name:\"ySuperscriptYOffset\",type:\"SHORT\",value:479},{name:\"yStrikeoutSize\",type:\"SHORT\",value:49},{name:\"yStrikeoutPosition\",type:\"SHORT\",value:258},{name:\"sFamilyClass\",type:\"SHORT\",value:0},{name:\"bFamilyType\",type:\"BYTE\",value:0},{name:\"bSerifStyle\",type:\"BYTE\",value:0},{name:\"bWeight\",type:\"BYTE\",value:0},{name:\"bProportion\",type:\"BYTE\",value:0},{name:\"bContrast\",type:\"BYTE\",value:0},{name:\"bStrokeVariation\",type:\"BYTE\",value:0},{name:\"bArmStyle\",type:\"BYTE\",value:0},{name:\"bLetterform\",type:\"BYTE\",value:0},{name:\"bMidline\",type:\"BYTE\",value:0},{name:\"bXHeight\",type:\"BYTE\",value:0},{name:\"ulUnicodeRange1\",type:\"ULONG\",value:0},{name:\"ulUnicodeRange2\",type:\"ULONG\",value:0},{name:\"ulUnicodeRange3\",type:\"ULONG\",value:0},{name:\"ulUnicodeRange4\",type:\"ULONG\",value:0},{name:\"achVendID\",type:\"CHARARRAY\",value:\"XXXX\"},{name:\"fsSelection\",type:\"USHORT\",value:0},{name:\"usFirstCharIndex\",type:\"USHORT\",value:0},{name:\"usLastCharIndex\",type:\"USHORT\",value:0},{name:\"sTypoAscender\",type:\"SHORT\",value:0},{name:\"sTypoDescender\",type:\"SHORT\",value:0},{name:\"sTypoLineGap\",type:\"SHORT\",value:0},{name:\"usWinAscent\",type:\"USHORT\",value:0},{name:\"usWinDescent\",type:\"USHORT\",value:0},{name:\"ulCodePageRange1\",type:\"ULONG\",value:0},{name:\"ulCodePageRange2\",type:\"ULONG\",value:0},{name:\"sxHeight\",type:\"SHORT\",value:0},{name:\"sCapHeight\",type:\"SHORT\",value:0},{name:\"usDefaultChar\",type:\"USHORT\",value:0},{name:\"usBreakChar\",type:\"USHORT\",value:0},{name:\"usMaxContext\",type:\"USHORT\",value:0}],e)}var xr={parse:Mn,make:Pn,unicodeRanges:yr,getUnicodeRange:In};function Gn(e,r){var t={},a=new k.Parser(e,r);switch(t.version=a.parseVersion(),t.italicAngle=a.parseFixed(),t.underlinePosition=a.parseShort(),t.underlineThickness=a.parseShort(),t.isFixedPitch=a.parseULong(),t.minMemType42=a.parseULong(),t.maxMemType42=a.parseULong(),t.minMemType1=a.parseULong(),t.maxMemType1=a.parseULong(),t.version){case 1:t.names=xe.slice();break;case 2:t.numberOfGlyphs=a.parseUShort(),t.glyphNameIndex=new Array(t.numberOfGlyphs);for(var n=0;n<t.numberOfGlyphs;n++)t.glyphNameIndex[n]=a.parseUShort();t.names=[];for(var s=0;s<t.numberOfGlyphs;s++)if(t.glyphNameIndex[s]>=xe.length){var i=a.parseChar();t.names.push(a.parseString(i))}break;case 2.5:t.numberOfGlyphs=a.parseUShort(),t.offset=new Array(t.numberOfGlyphs);for(var u=0;u<t.numberOfGlyphs;u++)t.offset[u]=a.parseChar();break}return t}function Nn(){return new b.Table(\"post\",[{name:\"version\",type:\"FIXED\",value:196608},{name:\"italicAngle\",type:\"FIXED\",value:0},{name:\"underlinePosition\",type:\"FWORD\",value:0},{name:\"underlineThickness\",type:\"FWORD\",value:0},{name:\"isFixedPitch\",type:\"ULONG\",value:0},{name:\"minMemType42\",type:\"ULONG\",value:0},{name:\"maxMemType42\",type:\"ULONG\",value:0},{name:\"minMemType1\",type:\"ULONG\",value:0},{name:\"maxMemType1\",type:\"ULONG\",value:0}])}var Qt={parse:Gn,make:Nn},ee=new Array(9);ee[1]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();if(t===1)return{substFormat:1,coverage:this.parsePointer(v.coverage),deltaGlyphId:this.parseUShort()};if(t===2)return{substFormat:2,coverage:this.parsePointer(v.coverage),substitute:this.parseOffset16List()};U.assert(!1,\"0x\"+r.toString(16)+\": lookup type 1 format must be 1 or 2.\")};ee[2]=function(){var r=this.parseUShort();return U.argument(r===1,\"GSUB Multiple Substitution Subtable identifier-format must be 1\"),{substFormat:r,coverage:this.parsePointer(v.coverage),sequences:this.parseListOfLists()}};ee[3]=function(){var r=this.parseUShort();return U.argument(r===1,\"GSUB Alternate Substitution Subtable identifier-format must be 1\"),{substFormat:r,coverage:this.parsePointer(v.coverage),alternateSets:this.parseListOfLists()}};ee[4]=function(){var r=this.parseUShort();return U.argument(r===1,\"GSUB ligature table identifier-format must be 1\"),{substFormat:r,coverage:this.parsePointer(v.coverage),ligatureSets:this.parseListOfLists(function(){return{ligGlyph:this.parseUShort(),components:this.parseUShortList(this.parseUShort()-1)}})}};var ke={sequenceIndex:v.uShort,lookupListIndex:v.uShort};ee[5]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();if(t===1)return{substFormat:t,coverage:this.parsePointer(v.coverage),ruleSets:this.parseListOfLists(function(){var s=this.parseUShort(),i=this.parseUShort();return{input:this.parseUShortList(s-1),lookupRecords:this.parseRecordList(i,ke)}})};if(t===2)return{substFormat:t,coverage:this.parsePointer(v.coverage),classDef:this.parsePointer(v.classDef),classSets:this.parseListOfLists(function(){var s=this.parseUShort(),i=this.parseUShort();return{classes:this.parseUShortList(s-1),lookupRecords:this.parseRecordList(i,ke)}})};if(t===3){var a=this.parseUShort(),n=this.parseUShort();return{substFormat:t,coverages:this.parseList(a,v.pointer(v.coverage)),lookupRecords:this.parseRecordList(n,ke)}}U.assert(!1,\"0x\"+r.toString(16)+\": lookup type 5 format must be 1, 2 or 3.\")};ee[6]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();if(t===1)return{substFormat:1,coverage:this.parsePointer(v.coverage),chainRuleSets:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(ke)}})};if(t===2)return{substFormat:2,coverage:this.parsePointer(v.coverage),backtrackClassDef:this.parsePointer(v.classDef),inputClassDef:this.parsePointer(v.classDef),lookaheadClassDef:this.parsePointer(v.classDef),chainClassSet:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(ke)}})};if(t===3)return{substFormat:3,backtrackCoverage:this.parseList(v.pointer(v.coverage)),inputCoverage:this.parseList(v.pointer(v.coverage)),lookaheadCoverage:this.parseList(v.pointer(v.coverage)),lookupRecords:this.parseRecordList(ke)};U.assert(!1,\"0x\"+r.toString(16)+\": lookup type 6 format must be 1, 2 or 3.\")};ee[7]=function(){var r=this.parseUShort();U.argument(r===1,\"GSUB Extension Substitution subtable identifier-format must be 1\");var t=this.parseUShort(),a=new v(this.data,this.offset+this.parseULong());return{substFormat:1,lookupType:t,extension:ee[t].call(a)}};ee[8]=function(){var r=this.parseUShort();return U.argument(r===1,\"GSUB Reverse Chaining Contextual Single Substitution Subtable identifier-format must be 1\"),{substFormat:r,coverage:this.parsePointer(v.coverage),backtrackCoverage:this.parseList(v.pointer(v.coverage)),lookaheadCoverage:this.parseList(v.pointer(v.coverage)),substitutes:this.parseUShortList()}};function Hn(e,r){r=r||0;var t=new v(e,r),a=t.parseVersion(1);return U.argument(a===1||a===1.1,\"Unsupported GSUB table version.\"),a===1?{version:a,scripts:t.parseScriptList(),features:t.parseFeatureList(),lookups:t.parseLookupList(ee)}:{version:a,scripts:t.parseScriptList(),features:t.parseFeatureList(),lookups:t.parseLookupList(ee),variations:t.parseFeatureVariationsList()}}var Ue=new Array(9);Ue[1]=function(r){return r.substFormat===1?new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:1},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)},{name:\"deltaGlyphID\",type:\"USHORT\",value:r.deltaGlyphId}]):new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:2},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.ushortList(\"substitute\",r.substitute)))};Ue[2]=function(r){return U.assert(r.substFormat===1,\"Lookup type 2 substFormat must be 1.\"),new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:1},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.tableList(\"seqSet\",r.sequences,function(t){return new b.Table(\"sequenceSetTable\",b.ushortList(\"sequence\",t))})))};Ue[3]=function(r){return U.assert(r.substFormat===1,\"Lookup type 3 substFormat must be 1.\"),new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:1},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.tableList(\"altSet\",r.alternateSets,function(t){return new b.Table(\"alternateSetTable\",b.ushortList(\"alternate\",t))})))};Ue[4]=function(r){return U.assert(r.substFormat===1,\"Lookup type 4 substFormat must be 1.\"),new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:1},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.tableList(\"ligSet\",r.ligatureSets,function(t){return new b.Table(\"ligatureSetTable\",b.tableList(\"ligature\",t,function(a){return new b.Table(\"ligatureTable\",[{name:\"ligGlyph\",type:\"USHORT\",value:a.ligGlyph}].concat(b.ushortList(\"component\",a.components,a.components.length+1)))}))})))};Ue[6]=function(r){if(r.substFormat===1){var t=new b.Table(\"chainContextTable\",[{name:\"substFormat\",type:\"USHORT\",value:r.substFormat},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.tableList(\"chainRuleSet\",r.chainRuleSets,function(s){return new b.Table(\"chainRuleSetTable\",b.tableList(\"chainRule\",s,function(i){var u=b.ushortList(\"backtrackGlyph\",i.backtrack,i.backtrack.length).concat(b.ushortList(\"inputGlyph\",i.input,i.input.length+1)).concat(b.ushortList(\"lookaheadGlyph\",i.lookahead,i.lookahead.length)).concat(b.ushortList(\"substitution\",[],i.lookupRecords.length));return i.lookupRecords.forEach(function(o,l){u=u.concat({name:\"sequenceIndex\"+l,type:\"USHORT\",value:o.sequenceIndex}).concat({name:\"lookupListIndex\"+l,type:\"USHORT\",value:o.lookupListIndex})}),new b.Table(\"chainRuleTable\",u)}))})));return t}else if(r.substFormat===2)U.assert(!1,\"lookup type 6 format 2 is not yet supported.\");else if(r.substFormat===3){var a=[{name:\"substFormat\",type:\"USHORT\",value:r.substFormat}];a.push({name:\"backtrackGlyphCount\",type:\"USHORT\",value:r.backtrackCoverage.length}),r.backtrackCoverage.forEach(function(s,i){a.push({name:\"backtrackCoverage\"+i,type:\"TABLE\",value:new b.Coverage(s)})}),a.push({name:\"inputGlyphCount\",type:\"USHORT\",value:r.inputCoverage.length}),r.inputCoverage.forEach(function(s,i){a.push({name:\"inputCoverage\"+i,type:\"TABLE\",value:new b.Coverage(s)})}),a.push({name:\"lookaheadGlyphCount\",type:\"USHORT\",value:r.lookaheadCoverage.length}),r.lookaheadCoverage.forEach(function(s,i){a.push({name:\"lookaheadCoverage\"+i,type:\"TABLE\",value:new b.Coverage(s)})}),a.push({name:\"substitutionCount\",type:\"USHORT\",value:r.lookupRecords.length}),r.lookupRecords.forEach(function(s,i){a=a.concat({name:\"sequenceIndex\"+i,type:\"USHORT\",value:s.sequenceIndex}).concat({name:\"lookupListIndex\"+i,type:\"USHORT\",value:s.lookupListIndex})});var n=new b.Table(\"chainContextTable\",a);return n}U.assert(!1,\"lookup type 6 format must be 1, 2 or 3.\")};function zn(e){return new b.Table(\"GSUB\",[{name:\"version\",type:\"ULONG\",value:65536},{name:\"scripts\",type:\"TABLE\",value:new b.ScriptList(e.scripts)},{name:\"features\",type:\"TABLE\",value:new b.FeatureList(e.features)},{name:\"lookups\",type:\"TABLE\",value:new b.LookupList(e.lookups,Ue)}])}var Kt={parse:Hn,make:zn};function Wn(e,r){var t=new k.Parser(e,r),a=t.parseULong();U.argument(a===1,\"Unsupported META table version.\"),t.parseULong(),t.parseULong();for(var n=t.parseULong(),s={},i=0;i<n;i++){var u=t.parseTag(),o=t.parseULong(),l=t.parseULong(),f=Fe.UTF8(e,r+o,l);s[u]=f}return s}function _n(e){var r=Object.keys(e).length,t=\"\",a=16+r*12,n=new b.Table(\"meta\",[{name:\"version\",type:\"ULONG\",value:1},{name:\"flags\",type:\"ULONG\",value:0},{name:\"offset\",type:\"ULONG\",value:a},{name:\"numTags\",type:\"ULONG\",value:r}]);for(var s in e){var i=t.length;t+=e[s],n.fields.push({name:\"tag \"+s,type:\"TAG\",value:s}),n.fields.push({name:\"offset \"+s,type:\"ULONG\",value:a+i}),n.fields.push({name:\"length \"+s,type:\"ULONG\",value:e[s].length})}return n.fields.push({name:\"stringPool\",type:\"CHARARRAY\",value:t}),n}var Jt={parse:Wn,make:_n};function Zr(e){return Math.log(e)/Math.log(2)|0}function Er(e){for(;e.length%4!==0;)e.push(0);for(var r=0,t=0;t<e.length;t+=4)r+=(e[t]<<24)+(e[t+1]<<16)+(e[t+2]<<8)+e[t+3];return r%=Math.pow(2,32),r}function Qr(e,r,t,a){return new b.Record(\"Table Record\",[{name:\"tag\",type:\"TAG\",value:e!==void 0?e:\"\"},{name:\"checkSum\",type:\"ULONG\",value:r!==void 0?r:0},{name:\"offset\",type:\"ULONG\",value:t!==void 0?t:0},{name:\"length\",type:\"ULONG\",value:a!==void 0?a:0}])}function jt(e){var r=new b.Table(\"sfnt\",[{name:\"version\",type:\"TAG\",value:\"OTTO\"},{name:\"numTables\",type:\"USHORT\",value:0},{name:\"searchRange\",type:\"USHORT\",value:0},{name:\"entrySelector\",type:\"USHORT\",value:0},{name:\"rangeShift\",type:\"USHORT\",value:0}]);r.tables=e,r.numTables=e.length;var t=Math.pow(2,Zr(r.numTables));r.searchRange=16*t,r.entrySelector=Zr(t),r.rangeShift=r.numTables*16-r.searchRange;for(var a=[],n=[],s=r.sizeOf()+Qr().sizeOf()*r.numTables;s%4!==0;)s+=1,n.push({name:\"padding\",type:\"BYTE\",value:0});for(var i=0;i<e.length;i+=1){var u=e[i];U.argument(u.tableName.length===4,\"Table name\"+u.tableName+\" is invalid.\");var o=u.sizeOf(),l=Qr(u.tableName,Er(u.encode()),s,o);for(a.push({name:l.tag+\" Table Record\",type:\"RECORD\",value:l}),n.push({name:u.tableName+\" table\",type:\"RECORD\",value:u}),s+=o,U.argument(!isNaN(s),\"Something went wrong calculating the offset.\");s%4!==0;)s+=1,n.push({name:\"padding\",type:\"BYTE\",value:0})}return a.sort(function(f,h){return f.value.tag>h.value.tag?1:-1}),r.fields=r.fields.concat(a),r.fields=r.fields.concat(n),r}function Kr(e,r,t){for(var a=0;a<r.length;a+=1){var n=e.charToGlyphIndex(r[a]);if(n>0){var s=e.glyphs.get(n);return s.getMetrics()}}return t}function Vn(e){for(var r=0,t=0;t<e.length;t+=1)r+=e[t];return r/e.length}function qn(e){for(var r=[],t=[],a=[],n=[],s=[],i=[],u=[],o,l=0,f=0,h=0,p=0,c=0,d=0;d<e.glyphs.length;d+=1){var x=e.glyphs.get(d),m=x.unicode|0;if(isNaN(x.advanceWidth))throw new Error(\"Glyph \"+x.name+\" (\"+d+\"): advanceWidth is not a number.\");(o>m||o===void 0)&&m>0&&(o=m),l<m&&(l=m);var y=xr.getUnicodeRange(m);if(y<32)f|=1<<y;else if(y<64)h|=1<<y-32;else if(y<96)p|=1<<y-64;else if(y<123)c|=1<<y-96;else throw new Error(\"Unicode ranges bits > 123 are reserved for internal usage\");if(x.name!==\".notdef\"){var C=x.getMetrics();r.push(C.xMin),t.push(C.yMin),a.push(C.xMax),n.push(C.yMax),i.push(C.leftSideBearing),u.push(C.rightSideBearing),s.push(x.advanceWidth)}}var S={xMin:Math.min.apply(null,r),yMin:Math.min.apply(null,t),xMax:Math.max.apply(null,a),yMax:Math.max.apply(null,n),advanceWidthMax:Math.max.apply(null,s),advanceWidthAvg:Vn(s),minLeftSideBearing:Math.min.apply(null,i),maxLeftSideBearing:Math.max.apply(null,i),minRightSideBearing:Math.min.apply(null,u)};S.ascender=e.ascender,S.descender=e.descender;var R=Nt.make({flags:3,unitsPerEm:e.unitsPerEm,xMin:S.xMin,yMin:S.yMin,xMax:S.xMax,yMax:S.yMax,lowestRecPPEM:3,createdTimestamp:e.createdTimestamp}),O=Ht.make({ascender:S.ascender,descender:S.descender,advanceWidthMax:S.advanceWidthMax,minLeftSideBearing:S.minLeftSideBearing,minRightSideBearing:S.minRightSideBearing,xMaxExtent:S.maxLeftSideBearing+(S.xMax-S.xMin),numberOfHMetrics:e.glyphs.length}),D=_t.make(e.glyphs.length),L=xr.make(Object.assign({xAvgCharWidth:Math.round(S.advanceWidthAvg),usFirstCharIndex:o,usLastCharIndex:l,ulUnicodeRange1:f,ulUnicodeRange2:h,ulUnicodeRange3:p,ulUnicodeRange4:c,sTypoAscender:S.ascender,sTypoDescender:S.descender,sTypoLineGap:0,usWinAscent:S.yMax,usWinDescent:Math.abs(S.yMin),ulCodePageRange1:1,sxHeight:Kr(e,\"xyvw\",{yMax:Math.round(S.ascender/2)}).yMax,sCapHeight:Kr(e,\"HIKLEFJMNTZBDPRAGOQSUVWXY\",S).yMax,usDefaultChar:e.hasChar(\" \")?32:0,usBreakChar:e.hasChar(\" \")?32:0},e.tables.os2)),F=zt.make(e.glyphs),G=Et.make(e.glyphs),Y=e.getEnglishName(\"fontFamily\"),Z=e.getEnglishName(\"fontSubfamily\"),j=Y+\" \"+Z,$=e.getEnglishName(\"postScriptName\");$||($=Y.replace(/\\s/g,\"\")+\"-\"+Z);var M={};for(var N in e.names)M[N]=e.names[N];M.uniqueID||(M.uniqueID={en:e.getEnglishName(\"manufacturer\")+\":\"+j}),M.postScriptName||(M.postScriptName={en:$}),M.preferredFamily||(M.preferredFamily=e.names.fontFamily),M.preferredSubfamily||(M.preferredSubfamily=e.names.fontSubfamily);var W=[],_=Zt.make(M,W),V=W.length>0?Wt.make(W):void 0,H=Qt.make(),X=Gt.make(e.glyphs,{version:e.getEnglishName(\"version\"),fullName:j,familyName:Y,weightName:Z,postScriptName:$,unitsPerEm:e.unitsPerEm,fontBBox:[0,S.yMin,S.ascender,S.advanceWidthMax]}),A=e.metas&&Object.keys(e.metas).length>0?Jt.make(e.metas):void 0,q=[R,O,D,L,_,G,H,X,F];V&&q.push(V),e.tables.gsub&&q.push(Kt.make(e.tables.gsub)),A&&q.push(A);for(var rr=jt(q),ha=rr.encode(),ca=Er(ha),tr=rr.fields,Ar=!1,Ge=0;Ge<tr.length;Ge+=1)if(tr[Ge].name===\"head table\"){tr[Ge].value.checkSumAdjustment=2981146554-ca,Ar=!0;break}if(!Ar)throw new Error(\"Could not find head table with checkSum to adjust.\");return rr}var Xn={make:jt,fontToTable:qn,computeCheckSum:Er};function sr(e,r){for(var t=0,a=e.length-1;t<=a;){var n=t+a>>>1,s=e[n].tag;if(s===r)return n;s<r?t=n+1:a=n-1}return-t-1}function Jr(e,r){for(var t=0,a=e.length-1;t<=a;){var n=t+a>>>1,s=e[n];if(s===r)return n;s<r?t=n+1:a=n-1}return-t-1}function jr(e,r){for(var t,a=0,n=e.length-1;a<=n;){var s=a+n>>>1;t=e[s];var i=t.start;if(i===r)return t;i<r?a=s+1:n=s-1}if(a>0)return t=e[a-1],r>t.end?0:t}function Ae(e,r){this.font=e,this.tableName=r}Ae.prototype={searchTag:sr,binSearch:Jr,getTable:function(e){var r=this.font.tables[this.tableName];return!r&&e&&(r=this.font.tables[this.tableName]=this.createDefaultTable()),r},getScriptNames:function(){var e=this.getTable();return e?e.scripts.map(function(r){return r.tag}):[]},getDefaultScriptName:function(){var e=this.getTable();if(!!e){for(var r=!1,t=0;t<e.scripts.length;t++){var a=e.scripts[t].tag;if(a===\"DFLT\")return a;a===\"latn\"&&(r=!0)}if(r)return\"latn\"}},getScriptTable:function(e,r){var t=this.getTable(r);if(t){e=e||\"DFLT\";var a=t.scripts,n=sr(t.scripts,e);if(n>=0)return a[n].script;if(r){var s={tag:e,script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}};return a.splice(-1-n,0,s),s.script}}},getLangSysTable:function(e,r,t){var a=this.getScriptTable(e,t);if(a){if(!r||r===\"dflt\"||r===\"DFLT\")return a.defaultLangSys;var n=sr(a.langSysRecords,r);if(n>=0)return a.langSysRecords[n].langSys;if(t){var s={tag:r,langSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]}};return a.langSysRecords.splice(-1-n,0,s),s.langSys}}},getFeatureTable:function(e,r,t,a){var n=this.getLangSysTable(e,r,a);if(n){for(var s,i=n.featureIndexes,u=this.font.tables[this.tableName].features,o=0;o<i.length;o++)if(s=u[i[o]],s.tag===t)return s.feature;if(a){var l=u.length;return U.assert(l===0||t>=u[l-1].tag,\"Features must be added in alphabetical order.\"),s={tag:t,feature:{params:0,lookupListIndexes:[]}},u.push(s),i.push(l),s.feature}}},getLookupTables:function(e,r,t,a,n){var s=this.getFeatureTable(e,r,t,n),i=[];if(s){for(var u,o=s.lookupListIndexes,l=this.font.tables[this.tableName].lookups,f=0;f<o.length;f++)u=l[o[f]],u.lookupType===a&&i.push(u);if(i.length===0&&n){u={lookupType:a,lookupFlag:0,subtables:[],markFilteringSet:void 0};var h=l.length;return l.push(u),o.push(h),[u]}}return i},getGlyphClass:function(e,r){switch(e.format){case 1:return e.startGlyph<=r&&r<e.startGlyph+e.classes.length?e.classes[r-e.startGlyph]:0;case 2:var t=jr(e.ranges,r);return t?t.classId:0}},getCoverageIndex:function(e,r){switch(e.format){case 1:var t=Jr(e.glyphs,r);return t>=0?t:-1;case 2:var a=jr(e.ranges,r);return a?a.index+r-a.start:-1}},expandCoverage:function(e){if(e.format===1)return e.glyphs;for(var r=[],t=e.ranges,a=0;a<t.length;a++)for(var n=t[a],s=n.start,i=n.end,u=s;u<=i;u++)r.push(u);return r}};function Be(e){Ae.call(this,e,\"gpos\")}Be.prototype=Ae.prototype;Be.prototype.init=function(){var e=this.getDefaultScriptName();this.defaultKerningTables=this.getKerningTables(e)};Be.prototype.getKerningValue=function(e,r,t){for(var a=0;a<e.length;a++)for(var n=e[a].subtables,s=0;s<n.length;s++){var i=n[s],u=this.getCoverageIndex(i.coverage,r);if(!(u<0))switch(i.posFormat){case 1:for(var o=i.pairSets[u],l=0;l<o.length;l++){var f=o[l];if(f.secondGlyph===t)return f.value1&&f.value1.xAdvance||0}break;case 2:var h=this.getGlyphClass(i.classDef1,r),p=this.getGlyphClass(i.classDef2,t),c=i.classRecords[h][p];return c.value1&&c.value1.xAdvance||0}}return 0};Be.prototype.getKerningTables=function(e,r){if(this.font.tables.gpos)return this.getLookupTables(e,r,\"kern\",2)};function K(e){Ae.call(this,e,\"gsub\")}function Yn(e,r){var t=e.length;if(t!==r.length)return!1;for(var a=0;a<t;a++)if(e[a]!==r[a])return!1;return!0}function Or(e,r,t){for(var a=e.subtables,n=0;n<a.length;n++){var s=a[n];if(s.substFormat===r)return s}if(t)return a.push(t),t}K.prototype=Ae.prototype;K.prototype.createDefaultTable=function(){return{version:1,scripts:[{tag:\"DFLT\",script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}}],features:[],lookups:[]}};K.prototype.getSingle=function(e,r,t){for(var a=[],n=this.getLookupTables(r,t,e,1),s=0;s<n.length;s++)for(var i=n[s].subtables,u=0;u<i.length;u++){var o=i[u],l=this.expandCoverage(o.coverage),f=void 0;if(o.substFormat===1){var h=o.deltaGlyphId;for(f=0;f<l.length;f++){var p=l[f];a.push({sub:p,by:p+h})}}else{var c=o.substitute;for(f=0;f<l.length;f++)a.push({sub:l[f],by:c[f]})}}return a};K.prototype.getMultiple=function(e,r,t){for(var a=[],n=this.getLookupTables(r,t,e,2),s=0;s<n.length;s++)for(var i=n[s].subtables,u=0;u<i.length;u++){var o=i[u],l=this.expandCoverage(o.coverage),f=void 0;for(f=0;f<l.length;f++){var h=l[f],p=o.sequences[f];a.push({sub:h,by:p})}}return a};K.prototype.getAlternates=function(e,r,t){for(var a=[],n=this.getLookupTables(r,t,e,3),s=0;s<n.length;s++)for(var i=n[s].subtables,u=0;u<i.length;u++)for(var o=i[u],l=this.expandCoverage(o.coverage),f=o.alternateSets,h=0;h<l.length;h++)a.push({sub:l[h],by:f[h]});return a};K.prototype.getLigatures=function(e,r,t){for(var a=[],n=this.getLookupTables(r,t,e,4),s=0;s<n.length;s++)for(var i=n[s].subtables,u=0;u<i.length;u++)for(var o=i[u],l=this.expandCoverage(o.coverage),f=o.ligatureSets,h=0;h<l.length;h++)for(var p=l[h],c=f[h],d=0;d<c.length;d++){var x=c[d];a.push({sub:[p].concat(x.components),by:x.ligGlyph})}return a};K.prototype.addSingle=function(e,r,t,a){var n=this.getLookupTables(t,a,e,1,!0)[0],s=Or(n,2,{substFormat:2,coverage:{format:1,glyphs:[]},substitute:[]});U.assert(s.coverage.format===1,\"Single: unable to modify coverage table format \"+s.coverage.format);var i=r.sub,u=this.binSearch(s.coverage.glyphs,i);u<0&&(u=-1-u,s.coverage.glyphs.splice(u,0,i),s.substitute.splice(u,0,0)),s.substitute[u]=r.by};K.prototype.addMultiple=function(e,r,t,a){U.assert(r.by instanceof Array&&r.by.length>1,'Multiple: \"by\" must be an array of two or more ids');var n=this.getLookupTables(t,a,e,2,!0)[0],s=Or(n,1,{substFormat:1,coverage:{format:1,glyphs:[]},sequences:[]});U.assert(s.coverage.format===1,\"Multiple: unable to modify coverage table format \"+s.coverage.format);var i=r.sub,u=this.binSearch(s.coverage.glyphs,i);u<0&&(u=-1-u,s.coverage.glyphs.splice(u,0,i),s.sequences.splice(u,0,0)),s.sequences[u]=r.by};K.prototype.addAlternate=function(e,r,t,a){var n=this.getLookupTables(t,a,e,3,!0)[0],s=Or(n,1,{substFormat:1,coverage:{format:1,glyphs:[]},alternateSets:[]});U.assert(s.coverage.format===1,\"Alternate: unable to modify coverage table format \"+s.coverage.format);var i=r.sub,u=this.binSearch(s.coverage.glyphs,i);u<0&&(u=-1-u,s.coverage.glyphs.splice(u,0,i),s.alternateSets.splice(u,0,0)),s.alternateSets[u]=r.by};K.prototype.addLigature=function(e,r,t,a){var n=this.getLookupTables(t,a,e,4,!0)[0],s=n.subtables[0];s||(s={substFormat:1,coverage:{format:1,glyphs:[]},ligatureSets:[]},n.subtables[0]=s),U.assert(s.coverage.format===1,\"Ligature: unable to modify coverage table format \"+s.coverage.format);var i=r.sub[0],u=r.sub.slice(1),o={ligGlyph:r.by,components:u},l=this.binSearch(s.coverage.glyphs,i);if(l>=0){for(var f=s.ligatureSets[l],h=0;h<f.length;h++)if(Yn(f[h].components,u))return;f.push(o)}else l=-1-l,s.coverage.glyphs.splice(l,0,i),s.ligatureSets.splice(l,0,[o])};K.prototype.getFeature=function(e,r,t){if(/ss\\d\\d/.test(e))return this.getSingle(e,r,t);switch(e){case\"aalt\":case\"salt\":return this.getSingle(e,r,t).concat(this.getAlternates(e,r,t));case\"dlig\":case\"liga\":case\"rlig\":return this.getLigatures(e,r,t);case\"ccmp\":return this.getMultiple(e,r,t).concat(this.getLigatures(e,r,t));case\"stch\":return this.getMultiple(e,r,t)}};K.prototype.add=function(e,r,t,a){if(/ss\\d\\d/.test(e))return this.addSingle(e,r,t,a);switch(e){case\"aalt\":case\"salt\":return typeof r.by==\"number\"?this.addSingle(e,r,t,a):this.addAlternate(e,r,t,a);case\"dlig\":case\"liga\":case\"rlig\":return this.addLigature(e,r,t,a);case\"ccmp\":return r.by instanceof Array?this.addMultiple(e,r,t,a):this.addLigature(e,r,t,a)}};function Zn(){return typeof window<\"u\"}function $t(e){for(var r=new ArrayBuffer(e.length),t=new Uint8Array(r),a=0;a<e.length;++a)t[a]=e[a];return r}function Qn(e){for(var r=new Buffer(e.byteLength),t=new Uint8Array(e),a=0;a<r.length;++a)r[a]=t[a];return r}function Ee(e,r){if(!e)throw r}function $r(e,r,t,a,n){var s;return(r&a)>0?(s=e.parseByte(),(r&n)===0&&(s=-s),s=t+s):(r&n)>0?s=t:s=t+e.parseShort(),s}function ea(e,r,t){var a=new k.Parser(r,t);e.numberOfContours=a.parseShort(),e._xMin=a.parseShort(),e._yMin=a.parseShort(),e._xMax=a.parseShort(),e._yMax=a.parseShort();var n,s;if(e.numberOfContours>0){for(var i=e.endPointIndices=[],u=0;u<e.numberOfContours;u+=1)i.push(a.parseUShort());e.instructionLength=a.parseUShort(),e.instructions=[];for(var o=0;o<e.instructionLength;o+=1)e.instructions.push(a.parseByte());var l=i[i.length-1]+1;n=[];for(var f=0;f<l;f+=1)if(s=a.parseByte(),n.push(s),(s&8)>0)for(var h=a.parseByte(),p=0;p<h;p+=1)n.push(s),f+=1;if(U.argument(n.length===l,\"Bad flags.\"),i.length>0){var c=[],d;if(l>0){for(var x=0;x<l;x+=1)s=n[x],d={},d.onCurve=!!(s&1),d.lastPointOfContour=i.indexOf(x)>=0,c.push(d);for(var m=0,y=0;y<l;y+=1)s=n[y],d=c[y],d.x=$r(a,s,m,2,16),m=d.x;for(var C=0,S=0;S<l;S+=1)s=n[S],d=c[S],d.y=$r(a,s,C,4,32),C=d.y}e.points=c}else e.points=[]}else if(e.numberOfContours===0)e.points=[];else{e.isComposite=!0,e.points=[],e.components=[];for(var R=!0;R;){n=a.parseUShort();var O={glyphIndex:a.parseUShort(),xScale:1,scale01:0,scale10:0,yScale:1,dx:0,dy:0};(n&1)>0?(n&2)>0?(O.dx=a.parseShort(),O.dy=a.parseShort()):O.matchedPoints=[a.parseUShort(),a.parseUShort()]:(n&2)>0?(O.dx=a.parseChar(),O.dy=a.parseChar()):O.matchedPoints=[a.parseByte(),a.parseByte()],(n&8)>0?O.xScale=O.yScale=a.parseF2Dot14():(n&64)>0?(O.xScale=a.parseF2Dot14(),O.yScale=a.parseF2Dot14()):(n&128)>0&&(O.xScale=a.parseF2Dot14(),O.scale01=a.parseF2Dot14(),O.scale10=a.parseF2Dot14(),O.yScale=a.parseF2Dot14()),e.components.push(O),R=!!(n&32)}if(n&256){e.instructionLength=a.parseUShort(),e.instructions=[];for(var D=0;D<e.instructionLength;D+=1)e.instructions.push(a.parseByte())}}}function ir(e,r){for(var t=[],a=0;a<e.length;a+=1){var n=e[a],s={x:r.xScale*n.x+r.scale01*n.y+r.dx,y:r.scale10*n.x+r.yScale*n.y+r.dy,onCurve:n.onCurve,lastPointOfContour:n.lastPointOfContour};t.push(s)}return t}function Kn(e){for(var r=[],t=[],a=0;a<e.length;a+=1){var n=e[a];t.push(n),n.lastPointOfContour&&(r.push(t),t=[])}return U.argument(t.length===0,\"There are still points left in the current contour.\"),r}function ra(e){var r=new P;if(!e)return r;for(var t=Kn(e),a=0;a<t.length;++a){var n=t[a],s=null,i=n[n.length-1],u=n[0];if(i.onCurve)r.moveTo(i.x,i.y);else if(u.onCurve)r.moveTo(u.x,u.y);else{var o={x:(i.x+u.x)*.5,y:(i.y+u.y)*.5};r.moveTo(o.x,o.y)}for(var l=0;l<n.length;++l)if(s=i,i=u,u=n[(l+1)%n.length],i.onCurve)r.lineTo(i.x,i.y);else{var f=s,h=u;s.onCurve||(f={x:(i.x+s.x)*.5,y:(i.y+s.y)*.5}),u.onCurve||(h={x:(i.x+u.x)*.5,y:(i.y+u.y)*.5}),r.quadraticCurveTo(i.x,i.y,h.x,h.y)}r.closePath()}return r}function ta(e,r){if(r.isComposite)for(var t=0;t<r.components.length;t+=1){var a=r.components[t],n=e.get(a.glyphIndex);if(n.getPath(),n.points){var s=void 0;if(a.matchedPoints===void 0)s=ir(n.points,a);else{if(a.matchedPoints[0]>r.points.length-1||a.matchedPoints[1]>n.points.length-1)throw Error(\"Matched points out of range in \"+r.name);var i=r.points[a.matchedPoints[0]],u=n.points[a.matchedPoints[1]],o={xScale:a.xScale,scale01:a.scale01,scale10:a.scale10,yScale:a.yScale,dx:0,dy:0};u=ir([u],o)[0],o.dx=i.x-u.x,o.dy=i.y-u.y,s=ir(n.points,o)}r.points=r.points.concat(s)}}return ra(r.points)}function Jn(e,r,t,a){for(var n=new ue.GlyphSet(a),s=0;s<t.length-1;s+=1){var i=t[s],u=t[s+1];i!==u?n.push(s,ue.ttfGlyphLoader(a,s,ea,e,r+i,ta)):n.push(s,ue.glyphLoader(a,s))}return n}function jn(e,r,t,a){var n=new ue.GlyphSet(a);return a._push=function(s){var i=t[s],u=t[s+1];i!==u?n.push(s,ue.ttfGlyphLoader(a,s,ea,e,r+i,ta)):n.push(s,ue.glyphLoader(a,s))},n}function $n(e,r,t,a,n){return n.lowMemory?jn(e,r,t,a):Jn(e,r,t,a)}var aa={getPath:ra,parse:$n},na,Se,sa,br;function ia(e){this.font=e,this.getCommands=function(r){return aa.getPath(r).commands},this._fpgmState=this._prepState=void 0,this._errorState=0}function es(e){return e}function oa(e){return Math.sign(e)*Math.round(Math.abs(e))}function rs(e){return Math.sign(e)*Math.round(Math.abs(e*2))/2}function ts(e){return Math.sign(e)*(Math.round(Math.abs(e)+.5)-.5)}function as(e){return Math.sign(e)*Math.ceil(Math.abs(e))}function ns(e){return Math.sign(e)*Math.floor(Math.abs(e))}var ua=function(e){var r=this.srPeriod,t=this.srPhase,a=this.srThreshold,n=1;return e<0&&(e=-e,n=-1),e+=a-t,e=Math.trunc(e/r)*r,e+=t,e<0?t*n:e*n},oe={x:1,y:0,axis:\"x\",distance:function(e,r,t,a){return(t?e.xo:e.x)-(a?r.xo:r.x)},interpolate:function(e,r,t,a){var n,s,i,u,o,l,f;if(!a||a===this){if(n=e.xo-r.xo,s=e.xo-t.xo,o=r.x-r.xo,l=t.x-t.xo,i=Math.abs(n),u=Math.abs(s),f=i+u,f===0){e.x=e.xo+(o+l)/2;return}e.x=e.xo+(o*u+l*i)/f;return}if(n=a.distance(e,r,!0,!0),s=a.distance(e,t,!0,!0),o=a.distance(r,r,!1,!0),l=a.distance(t,t,!1,!0),i=Math.abs(n),u=Math.abs(s),f=i+u,f===0){oe.setRelative(e,e,(o+l)/2,a,!0);return}oe.setRelative(e,e,(o*u+l*i)/f,a,!0)},normalSlope:Number.NEGATIVE_INFINITY,setRelative:function(e,r,t,a,n){if(!a||a===this){e.x=(n?r.xo:r.x)+t;return}var s=n?r.xo:r.x,i=n?r.yo:r.y,u=s+t*a.x,o=i+t*a.y;e.x=u+(e.y-o)/a.normalSlope},slope:0,touch:function(e){e.xTouched=!0},touched:function(e){return e.xTouched},untouch:function(e){e.xTouched=!1}},le={x:0,y:1,axis:\"y\",distance:function(e,r,t,a){return(t?e.yo:e.y)-(a?r.yo:r.y)},interpolate:function(e,r,t,a){var n,s,i,u,o,l,f;if(!a||a===this){if(n=e.yo-r.yo,s=e.yo-t.yo,o=r.y-r.yo,l=t.y-t.yo,i=Math.abs(n),u=Math.abs(s),f=i+u,f===0){e.y=e.yo+(o+l)/2;return}e.y=e.yo+(o*u+l*i)/f;return}if(n=a.distance(e,r,!0,!0),s=a.distance(e,t,!0,!0),o=a.distance(r,r,!1,!0),l=a.distance(t,t,!1,!0),i=Math.abs(n),u=Math.abs(s),f=i+u,f===0){le.setRelative(e,e,(o+l)/2,a,!0);return}le.setRelative(e,e,(o*u+l*i)/f,a,!0)},normalSlope:0,setRelative:function(e,r,t,a,n){if(!a||a===this){e.y=(n?r.yo:r.y)+t;return}var s=n?r.xo:r.x,i=n?r.yo:r.y,u=s+t*a.x,o=i+t*a.y;e.y=o+a.normalSlope*(e.x-u)},slope:Number.POSITIVE_INFINITY,touch:function(e){e.yTouched=!0},touched:function(e){return e.yTouched},untouch:function(e){e.yTouched=!1}};Object.freeze(oe);Object.freeze(le);function Ie(e,r){this.x=e,this.y=r,this.axis=void 0,this.slope=r/e,this.normalSlope=-e/r,Object.freeze(this)}Ie.prototype.distance=function(e,r,t,a){return this.x*oe.distance(e,r,t,a)+this.y*le.distance(e,r,t,a)};Ie.prototype.interpolate=function(e,r,t,a){var n,s,i,u,o,l,f;if(i=a.distance(e,r,!0,!0),u=a.distance(e,t,!0,!0),n=a.distance(r,r,!1,!0),s=a.distance(t,t,!1,!0),o=Math.abs(i),l=Math.abs(u),f=o+l,f===0){this.setRelative(e,e,(n+s)/2,a,!0);return}this.setRelative(e,e,(n*l+s*o)/f,a,!0)};Ie.prototype.setRelative=function(e,r,t,a,n){a=a||this;var s=n?r.xo:r.x,i=n?r.yo:r.y,u=s+t*a.x,o=i+t*a.y,l=a.normalSlope,f=this.slope,h=e.x,p=e.y;e.x=(f*h-l*u+o-p)/(f-l),e.y=f*(e.x-h)+p};Ie.prototype.touch=function(e){e.xTouched=!0,e.yTouched=!0};function Me(e,r){var t=Math.sqrt(e*e+r*r);return e/=t,r/=t,e===1&&r===0?oe:e===0&&r===1?le:new Ie(e,r)}function fe(e,r,t,a){this.x=this.xo=Math.round(e*64)/64,this.y=this.yo=Math.round(r*64)/64,this.lastPointOfContour=t,this.onCurve=a,this.prevPointOnContour=void 0,this.nextPointOnContour=void 0,this.xTouched=!1,this.yTouched=!1,Object.preventExtensions(this)}fe.prototype.nextTouched=function(e){for(var r=this.nextPointOnContour;!e.touched(r)&&r!==this;)r=r.nextPointOnContour;return r};fe.prototype.prevTouched=function(e){for(var r=this.prevPointOnContour;!e.touched(r)&&r!==this;)r=r.prevPointOnContour;return r};var De=Object.freeze(new fe(0,0)),ss={cvCutIn:17/16,deltaBase:9,deltaShift:.125,loop:1,minDis:1,autoFlip:!0};function de(e,r){switch(this.env=e,this.stack=[],this.prog=r,e){case\"glyf\":this.zp0=this.zp1=this.zp2=1,this.rp0=this.rp1=this.rp2=0;case\"prep\":this.fv=this.pv=this.dpv=oe,this.round=oa}}ia.prototype.exec=function(e,r){if(typeof r!=\"number\")throw new Error(\"Point size is not a number!\");if(!(this._errorState>2)){var t=this.font,a=this._prepState;if(!a||a.ppem!==r){var n=this._fpgmState;if(!n){de.prototype=ss,n=this._fpgmState=new de(\"fpgm\",t.tables.fpgm),n.funcs=[],n.font=t,exports.DEBUG&&(console.log(\"---EXEC FPGM---\"),n.step=-1);try{Se(n)}catch(l){console.log(\"Hinting error in FPGM:\"+l),this._errorState=3;return}}de.prototype=n,a=this._prepState=new de(\"prep\",t.tables.prep),a.ppem=r;var s=t.tables.cvt;if(s)for(var i=a.cvt=new Array(s.length),u=r/t.unitsPerEm,o=0;o<s.length;o++)i[o]=s[o]*u;else a.cvt=[];exports.DEBUG&&(console.log(\"---EXEC PREP---\"),a.step=-1);try{Se(a)}catch(l){this._errorState<2&&console.log(\"Hinting error in PREP:\"+l),this._errorState=2}}if(!(this._errorState>1))try{return sa(e,a)}catch(l){this._errorState<1&&(console.log(\"Hinting error:\"+l),console.log(\"Note: further hinting errors are silenced\")),this._errorState=1;return}}};sa=function(e,r){var t=r.ppem/r.font.unitsPerEm,a=t,n=e.components,s,i,u;if(de.prototype=r,!n)u=new de(\"glyf\",e.instructions),exports.DEBUG&&(console.log(\"---EXEC GLYPH---\"),u.step=-1),br(e,u,t,a),i=u.gZone;else{var o=r.font;i=[],s=[];for(var l=0;l<n.length;l++){var f=n[l],h=o.glyphs.get(f.glyphIndex);u=new de(\"glyf\",h.instructions),exports.DEBUG&&(console.log(\"---EXEC COMP \"+l+\"---\"),u.step=-1),br(h,u,t,a);for(var p=Math.round(f.dx*t),c=Math.round(f.dy*a),d=u.gZone,x=u.contours,m=0;m<d.length;m++){var y=d[m];y.xTouched=y.yTouched=!1,y.xo=y.x=y.x+p,y.yo=y.y=y.y+c}var C=i.length;i.push.apply(i,d);for(var S=0;S<x.length;S++)s.push(x[S]+C)}e.instructions&&!u.inhibitGridFit&&(u=new de(\"glyf\",e.instructions),u.gZone=u.z0=u.z1=u.z2=i,u.contours=s,i.push(new fe(0,0),new fe(Math.round(e.advanceWidth*t),0)),exports.DEBUG&&(console.log(\"---EXEC COMPOSITE---\"),u.step=-1),Se(u),i.length-=2)}return i};br=function(e,r,t,a){for(var n=e.points||[],s=n.length,i=r.gZone=r.z0=r.z1=r.z2=[],u=r.contours=[],o,l=0;l<s;l++)o=n[l],i[l]=new fe(o.x*t,o.y*a,o.lastPointOfContour,o.onCurve);for(var f,h,p=0;p<s;p++)o=i[p],f||(f=o,u.push(p)),o.lastPointOfContour?(o.nextPointOnContour=f,f.prevPointOnContour=o,f=void 0):(h=i[p+1],o.nextPointOnContour=h,h.prevPointOnContour=o);if(!r.inhibitGridFit){if(exports.DEBUG){console.log(\"PROCESSING GLYPH\",r.stack);for(var c=0;c<s;c++)console.log(c,i[c].x,i[c].y)}if(i.push(new fe(0,0),new fe(Math.round(e.advanceWidth*t),0)),Se(r),i.length-=2,exports.DEBUG){console.log(\"FINISHED GLYPH\",r.stack);for(var d=0;d<s;d++)console.log(d,i[d].x,i[d].y)}}};Se=function(e){var r=e.prog;if(!!r){var t=r.length,a;for(e.ip=0;e.ip<t;e.ip++){if(exports.DEBUG&&e.step++,a=na[r[e.ip]],!a)throw new Error(\"unknown instruction: 0x\"+Number(r[e.ip]).toString(16));a(e)}}};function $e(e){for(var r=e.tZone=new Array(e.gZone.length),t=0;t<r.length;t++)r[t]=new fe(0,0)}function la(e,r){var t=e.prog,a=e.ip,n=1,s;do if(s=t[++a],s===88)n++;else if(s===89)n--;else if(s===64)a+=t[a+1]+1;else if(s===65)a+=2*t[a+1]+1;else if(s>=176&&s<=183)a+=s-176+1;else if(s>=184&&s<=191)a+=(s-184+1)*2;else if(r&&n===1&&s===27)break;while(n>0);e.ip=a}function et(e,r){exports.DEBUG&&console.log(r.step,\"SVTCA[\"+e.axis+\"]\"),r.fv=r.pv=r.dpv=e}function rt(e,r){exports.DEBUG&&console.log(r.step,\"SPVTCA[\"+e.axis+\"]\"),r.pv=r.dpv=e}function tt(e,r){exports.DEBUG&&console.log(r.step,\"SFVTCA[\"+e.axis+\"]\"),r.fv=e}function at(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z2[a],i=r.z1[n];exports.DEBUG&&console.log(\"SPVTL[\"+e+\"]\",a,n);var u,o;e?(u=s.y-i.y,o=i.x-s.x):(u=i.x-s.x,o=i.y-s.y),r.pv=r.dpv=Me(u,o)}function nt(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z2[a],i=r.z1[n];exports.DEBUG&&console.log(\"SFVTL[\"+e+\"]\",a,n);var u,o;e?(u=s.y-i.y,o=i.x-s.x):(u=i.x-s.x,o=i.y-s.y),r.fv=Me(u,o)}function is(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"SPVFS[]\",t,a),e.pv=e.dpv=Me(a,t)}function os(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"SPVFS[]\",t,a),e.fv=Me(a,t)}function us(e){var r=e.stack,t=e.pv;exports.DEBUG&&console.log(e.step,\"GPV[]\"),r.push(t.x*16384),r.push(t.y*16384)}function ls(e){var r=e.stack,t=e.fv;exports.DEBUG&&console.log(e.step,\"GFV[]\"),r.push(t.x*16384),r.push(t.y*16384)}function fs(e){e.fv=e.pv,exports.DEBUG&&console.log(e.step,\"SFVTPV[]\")}function ps(e){var r=e.stack,t=r.pop(),a=r.pop(),n=r.pop(),s=r.pop(),i=r.pop(),u=e.z0,o=e.z1,l=u[t],f=u[a],h=o[n],p=o[s],c=e.z2[i];exports.DEBUG&&console.log(\"ISECT[], \",t,a,n,s,i);var d=l.x,x=l.y,m=f.x,y=f.y,C=h.x,S=h.y,R=p.x,O=p.y,D=(d-m)*(S-O)-(x-y)*(C-R),L=d*y-x*m,F=C*O-S*R;c.x=(L*(C-R)-F*(d-m))/D,c.y=(L*(S-O)-F*(x-y))/D}function hs(e){e.rp0=e.stack.pop(),exports.DEBUG&&console.log(e.step,\"SRP0[]\",e.rp0)}function cs(e){e.rp1=e.stack.pop(),exports.DEBUG&&console.log(e.step,\"SRP1[]\",e.rp1)}function vs(e){e.rp2=e.stack.pop(),exports.DEBUG&&console.log(e.step,\"SRP2[]\",e.rp2)}function ds(e){var r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"SZP0[]\",r),e.zp0=r,r){case 0:e.tZone||$e(e),e.z0=e.tZone;break;case 1:e.z0=e.gZone;break;default:throw new Error(\"Invalid zone pointer\")}}function gs(e){var r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"SZP1[]\",r),e.zp1=r,r){case 0:e.tZone||$e(e),e.z1=e.tZone;break;case 1:e.z1=e.gZone;break;default:throw new Error(\"Invalid zone pointer\")}}function ms(e){var r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"SZP2[]\",r),e.zp2=r,r){case 0:e.tZone||$e(e),e.z2=e.tZone;break;case 1:e.z2=e.gZone;break;default:throw new Error(\"Invalid zone pointer\")}}function ys(e){var r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"SZPS[]\",r),e.zp0=e.zp1=e.zp2=r,r){case 0:e.tZone||$e(e),e.z0=e.z1=e.z2=e.tZone;break;case 1:e.z0=e.z1=e.z2=e.gZone;break;default:throw new Error(\"Invalid zone pointer\")}}function xs(e){e.loop=e.stack.pop(),exports.DEBUG&&console.log(e.step,\"SLOOP[]\",e.loop)}function bs(e){exports.DEBUG&&console.log(e.step,\"RTG[]\"),e.round=oa}function Ss(e){exports.DEBUG&&console.log(e.step,\"RTHG[]\"),e.round=ts}function Ts(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SMD[]\",r),e.minDis=r/64}function ks(e){exports.DEBUG&&console.log(e.step,\"ELSE[]\"),la(e,!1)}function Fs(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"JMPR[]\",r),e.ip+=r-1}function Us(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SCVTCI[]\",r),e.cvCutIn=r/64}function Cs(e){var r=e.stack;exports.DEBUG&&console.log(e.step,\"DUP[]\"),r.push(r[r.length-1])}function or(e){exports.DEBUG&&console.log(e.step,\"POP[]\"),e.stack.pop()}function Es(e){exports.DEBUG&&console.log(e.step,\"CLEAR[]\"),e.stack.length=0}function Os(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"SWAP[]\"),r.push(t),r.push(a)}function Ls(e){var r=e.stack;exports.DEBUG&&console.log(e.step,\"DEPTH[]\"),r.push(r.length)}function Rs(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"LOOPCALL[]\",t,a);var n=e.ip,s=e.prog;e.prog=e.funcs[t];for(var i=0;i<a;i++)Se(e),exports.DEBUG&&console.log(++e.step,i+1<a?\"next loopcall\":\"done loopcall\",i);e.ip=n,e.prog=s}function ws(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"CALL[]\",r);var t=e.ip,a=e.prog;e.prog=e.funcs[r],Se(e),e.ip=t,e.prog=a,exports.DEBUG&&console.log(++e.step,\"returning from\",r)}function Ds(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"CINDEX[]\",t),r.push(r[r.length-t])}function As(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"MINDEX[]\",t),r.push(r.splice(r.length-t,1)[0])}function Bs(e){if(e.env!==\"fpgm\")throw new Error(\"FDEF not allowed here\");var r=e.stack,t=e.prog,a=e.ip,n=r.pop(),s=a;for(exports.DEBUG&&console.log(e.step,\"FDEF[]\",n);t[++a]!==45;);e.ip=a,e.funcs[n]=t.slice(s+1,a)}function st(e,r){var t=r.stack.pop(),a=r.z0[t],n=r.fv,s=r.pv;exports.DEBUG&&console.log(r.step,\"MDAP[\"+e+\"]\",t);var i=s.distance(a,De);e&&(i=r.round(i)),n.setRelative(a,De,i,s),n.touch(a),r.rp0=r.rp1=t}function it(e,r){var t=r.z2,a=t.length-2,n,s,i;exports.DEBUG&&console.log(r.step,\"IUP[\"+e.axis+\"]\");for(var u=0;u<a;u++)n=t[u],!e.touched(n)&&(s=n.prevTouched(e),s!==n&&(i=n.nextTouched(e),s===i&&e.setRelative(n,n,e.distance(s,s,!1,!0),e,!0),e.interpolate(n,s,i,e)))}function ot(e,r){for(var t=r.stack,a=e?r.rp1:r.rp2,n=(e?r.z0:r.z1)[a],s=r.fv,i=r.pv,u=r.loop,o=r.z2;u--;){var l=t.pop(),f=o[l],h=i.distance(n,n,!1,!0);s.setRelative(f,f,h,i),s.touch(f),exports.DEBUG&&console.log(r.step,(r.loop>1?\"loop \"+(r.loop-u)+\": \":\"\")+\"SHP[\"+(e?\"rp1\":\"rp2\")+\"]\",l)}r.loop=1}function ut(e,r){var t=r.stack,a=e?r.rp1:r.rp2,n=(e?r.z0:r.z1)[a],s=r.fv,i=r.pv,u=t.pop(),o=r.z2[r.contours[u]],l=o;exports.DEBUG&&console.log(r.step,\"SHC[\"+e+\"]\",u);var f=i.distance(n,n,!1,!0);do l!==n&&s.setRelative(l,l,f,i),l=l.nextPointOnContour;while(l!==o)}function lt(e,r){var t=r.stack,a=e?r.rp1:r.rp2,n=(e?r.z0:r.z1)[a],s=r.fv,i=r.pv,u=t.pop();exports.DEBUG&&console.log(r.step,\"SHZ[\"+e+\"]\",u);var o;switch(u){case 0:o=r.tZone;break;case 1:o=r.gZone;break;default:throw new Error(\"Invalid zone\")}for(var l,f=i.distance(n,n,!1,!0),h=o.length-2,p=0;p<h;p++)l=o[p],s.setRelative(l,l,f,i)}function Is(e){for(var r=e.stack,t=e.loop,a=e.fv,n=r.pop()/64,s=e.z2;t--;){var i=r.pop(),u=s[i];exports.DEBUG&&console.log(e.step,(e.loop>1?\"loop \"+(e.loop-t)+\": \":\"\")+\"SHPIX[]\",i,n),a.setRelative(u,u,n),a.touch(u)}e.loop=1}function Ms(e){for(var r=e.stack,t=e.rp1,a=e.rp2,n=e.loop,s=e.z0[t],i=e.z1[a],u=e.fv,o=e.dpv,l=e.z2;n--;){var f=r.pop(),h=l[f];exports.DEBUG&&console.log(e.step,(e.loop>1?\"loop \"+(e.loop-n)+\": \":\"\")+\"IP[]\",f,t,\"<->\",a),u.interpolate(h,s,i,o),u.touch(h)}e.loop=1}function ft(e,r){var t=r.stack,a=t.pop()/64,n=t.pop(),s=r.z1[n],i=r.z0[r.rp0],u=r.fv,o=r.pv;u.setRelative(s,i,a,o),u.touch(s),exports.DEBUG&&console.log(r.step,\"MSIRP[\"+e+\"]\",a,n),r.rp1=r.rp0,r.rp2=n,e&&(r.rp0=n)}function Ps(e){for(var r=e.stack,t=e.rp0,a=e.z0[t],n=e.loop,s=e.fv,i=e.pv,u=e.z1;n--;){var o=r.pop(),l=u[o];exports.DEBUG&&console.log(e.step,(e.loop>1?\"loop \"+(e.loop-n)+\": \":\"\")+\"ALIGNRP[]\",o),s.setRelative(l,a,0,i),s.touch(l)}e.loop=1}function Gs(e){exports.DEBUG&&console.log(e.step,\"RTDG[]\"),e.round=rs}function pt(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z0[n],i=r.fv,u=r.pv,o=r.cvt[a];exports.DEBUG&&console.log(r.step,\"MIAP[\"+e+\"]\",a,\"(\",o,\")\",n);var l=u.distance(s,De);e&&(Math.abs(l-o)<r.cvCutIn&&(l=o),l=r.round(l)),i.setRelative(s,De,l,u),r.zp0===0&&(s.xo=s.x,s.yo=s.y),i.touch(s),r.rp0=r.rp1=n}function Ns(e){var r=e.prog,t=e.ip,a=e.stack,n=r[++t];exports.DEBUG&&console.log(e.step,\"NPUSHB[]\",n);for(var s=0;s<n;s++)a.push(r[++t]);e.ip=t}function Hs(e){var r=e.ip,t=e.prog,a=e.stack,n=t[++r];exports.DEBUG&&console.log(e.step,\"NPUSHW[]\",n);for(var s=0;s<n;s++){var i=t[++r]<<8|t[++r];i&32768&&(i=-((i^65535)+1)),a.push(i)}e.ip=r}function zs(e){var r=e.stack,t=e.store;t||(t=e.store=[]);var a=r.pop(),n=r.pop();exports.DEBUG&&console.log(e.step,\"WS\",a,n),t[n]=a}function Ws(e){var r=e.stack,t=e.store,a=r.pop();exports.DEBUG&&console.log(e.step,\"RS\",a);var n=t&&t[a]||0;r.push(n)}function _s(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"WCVTP\",t,a),e.cvt[a]=t/64}function Vs(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"RCVT\",t),r.push(e.cvt[t]*64)}function ht(e,r){var t=r.stack,a=t.pop(),n=r.z2[a];exports.DEBUG&&console.log(r.step,\"GC[\"+e+\"]\",a),t.push(r.dpv.distance(n,De,e,!1)*64)}function ct(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z1[a],i=r.z0[n],u=r.dpv.distance(i,s,e,e);exports.DEBUG&&console.log(r.step,\"MD[\"+e+\"]\",a,n,\"->\",u),r.stack.push(Math.round(u*64))}function qs(e){exports.DEBUG&&console.log(e.step,\"MPPEM[]\"),e.stack.push(e.ppem)}function Xs(e){exports.DEBUG&&console.log(e.step,\"FLIPON[]\"),e.autoFlip=!0}function Ys(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"LT[]\",t,a),r.push(a<t?1:0)}function Zs(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"LTEQ[]\",t,a),r.push(a<=t?1:0)}function Qs(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"GT[]\",t,a),r.push(a>t?1:0)}function Ks(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"GTEQ[]\",t,a),r.push(a>=t?1:0)}function Js(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"EQ[]\",t,a),r.push(t===a?1:0)}function js(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"NEQ[]\",t,a),r.push(t!==a?1:0)}function $s(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"ODD[]\",t),r.push(Math.trunc(t)%2?1:0)}function ei(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"EVEN[]\",t),r.push(Math.trunc(t)%2?0:1)}function ri(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"IF[]\",r),r||(la(e,!0),exports.DEBUG&&console.log(e.step,\"EIF[]\"))}function ti(e){exports.DEBUG&&console.log(e.step,\"EIF[]\")}function ai(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"AND[]\",t,a),r.push(t&&a?1:0)}function ni(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"OR[]\",t,a),r.push(t||a?1:0)}function si(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"NOT[]\",t),r.push(t?0:1)}function ur(e,r){var t=r.stack,a=t.pop(),n=r.fv,s=r.pv,i=r.ppem,u=r.deltaBase+(e-1)*16,o=r.deltaShift,l=r.z0;exports.DEBUG&&console.log(r.step,\"DELTAP[\"+e+\"]\",a,t);for(var f=0;f<a;f++){var h=t.pop(),p=t.pop(),c=u+((p&240)>>4);if(c===i){var d=(p&15)-8;d>=0&&d++,exports.DEBUG&&console.log(r.step,\"DELTAPFIX\",h,\"by\",d*o);var x=l[h];n.setRelative(x,x,d*o,s)}}}function ii(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"SDB[]\",t),e.deltaBase=t}function oi(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"SDS[]\",t),e.deltaShift=Math.pow(.5,t)}function ui(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"ADD[]\",t,a),r.push(a+t)}function li(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"SUB[]\",t,a),r.push(a-t)}function fi(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"DIV[]\",t,a),r.push(a*64/t)}function pi(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"MUL[]\",t,a),r.push(a*t/64)}function hi(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"ABS[]\",t),r.push(Math.abs(t))}function ci(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"NEG[]\",t),r.push(-t)}function vi(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"FLOOR[]\",t),r.push(Math.floor(t/64)*64)}function di(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"CEILING[]\",t),r.push(Math.ceil(t/64)*64)}function Ve(e,r){var t=r.stack,a=t.pop();exports.DEBUG&&console.log(r.step,\"ROUND[]\"),t.push(r.round(a/64)*64)}function gi(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"WCVTF[]\",t,a),e.cvt[a]=t*e.ppem/e.font.unitsPerEm}function lr(e,r){var t=r.stack,a=t.pop(),n=r.ppem,s=r.deltaBase+(e-1)*16,i=r.deltaShift;exports.DEBUG&&console.log(r.step,\"DELTAC[\"+e+\"]\",a,t);for(var u=0;u<a;u++){var o=t.pop(),l=t.pop(),f=s+((l&240)>>4);if(f===n){var h=(l&15)-8;h>=0&&h++;var p=h*i;exports.DEBUG&&console.log(r.step,\"DELTACFIX\",o,\"by\",p),r.cvt[o]+=p}}}function mi(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SROUND[]\",r),e.round=ua;var t;switch(r&192){case 0:t=.5;break;case 64:t=1;break;case 128:t=2;break;default:throw new Error(\"invalid SROUND value\")}switch(e.srPeriod=t,r&48){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw new Error(\"invalid SROUND value\")}r&=15,r===0?e.srThreshold=0:e.srThreshold=(r/8-.5)*t}function yi(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"S45ROUND[]\",r),e.round=ua;var t;switch(r&192){case 0:t=Math.sqrt(2)/2;break;case 64:t=Math.sqrt(2);break;case 128:t=2*Math.sqrt(2);break;default:throw new Error(\"invalid S45ROUND value\")}switch(e.srPeriod=t,r&48){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw new Error(\"invalid S45ROUND value\")}r&=15,r===0?e.srThreshold=0:e.srThreshold=(r/8-.5)*t}function xi(e){exports.DEBUG&&console.log(e.step,\"ROFF[]\"),e.round=es}function bi(e){exports.DEBUG&&console.log(e.step,\"RUTG[]\"),e.round=as}function Si(e){exports.DEBUG&&console.log(e.step,\"RDTG[]\"),e.round=ns}function Ti(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SCANCTRL[]\",r)}function vt(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z2[a],i=r.z1[n];exports.DEBUG&&console.log(r.step,\"SDPVTL[\"+e+\"]\",a,n);var u,o;e?(u=s.y-i.y,o=i.x-s.x):(u=i.x-s.x,o=i.y-s.y),r.dpv=Me(u,o)}function ki(e){var r=e.stack,t=r.pop(),a=0;exports.DEBUG&&console.log(e.step,\"GETINFO[]\",t),t&1&&(a=35),t&32&&(a|=4096),r.push(a)}function Fi(e){var r=e.stack,t=r.pop(),a=r.pop(),n=r.pop();exports.DEBUG&&console.log(e.step,\"ROLL[]\"),r.push(a),r.push(t),r.push(n)}function Ui(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"MAX[]\",t,a),r.push(Math.max(a,t))}function Ci(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"MIN[]\",t,a),r.push(Math.min(a,t))}function Ei(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SCANTYPE[]\",r)}function Oi(e){var r=e.stack.pop(),t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"INSTCTRL[]\",r,t),r){case 1:e.inhibitGridFit=!!t;return;case 2:e.ignoreCvt=!!t;return;default:throw new Error(\"invalid INSTCTRL[] selector\")}}function he(e,r){var t=r.stack,a=r.prog,n=r.ip;exports.DEBUG&&console.log(r.step,\"PUSHB[\"+e+\"]\");for(var s=0;s<e;s++)t.push(a[++n]);r.ip=n}function ce(e,r){var t=r.ip,a=r.prog,n=r.stack;exports.DEBUG&&console.log(r.ip,\"PUSHW[\"+e+\"]\");for(var s=0;s<e;s++){var i=a[++t]<<8|a[++t];i&32768&&(i=-((i^65535)+1)),n.push(i)}r.ip=t}function T(e,r,t,a,n,s){var i=s.stack,u=e&&i.pop(),o=i.pop(),l=s.rp0,f=s.z0[l],h=s.z1[o],p=s.minDis,c=s.fv,d=s.dpv,x,m,y,C;m=x=d.distance(h,f,!0,!0),y=m>=0?1:-1,m=Math.abs(m),e&&(C=s.cvt[u],a&&Math.abs(m-C)<s.cvCutIn&&(m=C)),t&&m<p&&(m=p),a&&(m=s.round(m)),c.setRelative(h,f,y*m,d),c.touch(h),exports.DEBUG&&console.log(s.step,(e?\"MIRP[\":\"MDRP[\")+(r?\"M\":\"m\")+(t?\">\":\"_\")+(a?\"R\":\"_\")+(n===0?\"Gr\":n===1?\"Bl\":n===2?\"Wh\":\"\")+\"]\",e?u+\"(\"+s.cvt[u]+\",\"+C+\")\":\"\",o,\"(d =\",x,\"->\",y*m,\")\"),s.rp1=s.rp0,s.rp2=o,r&&(s.rp0=o)}na=[et.bind(void 0,le),et.bind(void 0,oe),rt.bind(void 0,le),rt.bind(void 0,oe),tt.bind(void 0,le),tt.bind(void 0,oe),at.bind(void 0,0),at.bind(void 0,1),nt.bind(void 0,0),nt.bind(void 0,1),is,os,us,ls,fs,ps,hs,cs,vs,ds,gs,ms,ys,xs,bs,Ss,Ts,ks,Fs,Us,void 0,void 0,Cs,or,Es,Os,Ls,Ds,As,void 0,void 0,void 0,Rs,ws,Bs,void 0,st.bind(void 0,0),st.bind(void 0,1),it.bind(void 0,le),it.bind(void 0,oe),ot.bind(void 0,0),ot.bind(void 0,1),ut.bind(void 0,0),ut.bind(void 0,1),lt.bind(void 0,0),lt.bind(void 0,1),Is,Ms,ft.bind(void 0,0),ft.bind(void 0,1),Ps,Gs,pt.bind(void 0,0),pt.bind(void 0,1),Ns,Hs,zs,Ws,_s,Vs,ht.bind(void 0,0),ht.bind(void 0,1),void 0,ct.bind(void 0,0),ct.bind(void 0,1),qs,void 0,Xs,void 0,void 0,Ys,Zs,Qs,Ks,Js,js,$s,ei,ri,ti,ai,ni,si,ur.bind(void 0,1),ii,oi,ui,li,fi,pi,hi,ci,vi,di,Ve.bind(void 0,0),Ve.bind(void 0,1),Ve.bind(void 0,2),Ve.bind(void 0,3),void 0,void 0,void 0,void 0,gi,ur.bind(void 0,2),ur.bind(void 0,3),lr.bind(void 0,1),lr.bind(void 0,2),lr.bind(void 0,3),mi,yi,void 0,void 0,xi,void 0,bi,Si,or,or,void 0,void 0,void 0,void 0,void 0,Ti,vt.bind(void 0,0),vt.bind(void 0,1),ki,void 0,Fi,Ui,Ci,Ei,Oi,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,he.bind(void 0,1),he.bind(void 0,2),he.bind(void 0,3),he.bind(void 0,4),he.bind(void 0,5),he.bind(void 0,6),he.bind(void 0,7),he.bind(void 0,8),ce.bind(void 0,1),ce.bind(void 0,2),ce.bind(void 0,3),ce.bind(void 0,4),ce.bind(void 0,5),ce.bind(void 0,6),ce.bind(void 0,7),ce.bind(void 0,8),T.bind(void 0,0,0,0,0,0),T.bind(void 0,0,0,0,0,1),T.bind(void 0,0,0,0,0,2),T.bind(void 0,0,0,0,0,3),T.bind(void 0,0,0,0,1,0),T.bind(void 0,0,0,0,1,1),T.bind(void 0,0,0,0,1,2),T.bind(void 0,0,0,0,1,3),T.bind(void 0,0,0,1,0,0),T.bind(void 0,0,0,1,0,1),T.bind(void 0,0,0,1,0,2),T.bind(void 0,0,0,1,0,3),T.bind(void 0,0,0,1,1,0),T.bind(void 0,0,0,1,1,1),T.bind(void 0,0,0,1,1,2),T.bind(void 0,0,0,1,1,3),T.bind(void 0,0,1,0,0,0),T.bind(void 0,0,1,0,0,1),T.bind(void 0,0,1,0,0,2),T.bind(void 0,0,1,0,0,3),T.bind(void 0,0,1,0,1,0),T.bind(void 0,0,1,0,1,1),T.bind(void 0,0,1,0,1,2),T.bind(void 0,0,1,0,1,3),T.bind(void 0,0,1,1,0,0),T.bind(void 0,0,1,1,0,1),T.bind(void 0,0,1,1,0,2),T.bind(void 0,0,1,1,0,3),T.bind(void 0,0,1,1,1,0),T.bind(void 0,0,1,1,1,1),T.bind(void 0,0,1,1,1,2),T.bind(void 0,0,1,1,1,3),T.bind(void 0,1,0,0,0,0),T.bind(void 0,1,0,0,0,1),T.bind(void 0,1,0,0,0,2),T.bind(void 0,1,0,0,0,3),T.bind(void 0,1,0,0,1,0),T.bind(void 0,1,0,0,1,1),T.bind(void 0,1,0,0,1,2),T.bind(void 0,1,0,0,1,3),T.bind(void 0,1,0,1,0,0),T.bind(void 0,1,0,1,0,1),T.bind(void 0,1,0,1,0,2),T.bind(void 0,1,0,1,0,3),T.bind(void 0,1,0,1,1,0),T.bind(void 0,1,0,1,1,1),T.bind(void 0,1,0,1,1,2),T.bind(void 0,1,0,1,1,3),T.bind(void 0,1,1,0,0,0),T.bind(void 0,1,1,0,0,1),T.bind(void 0,1,1,0,0,2),T.bind(void 0,1,1,0,0,3),T.bind(void 0,1,1,0,1,0),T.bind(void 0,1,1,0,1,1),T.bind(void 0,1,1,0,1,2),T.bind(void 0,1,1,0,1,3),T.bind(void 0,1,1,1,0,0),T.bind(void 0,1,1,1,0,1),T.bind(void 0,1,1,1,0,2),T.bind(void 0,1,1,1,0,3),T.bind(void 0,1,1,1,1,0),T.bind(void 0,1,1,1,1,1),T.bind(void 0,1,1,1,1,2),T.bind(void 0,1,1,1,1,3)];function Ce(e){this.char=e,this.state={},this.activeState=null}function Lr(e,r,t){this.contextName=t,this.startIndex=e,this.endOffset=r}function Li(e,r,t){this.contextName=e,this.openRange=null,this.ranges=[],this.checkStart=r,this.checkEnd=t}function re(e,r){this.context=e,this.index=r,this.length=e.length,this.current=e[r],this.backtrack=e.slice(0,r),this.lookahead=e.slice(r+1)}function er(e){this.eventId=e,this.subscribers=[]}function Ri(e){var r=this,t=[\"start\",\"end\",\"next\",\"newToken\",\"contextStart\",\"contextEnd\",\"insertToken\",\"removeToken\",\"removeRange\",\"replaceToken\",\"replaceRange\",\"composeRUD\",\"updateContextsRanges\"];t.forEach(function(n){Object.defineProperty(r.events,n,{value:new er(n)})}),e&&t.forEach(function(n){var s=e[n];typeof s==\"function\"&&r.events[n].subscribe(s)});var a=[\"insertToken\",\"removeToken\",\"removeRange\",\"replaceToken\",\"replaceRange\",\"composeRUD\"];a.forEach(function(n){r.events[n].subscribe(r.updateContextsRanges)})}function B(e){this.tokens=[],this.registeredContexts={},this.contextCheckers=[],this.events={},this.registeredModifiers=[],Ri.call(this,e)}Ce.prototype.setState=function(e,r){return this.state[e]=r,this.activeState={key:e,value:this.state[e]},this.activeState};Ce.prototype.getState=function(e){return this.state[e]||null};B.prototype.inboundIndex=function(e){return e>=0&&e<this.tokens.length};B.prototype.composeRUD=function(e){var r=this,t=!0,a=e.map(function(s){return r[s[0]].apply(r,s.slice(1).concat(t))}),n=function(s){return typeof s==\"object\"&&s.hasOwnProperty(\"FAIL\")};if(a.every(n))return{FAIL:\"composeRUD: one or more operations hasn't completed successfully\",report:a.filter(n)};this.dispatch(\"composeRUD\",[a.filter(function(s){return!n(s)})])};B.prototype.replaceRange=function(e,r,t,a){r=r!==null?r:this.tokens.length;var n=t.every(function(i){return i instanceof Ce});if(!isNaN(e)&&this.inboundIndex(e)&&n){var s=this.tokens.splice.apply(this.tokens,[e,r].concat(t));return a||this.dispatch(\"replaceToken\",[e,r,t]),[s,t]}else return{FAIL:\"replaceRange: invalid tokens or startIndex.\"}};B.prototype.replaceToken=function(e,r,t){if(!isNaN(e)&&this.inboundIndex(e)&&r instanceof Ce){var a=this.tokens.splice(e,1,r);return t||this.dispatch(\"replaceToken\",[e,r]),[a[0],r]}else return{FAIL:\"replaceToken: invalid token or index.\"}};B.prototype.removeRange=function(e,r,t){r=isNaN(r)?this.tokens.length:r;var a=this.tokens.splice(e,r);return t||this.dispatch(\"removeRange\",[a,e,r]),a};B.prototype.removeToken=function(e,r){if(!isNaN(e)&&this.inboundIndex(e)){var t=this.tokens.splice(e,1);return r||this.dispatch(\"removeToken\",[t,e]),t}else return{FAIL:\"removeToken: invalid token index.\"}};B.prototype.insertToken=function(e,r,t){var a=e.every(function(n){return n instanceof Ce});return a?(this.tokens.splice.apply(this.tokens,[r,0].concat(e)),t||this.dispatch(\"insertToken\",[e,r]),e):{FAIL:\"insertToken: invalid token(s).\"}};B.prototype.registerModifier=function(e,r,t){this.events.newToken.subscribe(function(a,n){var s=[a,n],i=r===null||r.apply(this,s)===!0,u=[a,n];if(i){var o=t.apply(this,u);a.setState(e,o)}}),this.registeredModifiers.push(e)};er.prototype.subscribe=function(e){return typeof e==\"function\"?this.subscribers.push(e)-1:{FAIL:\"invalid '\"+this.eventId+\"' event handler\"}};er.prototype.unsubscribe=function(e){this.subscribers.splice(e,1)};re.prototype.setCurrentIndex=function(e){this.index=e,this.current=this.context[e],this.backtrack=this.context.slice(0,e),this.lookahead=this.context.slice(e+1)};re.prototype.get=function(e){switch(!0){case e===0:return this.current;case(e<0&&Math.abs(e)<=this.backtrack.length):return this.backtrack.slice(e)[0];case(e>0&&e<=this.lookahead.length):return this.lookahead[e-1];default:return null}};B.prototype.rangeToText=function(e){if(e instanceof Lr)return this.getRangeTokens(e).map(function(r){return r.char}).join(\"\")};B.prototype.getText=function(){return this.tokens.map(function(e){return e.char}).join(\"\")};B.prototype.getContext=function(e){var r=this.registeredContexts[e];return r||null};B.prototype.on=function(e,r){var t=this.events[e];return t?t.subscribe(r):null};B.prototype.dispatch=function(e,r){var t=this,a=this.events[e];a instanceof er&&a.subscribers.forEach(function(n){n.apply(t,r||[])})};B.prototype.registerContextChecker=function(e,r,t){if(this.getContext(e))return{FAIL:\"context name '\"+e+\"' is already registered.\"};if(typeof r!=\"function\")return{FAIL:\"missing context start check.\"};if(typeof t!=\"function\")return{FAIL:\"missing context end check.\"};var a=new Li(e,r,t);return this.registeredContexts[e]=a,this.contextCheckers.push(a),a};B.prototype.getRangeTokens=function(e){var r=e.startIndex+e.endOffset;return[].concat(this.tokens.slice(e.startIndex,r))};B.prototype.getContextRanges=function(e){var r=this.getContext(e);return r?r.ranges:{FAIL:\"context checker '\"+e+\"' is not registered.\"}};B.prototype.resetContextsRanges=function(){var e=this.registeredContexts;for(var r in e)if(e.hasOwnProperty(r)){var t=e[r];t.ranges=[]}};B.prototype.updateContextsRanges=function(){this.resetContextsRanges();for(var e=this.tokens.map(function(a){return a.char}),r=0;r<e.length;r++){var t=new re(e,r);this.runContextCheck(t)}this.dispatch(\"updateContextsRanges\",[this.registeredContexts])};B.prototype.setEndOffset=function(e,r){var t=this.getContext(r).openRange.startIndex,a=new Lr(t,e,r),n=this.getContext(r).ranges;return a.rangeId=r+\".\"+n.length,n.push(a),this.getContext(r).openRange=null,a};B.prototype.runContextCheck=function(e){var r=this,t=e.index;this.contextCheckers.forEach(function(a){var n=a.contextName,s=r.getContext(n).openRange;if(!s&&a.checkStart(e)&&(s=new Lr(t,null,n),r.getContext(n).openRange=s,r.dispatch(\"contextStart\",[n,t])),!!s&&a.checkEnd(e)){var i=t-s.startIndex+1,u=r.setEndOffset(i,n);r.dispatch(\"contextEnd\",[n,u])}})};B.prototype.tokenize=function(e){this.tokens=[],this.resetContextsRanges();var r=Array.from(e);this.dispatch(\"start\");for(var t=0;t<r.length;t++){var a=r[t],n=new re(r,t);this.dispatch(\"next\",[n]),this.runContextCheck(n);var s=new Ce(a);this.tokens.push(s),this.dispatch(\"newToken\",[s,n])}return this.dispatch(\"end\",[this.tokens]),this.tokens};function ge(e){return/[\\u0600-\\u065F\\u066A-\\u06D2\\u06FA-\\u06FF]/.test(e)}function fa(e){return/[\\u0630\\u0690\\u0621\\u0631\\u0661\\u0671\\u0622\\u0632\\u0672\\u0692\\u06C2\\u0623\\u0673\\u0693\\u06C3\\u0624\\u0694\\u06C4\\u0625\\u0675\\u0695\\u06C5\\u06E5\\u0676\\u0696\\u06C6\\u0627\\u0677\\u0697\\u06C7\\u0648\\u0688\\u0698\\u06C8\\u0689\\u0699\\u06C9\\u068A\\u06CA\\u066B\\u068B\\u06CB\\u068C\\u068D\\u06CD\\u06FD\\u068E\\u06EE\\u06FE\\u062F\\u068F\\u06CF\\u06EF]/.test(e)}function me(e){return/[\\u0600-\\u0605\\u060C-\\u060E\\u0610-\\u061B\\u061E\\u064B-\\u065F\\u0670\\u06D6-\\u06DC\\u06DF-\\u06E4\\u06E7\\u06E8\\u06EA-\\u06ED]/.test(e)}function Xe(e){return/[A-z]/.test(e)}function wi(e){return/\\s/.test(e)}function J(e){this.font=e,this.features={}}function be(e){this.id=e.id,this.tag=e.tag,this.substitution=e.substitution}function Pe(e,r){if(!e)return-1;switch(r.format){case 1:return r.glyphs.indexOf(e);case 2:for(var t=r.ranges,a=0;a<t.length;a++){var n=t[a];if(e>=n.start&&e<=n.end){var s=e-n.start;return n.index+s}}break;default:return-1}return-1}function Di(e,r){var t=Pe(e,r.coverage);return t===-1?null:e+r.deltaGlyphId}function Ai(e,r){var t=Pe(e,r.coverage);return t===-1?null:r.substitute[t]}function fr(e,r){for(var t=[],a=0;a<e.length;a++){var n=e[a],s=r.current;s=Array.isArray(s)?s[0]:s;var i=Pe(s,n);i!==-1&&t.push(i)}return t.length!==e.length?-1:t}function Bi(e,r){var t=r.inputCoverage.length+r.lookaheadCoverage.length+r.backtrackCoverage.length;if(e.context.length<t)return[];var a=fr(r.inputCoverage,e);if(a===-1)return[];var n=r.inputCoverage.length-1;if(e.lookahead.length<r.lookaheadCoverage.length)return[];for(var s=e.lookahead.slice(n);s.length&&me(s[0].char);)s.shift();var i=new re(s,0),u=fr(r.lookaheadCoverage,i),o=[].concat(e.backtrack);for(o.reverse();o.length&&me(o[0].char);)o.shift();if(o.length<r.backtrackCoverage.length)return[];var l=new re(o,0),f=fr(r.backtrackCoverage,l),h=a.length===r.inputCoverage.length&&u.length===r.lookaheadCoverage.length&&f.length===r.backtrackCoverage.length,p=[];if(h)for(var c=0;c<r.lookupRecords.length;c++)for(var d=r.lookupRecords[c],x=d.lookupListIndex,m=this.getLookupByIndex(x),y=0;y<m.subtables.length;y++){var C=m.subtables[y],S=this.getLookupMethod(m,C),R=this.getSubstitutionType(m,C);if(R===\"12\")for(var O=0;O<a.length;O++){var D=e.get(O),L=S(D);L&&p.push(L)}}return p}function Ii(e,r){var t=e.current,a=Pe(t,r.coverage);if(a===-1)return null;for(var n,s=r.ligatureSets[a],i=0;i<s.length;i++){n=s[i];for(var u=0;u<n.components.length;u++){var o=e.lookahead[u],l=n.components[u];if(o!==l)break;if(u===n.components.length-1)return n}}return null}function Mi(e,r){var t=Pe(e,r.coverage);return t===-1?null:r.sequences[t]}J.prototype.getDefaultScriptFeaturesIndexes=function(){for(var e=this.font.tables.gsub.scripts,r=0;r<e.length;r++){var t=e[r];if(t.tag===\"DFLT\")return t.script.defaultLangSys.featureIndexes}return[]};J.prototype.getScriptFeaturesIndexes=function(e){var r=this.font.tables;if(!r.gsub)return[];if(!e)return this.getDefaultScriptFeaturesIndexes();for(var t=this.font.tables.gsub.scripts,a=0;a<t.length;a++){var n=t[a];if(n.tag===e&&n.script.defaultLangSys)return n.script.defaultLangSys.featureIndexes;var s=n.langSysRecords;if(s)for(var i=0;i<s.length;i++){var u=s[i];if(u.tag===e){var o=u.langSys;return o.featureIndexes}}}return this.getDefaultScriptFeaturesIndexes()};J.prototype.mapTagsToFeatures=function(e,r){for(var t={},a=0;a<e.length;a++){var n=e[a].tag,s=e[a].feature;t[n]=s}this.features[r].tags=t};J.prototype.getScriptFeatures=function(e){var r=this.features[e];if(this.features.hasOwnProperty(e))return r;var t=this.getScriptFeaturesIndexes(e);if(!t)return null;var a=this.font.tables.gsub;return r=t.map(function(n){return a.features[n]}),this.features[e]=r,this.mapTagsToFeatures(r,e),r};J.prototype.getSubstitutionType=function(e,r){var t=e.lookupType.toString(),a=r.substFormat.toString();return t+a};J.prototype.getLookupMethod=function(e,r){var t=this,a=this.getSubstitutionType(e,r);switch(a){case\"11\":return function(n){return Di.apply(t,[n,r])};case\"12\":return function(n){return Ai.apply(t,[n,r])};case\"63\":return function(n){return Bi.apply(t,[n,r])};case\"41\":return function(n){return Ii.apply(t,[n,r])};case\"21\":return function(n){return Mi.apply(t,[n,r])};default:throw new Error(\"lookupType: \"+e.lookupType+\" - substFormat: \"+r.substFormat+\" is not yet supported\")}};J.prototype.lookupFeature=function(e){var r=e.contextParams,t=r.index,a=this.getFeature({tag:e.tag,script:e.script});if(!a)return new Error(\"font '\"+this.font.names.fullName.en+\"' doesn't support feature '\"+e.tag+\"' for script '\"+e.script+\"'.\");for(var n=this.getFeatureLookups(a),s=[].concat(r.context),i=0;i<n.length;i++)for(var u=n[i],o=this.getLookupSubtables(u),l=0;l<o.length;l++){var f=o[l],h=this.getSubstitutionType(u,f),p=this.getLookupMethod(u,f),c=void 0;switch(h){case\"11\":c=p(r.current),c&&s.splice(t,1,new be({id:11,tag:e.tag,substitution:c}));break;case\"12\":c=p(r.current),c&&s.splice(t,1,new be({id:12,tag:e.tag,substitution:c}));break;case\"63\":c=p(r),Array.isArray(c)&&c.length&&s.splice(t,1,new be({id:63,tag:e.tag,substitution:c}));break;case\"41\":c=p(r),c&&s.splice(t,1,new be({id:41,tag:e.tag,substitution:c}));break;case\"21\":c=p(r.current),c&&s.splice(t,1,new be({id:21,tag:e.tag,substitution:c}));break}r=new re(s,t),!(Array.isArray(c)&&!c.length)&&(c=null)}return s.length?s:null};J.prototype.supports=function(e){if(!e.script)return!1;this.getScriptFeatures(e.script);var r=this.features.hasOwnProperty(e.script);if(!e.tag)return r;var t=this.features[e.script].some(function(a){return a.tag===e.tag});return r&&t};J.prototype.getLookupSubtables=function(e){return e.subtables||null};J.prototype.getLookupByIndex=function(e){var r=this.font.tables.gsub.lookups;return r[e]||null};J.prototype.getFeatureLookups=function(e){return e.lookupListIndexes.map(this.getLookupByIndex.bind(this))};J.prototype.getFeature=function(r){if(!this.font)return{FAIL:\"No font was found\"};this.features.hasOwnProperty(r.script)||this.getScriptFeatures(r.script);var t=this.features[r.script];return t?t.tags[r.tag]?this.features[r.script].tags[r.tag]:null:{FAIL:\"No feature for script \"+r.script}};function Pi(e){var r=e.current,t=e.get(-1);return t===null&&ge(r)||!ge(t)&&ge(r)}function Gi(e){var r=e.get(1);return r===null||!ge(r)}var Ni={startCheck:Pi,endCheck:Gi};function Hi(e){var r=e.current,t=e.get(-1);return(ge(r)||me(r))&&!ge(t)}function zi(e){var r=e.get(1);switch(!0){case r===null:return!0;case(!ge(r)&&!me(r)):var t=wi(r);if(!t)return!0;if(t){var a=!1;if(a=e.lookahead.some(function(n){return ge(n)||me(n)}),!a)return!0}break;default:return!1}}var Wi={startCheck:Hi,endCheck:zi};function _i(e,r,t){r[t].setState(e.tag,e.substitution)}function Vi(e,r,t){r[t].setState(e.tag,e.substitution)}function qi(e,r,t){e.substitution.forEach(function(a,n){var s=r[t+n];s.setState(e.tag,a)})}function Xi(e,r,t){var a=r[t];a.setState(e.tag,e.substitution.ligGlyph);for(var n=e.substitution.components.length,s=0;s<n;s++)a=r[t+s+1],a.setState(\"deleted\",!0)}var dt={11:_i,12:Vi,63:qi,41:Xi};function Rr(e,r,t){e instanceof be&&dt[e.id]&&dt[e.id](e,r,t)}function Yi(e){for(var r=[].concat(e.backtrack),t=r.length-1;t>=0;t--){var a=r[t],n=fa(a),s=me(a);if(!n&&!s)return!0;if(n)return!1}return!1}function Zi(e){if(fa(e.current))return!1;for(var r=0;r<e.lookahead.length;r++){var t=e.lookahead[r],a=me(t);if(!a)return!0}return!1}function Qi(e){var r=this,t=\"arab\",a=this.featuresTags[t],n=this.tokenizer.getRangeTokens(e);if(n.length!==1){var s=new re(n.map(function(u){return u.getState(\"glyphIndex\")}),0),i=new re(n.map(function(u){return u.char}),0);n.forEach(function(u,o){if(!me(u.char)){s.setCurrentIndex(o),i.setCurrentIndex(o);var l=0;Yi(i)&&(l|=1),Zi(i)&&(l|=2);var f;switch(l){case 1:f=\"fina\";break;case 2:f=\"init\";break;case 3:f=\"medi\";break}if(a.indexOf(f)!==-1){var h=r.query.lookupFeature({tag:f,script:t,contextParams:s});if(h instanceof Error)return console.info(h.message);h.forEach(function(p,c){p instanceof be&&(Rr(p,n,c),s.context[c]=p.substitution)})}}})}}function gt(e,r){var t=e.map(function(a){return a.activeState.value});return new re(t,r||0)}function Ki(e){var r=this,t=\"arab\",a=this.tokenizer.getRangeTokens(e),n=gt(a);n.context.forEach(function(s,i){n.setCurrentIndex(i);var u=r.query.lookupFeature({tag:\"rlig\",script:t,contextParams:n});u.length&&(u.forEach(function(o){return Rr(o,a,i)}),n=gt(a))})}function Ji(e){var r=e.current,t=e.get(-1);return t===null&&Xe(r)||!Xe(t)&&Xe(r)}function ji(e){var r=e.get(1);return r===null||!Xe(r)}var $i={startCheck:Ji,endCheck:ji};function mt(e,r){var t=e.map(function(a){return a.activeState.value});return new re(t,r||0)}function eo(e){var r=this,t=\"latn\",a=this.tokenizer.getRangeTokens(e),n=mt(a);n.context.forEach(function(s,i){n.setCurrentIndex(i);var u=r.query.lookupFeature({tag:\"liga\",script:t,contextParams:n});u.length&&(u.forEach(function(o){return Rr(o,a,i)}),n=mt(a))})}function ne(e){this.baseDir=e||\"ltr\",this.tokenizer=new B,this.featuresTags={}}ne.prototype.setText=function(e){this.text=e};ne.prototype.contextChecks={latinWordCheck:$i,arabicWordCheck:Ni,arabicSentenceCheck:Wi};function pr(e){var r=this.contextChecks[e+\"Check\"];return this.tokenizer.registerContextChecker(e,r.startCheck,r.endCheck)}function ro(){return pr.call(this,\"latinWord\"),pr.call(this,\"arabicWord\"),pr.call(this,\"arabicSentence\"),this.tokenizer.tokenize(this.text)}function to(){var e=this,r=this.tokenizer.getContextRanges(\"arabicSentence\");r.forEach(function(t){var a=e.tokenizer.getRangeTokens(t);e.tokenizer.replaceRange(t.startIndex,t.endOffset,a.reverse())})}ne.prototype.registerFeatures=function(e,r){var t=this,a=r.filter(function(n){return t.query.supports({script:e,tag:n})});this.featuresTags.hasOwnProperty(e)?this.featuresTags[e]=this.featuresTags[e].concat(a):this.featuresTags[e]=a};ne.prototype.applyFeatures=function(e,r){if(!e)throw new Error(\"No valid font was provided to apply features\");this.query||(this.query=new J(e));for(var t=0;t<r.length;t++){var a=r[t];!this.query.supports({script:a.script})||this.registerFeatures(a.script,a.tags)}};ne.prototype.registerModifier=function(e,r,t){this.tokenizer.registerModifier(e,r,t)};function wr(){if(this.tokenizer.registeredModifiers.indexOf(\"glyphIndex\")===-1)throw new Error(\"glyphIndex modifier is required to apply arabic presentation features.\")}function ao(){var e=this,r=\"arab\";if(!!this.featuresTags.hasOwnProperty(r)){wr.call(this);var t=this.tokenizer.getContextRanges(\"arabicWord\");t.forEach(function(a){Qi.call(e,a)})}}function no(){var e=this,r=\"arab\";if(!!this.featuresTags.hasOwnProperty(r)){var t=this.featuresTags[r];if(t.indexOf(\"rlig\")!==-1){wr.call(this);var a=this.tokenizer.getContextRanges(\"arabicWord\");a.forEach(function(n){Ki.call(e,n)})}}}function so(){var e=this,r=\"latn\";if(!!this.featuresTags.hasOwnProperty(r)){var t=this.featuresTags[r];if(t.indexOf(\"liga\")!==-1){wr.call(this);var a=this.tokenizer.getContextRanges(\"latinWord\");a.forEach(function(n){eo.call(e,n)})}}}ne.prototype.checkContextReady=function(e){return!!this.tokenizer.getContext(e)};ne.prototype.applyFeaturesToContexts=function(){this.checkContextReady(\"arabicWord\")&&(ao.call(this),no.call(this)),this.checkContextReady(\"latinWord\")&&so.call(this),this.checkContextReady(\"arabicSentence\")&&to.call(this)};ne.prototype.processText=function(e){(!this.text||this.text!==e)&&(this.setText(e),ro.call(this),this.applyFeaturesToContexts())};ne.prototype.getBidiText=function(e){return this.processText(e),this.tokenizer.getText()};ne.prototype.getTextGlyphs=function(e){this.processText(e);for(var r=[],t=0;t<this.tokenizer.tokens.length;t++){var a=this.tokenizer.tokens[t];if(!a.state.deleted){var n=a.activeState.value;r.push(Array.isArray(n)?n[0]:n)}}return r};function w(e){e=e||{},e.tables=e.tables||{},e.empty||(Ee(e.familyName,\"When creating a new Font object, familyName is required.\"),Ee(e.styleName,\"When creating a new Font object, styleName is required.\"),Ee(e.unitsPerEm,\"When creating a new Font object, unitsPerEm is required.\"),Ee(e.ascender,\"When creating a new Font object, ascender is required.\"),Ee(e.descender<=0,\"When creating a new Font object, negative descender value is required.\"),this.names={fontFamily:{en:e.familyName||\" \"},fontSubfamily:{en:e.styleName||\" \"},fullName:{en:e.fullName||e.familyName+\" \"+e.styleName},postScriptName:{en:e.postScriptName||(e.familyName+e.styleName).replace(/\\s/g,\"\")},designer:{en:e.designer||\" \"},designerURL:{en:e.designerURL||\" \"},manufacturer:{en:e.manufacturer||\" \"},manufacturerURL:{en:e.manufacturerURL||\" \"},license:{en:e.license||\" \"},licenseURL:{en:e.licenseURL||\" \"},version:{en:e.version||\"Version 0.1\"},description:{en:e.description||\" \"},copyright:{en:e.copyright||\" \"},trademark:{en:e.trademark||\" \"}},this.unitsPerEm=e.unitsPerEm||1e3,this.ascender=e.ascender,this.descender=e.descender,this.createdTimestamp=e.createdTimestamp,this.tables=Object.assign(e.tables,{os2:Object.assign({usWeightClass:e.weightClass||this.usWeightClasses.MEDIUM,usWidthClass:e.widthClass||this.usWidthClasses.MEDIUM,fsSelection:e.fsSelection||this.fsSelectionValues.REGULAR},e.tables.os2)})),this.supported=!0,this.glyphs=new ue.GlyphSet(this,e.glyphs||[]),this.encoding=new Ot(this),this.position=new Be(this),this.substitution=new K(this),this.tables=this.tables||{},this._push=null,this._hmtxTableData={},Object.defineProperty(this,\"hinting\",{get:function(){if(this._hinting)return this._hinting;if(this.outlinesFormat===\"truetype\")return this._hinting=new ia(this)}})}w.prototype.hasChar=function(e){return this.encoding.charToGlyphIndex(e)!==null};w.prototype.charToGlyphIndex=function(e){return this.encoding.charToGlyphIndex(e)};w.prototype.charToGlyph=function(e){var r=this.charToGlyphIndex(e),t=this.glyphs.get(r);return t||(t=this.glyphs.get(0)),t};w.prototype.updateFeatures=function(e){return this.defaultRenderOptions.features.map(function(r){return r.script===\"latn\"?{script:\"latn\",tags:r.tags.filter(function(t){return e[t]})}:r})};w.prototype.stringToGlyphs=function(e,r){var t=this,a=new ne,n=function(h){return t.charToGlyphIndex(h.char)};a.registerModifier(\"glyphIndex\",null,n);var s=r?this.updateFeatures(r.features):this.defaultRenderOptions.features;a.applyFeatures(this,s);for(var i=a.getTextGlyphs(e),u=i.length,o=new Array(u),l=this.glyphs.get(0),f=0;f<u;f+=1)o[f]=this.glyphs.get(i[f])||l;return o};w.prototype.nameToGlyphIndex=function(e){return this.glyphNames.nameToGlyphIndex(e)};w.prototype.nameToGlyph=function(e){var r=this.nameToGlyphIndex(e),t=this.glyphs.get(r);return t||(t=this.glyphs.get(0)),t};w.prototype.glyphIndexToName=function(e){return this.glyphNames.glyphIndexToName?this.glyphNames.glyphIndexToName(e):\"\"};w.prototype.getKerningValue=function(e,r){e=e.index||e,r=r.index||r;var t=this.position.defaultKerningTables;return t?this.position.getKerningValue(t,e,r):this.kerningPairs[e+\",\"+r]||0};w.prototype.defaultRenderOptions={kerning:!0,features:[{script:\"arab\",tags:[\"init\",\"medi\",\"fina\",\"rlig\"]},{script:\"latn\",tags:[\"liga\",\"rlig\"]}]};w.prototype.forEachGlyph=function(e,r,t,a,n,s){r=r!==void 0?r:0,t=t!==void 0?t:0,a=a!==void 0?a:72,n=Object.assign({},this.defaultRenderOptions,n);var i=1/this.unitsPerEm*a,u=this.stringToGlyphs(e,n),o;if(n.kerning){var l=n.script||this.position.getDefaultScriptName();o=this.position.getKerningTables(l,n.language)}for(var f=0;f<u.length;f+=1){var h=u[f];if(s.call(this,h,r,t,a,n),h.advanceWidth&&(r+=h.advanceWidth*i),n.kerning&&f<u.length-1){var p=o?this.position.getKerningValue(o,h.index,u[f+1].index):this.getKerningValue(h,u[f+1]);r+=p*i}n.letterSpacing?r+=n.letterSpacing*a:n.tracking&&(r+=n.tracking/1e3*a)}return r};w.prototype.getPath=function(e,r,t,a,n){var s=new P;return this.forEachGlyph(e,r,t,a,n,function(i,u,o,l){var f=i.getPath(u,o,l,n,this);s.extend(f)}),s};w.prototype.getPaths=function(e,r,t,a,n){var s=[];return this.forEachGlyph(e,r,t,a,n,function(i,u,o,l){var f=i.getPath(u,o,l,n,this);s.push(f)}),s};w.prototype.getAdvanceWidth=function(e,r,t){return this.forEachGlyph(e,0,0,r,t,function(){})};w.prototype.draw=function(e,r,t,a,n,s){this.getPath(r,t,a,n,s).draw(e)};w.prototype.drawPoints=function(e,r,t,a,n,s){this.forEachGlyph(r,t,a,n,s,function(i,u,o,l){i.drawPoints(e,u,o,l)})};w.prototype.drawMetrics=function(e,r,t,a,n,s){this.forEachGlyph(r,t,a,n,s,function(i,u,o,l){i.drawMetrics(e,u,o,l)})};w.prototype.getEnglishName=function(e){var r=this.names[e];if(r)return r.en};w.prototype.validate=function(){var e=this;function r(a,n){}function t(a){var n=e.getEnglishName(a);n&&n.trim().length>0}t(\"fontFamily\"),t(\"weightName\"),t(\"manufacturer\"),t(\"copyright\"),t(\"version\"),this.unitsPerEm>0};w.prototype.toTables=function(){return Xn.fontToTable(this)};w.prototype.toBuffer=function(){return console.warn(\"Font.toBuffer is deprecated. Use Font.toArrayBuffer instead.\"),this.toArrayBuffer()};w.prototype.toArrayBuffer=function(){for(var e=this.toTables(),r=e.encode(),t=new ArrayBuffer(r.length),a=new Uint8Array(t),n=0;n<r.length;n++)a[n]=r[n];return t};w.prototype.download=function(e){var r=this.getEnglishName(\"fontFamily\"),t=this.getEnglishName(\"fontSubfamily\");e=e||r.replace(/\\s/g,\"\")+\"-\"+t+\".otf\";var a=this.toArrayBuffer();if(Zn())if(window.URL=window.URL||window.webkitURL,window.URL){var n=new DataView(a),s=new Blob([n],{type:\"font/opentype\"}),i=document.createElement(\"a\");i.href=window.URL.createObjectURL(s),i.download=e;var u=document.createEvent(\"MouseEvents\");u.initEvent(\"click\",!0,!1),i.dispatchEvent(u)}else console.warn(\"Font file could not be downloaded. Try using a different browser.\");else{var o=Ne(),l=Qn(a);o.writeFileSync(e,l)}};w.prototype.fsSelectionValues={ITALIC:1,UNDERSCORE:2,NEGATIVE:4,OUTLINED:8,STRIKEOUT:16,BOLD:32,REGULAR:64,USER_TYPO_METRICS:128,WWS:256,OBLIQUE:512};w.prototype.usWidthClasses={ULTRA_CONDENSED:1,EXTRA_CONDENSED:2,CONDENSED:3,SEMI_CONDENSED:4,MEDIUM:5,SEMI_EXPANDED:6,EXPANDED:7,EXTRA_EXPANDED:8,ULTRA_EXPANDED:9};w.prototype.usWeightClasses={THIN:100,EXTRA_LIGHT:200,LIGHT:300,NORMAL:400,MEDIUM:500,SEMI_BOLD:600,BOLD:700,EXTRA_BOLD:800,BLACK:900};function pa(e,r){var t=JSON.stringify(e),a=256;for(var n in r){var s=parseInt(n);if(!(!s||s<256)){if(JSON.stringify(r[n])===t)return s;a<=s&&(a=s+1)}}return r[a]=e,a}function io(e,r,t){var a=pa(r.name,t);return[{name:\"tag_\"+e,type:\"TAG\",value:r.tag},{name:\"minValue_\"+e,type:\"FIXED\",value:r.minValue<<16},{name:\"defaultValue_\"+e,type:\"FIXED\",value:r.defaultValue<<16},{name:\"maxValue_\"+e,type:\"FIXED\",value:r.maxValue<<16},{name:\"flags_\"+e,type:\"USHORT\",value:0},{name:\"nameID_\"+e,type:\"USHORT\",value:a}]}function oo(e,r,t){var a={},n=new k.Parser(e,r);return a.tag=n.parseTag(),a.minValue=n.parseFixed(),a.defaultValue=n.parseFixed(),a.maxValue=n.parseFixed(),n.skip(\"uShort\",1),a.name=t[n.parseUShort()]||{},a}function uo(e,r,t,a){for(var n=pa(r.name,a),s=[{name:\"nameID_\"+e,type:\"USHORT\",value:n},{name:\"flags_\"+e,type:\"USHORT\",value:0}],i=0;i<t.length;++i){var u=t[i].tag;s.push({name:\"axis_\"+e+\" \"+u,type:\"FIXED\",value:r.coordinates[u]<<16})}return s}function lo(e,r,t,a){var n={},s=new k.Parser(e,r);n.name=a[s.parseUShort()]||{},s.skip(\"uShort\",1),n.coordinates={};for(var i=0;i<t.length;++i)n.coordinates[t[i].tag]=s.parseFixed();return n}function fo(e,r){var t=new b.Table(\"fvar\",[{name:\"version\",type:\"ULONG\",value:65536},{name:\"offsetToData\",type:\"USHORT\",value:0},{name:\"countSizePairs\",type:\"USHORT\",value:2},{name:\"axisCount\",type:\"USHORT\",value:e.axes.length},{name:\"axisSize\",type:\"USHORT\",value:20},{name:\"instanceCount\",type:\"USHORT\",value:e.instances.length},{name:\"instanceSize\",type:\"USHORT\",value:4+e.axes.length*4}]);t.offsetToData=t.sizeOf();for(var a=0;a<e.axes.length;a++)t.fields=t.fields.concat(io(a,e.axes[a],r));for(var n=0;n<e.instances.length;n++)t.fields=t.fields.concat(uo(n,e.instances[n],e.axes,r));return t}function po(e,r,t){var a=new k.Parser(e,r),n=a.parseULong();U.argument(n===65536,\"Unsupported fvar table version.\");var s=a.parseOffset16();a.skip(\"uShort\",1);for(var i=a.parseUShort(),u=a.parseUShort(),o=a.parseUShort(),l=a.parseUShort(),f=[],h=0;h<i;h++)f.push(oo(e,r+s+h*u,t));for(var p=[],c=r+s+i*u,d=0;d<o;d++)p.push(lo(e,c+d*l,f,t));return{axes:f,instances:p}}var ho={make:fo,parse:po},co=function(){return{coverage:this.parsePointer(v.coverage),attachPoints:this.parseList(v.pointer(v.uShortList))}},vo=function(){var e=this.parseUShort();if(U.argument(e===1||e===2||e===3,\"Unsupported CaretValue table version.\"),e===1)return{coordinate:this.parseShort()};if(e===2)return{pointindex:this.parseShort()};if(e===3)return{coordinate:this.parseShort()}},go=function(){return this.parseList(v.pointer(vo))},mo=function(){return{coverage:this.parsePointer(v.coverage),ligGlyphs:this.parseList(v.pointer(go))}},yo=function(){return this.parseUShort(),this.parseList(v.pointer(v.coverage))};function xo(e,r){r=r||0;var t=new v(e,r),a=t.parseVersion(1);U.argument(a===1||a===1.2||a===1.3,\"Unsupported GDEF table version.\");var n={version:a,classDef:t.parsePointer(v.classDef),attachList:t.parsePointer(co),ligCaretList:t.parsePointer(mo),markAttachClassDef:t.parsePointer(v.classDef)};return a>=1.2&&(n.markGlyphSets=t.parsePointer(yo)),n}var bo={parse:xo},te=new Array(10);te[1]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();if(t===1)return{posFormat:1,coverage:this.parsePointer(v.coverage),value:this.parseValueRecord()};if(t===2)return{posFormat:2,coverage:this.parsePointer(v.coverage),values:this.parseValueRecordList()};U.assert(!1,\"0x\"+r.toString(16)+\": GPOS lookup type 1 format must be 1 or 2.\")};te[2]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();U.assert(t===1||t===2,\"0x\"+r.toString(16)+\": GPOS lookup type 2 format must be 1 or 2.\");var a=this.parsePointer(v.coverage),n=this.parseUShort(),s=this.parseUShort();if(t===1)return{posFormat:t,coverage:a,valueFormat1:n,valueFormat2:s,pairSets:this.parseList(v.pointer(v.list(function(){return{secondGlyph:this.parseUShort(),value1:this.parseValueRecord(n),value2:this.parseValueRecord(s)}})))};if(t===2){var i=this.parsePointer(v.classDef),u=this.parsePointer(v.classDef),o=this.parseUShort(),l=this.parseUShort();return{posFormat:t,coverage:a,valueFormat1:n,valueFormat2:s,classDef1:i,classDef2:u,class1Count:o,class2Count:l,classRecords:this.parseList(o,v.list(l,function(){return{value1:this.parseValueRecord(n),value2:this.parseValueRecord(s)}}))}}};te[3]=function(){return{error:\"GPOS Lookup 3 not supported\"}};te[4]=function(){return{error:\"GPOS Lookup 4 not supported\"}};te[5]=function(){return{error:\"GPOS Lookup 5 not supported\"}};te[6]=function(){return{error:\"GPOS Lookup 6 not supported\"}};te[7]=function(){return{error:\"GPOS Lookup 7 not supported\"}};te[8]=function(){return{error:\"GPOS Lookup 8 not supported\"}};te[9]=function(){return{error:\"GPOS Lookup 9 not supported\"}};function So(e,r){r=r||0;var t=new v(e,r),a=t.parseVersion(1);return U.argument(a===1||a===1.1,\"Unsupported GPOS table version \"+a),a===1?{version:a,scripts:t.parseScriptList(),features:t.parseFeatureList(),lookups:t.parseLookupList(te)}:{version:a,scripts:t.parseScriptList(),features:t.parseFeatureList(),lookups:t.parseLookupList(te),variations:t.parseFeatureVariationsList()}}var To=new Array(10);function ko(e){return new b.Table(\"GPOS\",[{name:\"version\",type:\"ULONG\",value:65536},{name:\"scripts\",type:\"TABLE\",value:new b.ScriptList(e.scripts)},{name:\"features\",type:\"TABLE\",value:new b.FeatureList(e.features)},{name:\"lookups\",type:\"TABLE\",value:new b.LookupList(e.lookups,To)}])}var Fo={parse:So,make:ko};function Uo(e){var r={};e.skip(\"uShort\");var t=e.parseUShort();U.argument(t===0,\"Unsupported kern sub-table version.\"),e.skip(\"uShort\",2);var a=e.parseUShort();e.skip(\"uShort\",3);for(var n=0;n<a;n+=1){var s=e.parseUShort(),i=e.parseUShort(),u=e.parseShort();r[s+\",\"+i]=u}return r}function Co(e){var r={};e.skip(\"uShort\");var t=e.parseULong();t>1&&console.warn(\"Only the first kern subtable is supported.\"),e.skip(\"uLong\");var a=e.parseUShort(),n=a&255;if(e.skip(\"uShort\"),n===0){var s=e.parseUShort();e.skip(\"uShort\",3);for(var i=0;i<s;i+=1){var u=e.parseUShort(),o=e.parseUShort(),l=e.parseShort();r[u+\",\"+o]=l}}return r}function Eo(e,r){var t=new k.Parser(e,r),a=t.parseUShort();if(a===0)return Uo(t);if(a===1)return Co(t);throw new Error(\"Unsupported kern table version (\"+a+\").\")}var Oo={parse:Eo};function Lo(e,r,t,a){for(var n=new k.Parser(e,r),s=a?n.parseUShort:n.parseULong,i=[],u=0;u<t+1;u+=1){var o=s.call(n);a&&(o*=2),i.push(o)}return i}var Ro={parse:Lo};function wo(e,r){var t=Ne();t.readFile(e,function(a,n){if(a)return r(a.message);r(null,$t(n))})}function Do(e,r){var t=new XMLHttpRequest;t.open(\"get\",e,!0),t.responseType=\"arraybuffer\",t.onload=function(){return t.response?r(null,t.response):r(\"Font could not be loaded: \"+t.statusText)},t.onerror=function(){r(\"Font could not be loaded\")},t.send()}function yt(e,r){for(var t=[],a=12,n=0;n<r;n+=1){var s=k.getTag(e,a),i=k.getULong(e,a+4),u=k.getULong(e,a+8),o=k.getULong(e,a+12);t.push({tag:s,checksum:i,offset:u,length:o,compression:!1}),a+=16}return t}function Ao(e,r){for(var t=[],a=44,n=0;n<r;n+=1){var s=k.getTag(e,a),i=k.getULong(e,a+4),u=k.getULong(e,a+8),o=k.getULong(e,a+12),l=void 0;u<o?l=\"WOFF\":l=!1,t.push({tag:s,offset:i,compression:l,compressedLength:u,length:o}),a+=20}return t}function I(e,r){if(r.compression===\"WOFF\"){var t=new Uint8Array(e.buffer,r.offset+2,r.compressedLength-2),a=new Uint8Array(r.length);if(Ta(t,a),a.byteLength!==r.length)throw new Error(\"Decompression error: \"+r.tag+\" decompressed length doesn't match recorded length\");var n=new DataView(a.buffer,0);return{data:n,offset:0}}else return{data:e,offset:r.offset}}function Dr(e,r){r=r??{};var t,a,n=new w({empty:!0}),s=new DataView(e,0),i,u=[],o=k.getTag(s,0);if(o===String.fromCharCode(0,1,0,0)||o===\"true\"||o===\"typ1\")n.outlinesFormat=\"truetype\",i=k.getUShort(s,4),u=yt(s,i);else if(o===\"OTTO\")n.outlinesFormat=\"cff\",i=k.getUShort(s,4),u=yt(s,i);else if(o===\"wOFF\"){var l=k.getTag(s,4);if(l===String.fromCharCode(0,1,0,0))n.outlinesFormat=\"truetype\";else if(l===\"OTTO\")n.outlinesFormat=\"cff\";else throw new Error(\"Unsupported OpenType flavor \"+o);i=k.getUShort(s,12),u=Ao(s,i)}else throw new Error(\"Unsupported OpenType signature \"+o);for(var f,h,p,c,d,x,m,y,C,S,R,O,D=0;D<i;D+=1){var L=u[D],F=void 0;switch(L.tag){case\"cmap\":F=I(s,L),n.tables.cmap=Et.parse(F.data,F.offset),n.encoding=new Lt(n.tables.cmap);break;case\"cvt \":F=I(s,L),O=new k.Parser(F.data,F.offset),n.tables.cvt=O.parseShortList(L.length/2);break;case\"fvar\":h=L;break;case\"fpgm\":F=I(s,L),O=new k.Parser(F.data,F.offset),n.tables.fpgm=O.parseByteList(L.length);break;case\"head\":F=I(s,L),n.tables.head=Nt.parse(F.data,F.offset),n.unitsPerEm=n.tables.head.unitsPerEm,t=n.tables.head.indexToLocFormat;break;case\"hhea\":F=I(s,L),n.tables.hhea=Ht.parse(F.data,F.offset),n.ascender=n.tables.hhea.ascender,n.descender=n.tables.hhea.descender,n.numberOfHMetrics=n.tables.hhea.numberOfHMetrics;break;case\"hmtx\":m=L;break;case\"ltag\":F=I(s,L),a=Wt.parse(F.data,F.offset);break;case\"maxp\":F=I(s,L),n.tables.maxp=_t.parse(F.data,F.offset),n.numGlyphs=n.tables.maxp.numGlyphs;break;case\"name\":S=L;break;case\"OS/2\":F=I(s,L),n.tables.os2=xr.parse(F.data,F.offset);break;case\"post\":F=I(s,L),n.tables.post=Qt.parse(F.data,F.offset),n.glyphNames=new Ur(n.tables.post);break;case\"prep\":F=I(s,L),O=new k.Parser(F.data,F.offset),n.tables.prep=O.parseByteList(L.length);break;case\"glyf\":p=L;break;case\"loca\":C=L;break;case\"CFF \":f=L;break;case\"kern\":y=L;break;case\"GDEF\":c=L;break;case\"GPOS\":d=L;break;case\"GSUB\":x=L;break;case\"meta\":R=L;break}}var G=I(s,S);if(n.tables.name=Zt.parse(G.data,G.offset,a),n.names=n.tables.name,p&&C){var Y=t===0,Z=I(s,C),j=Ro.parse(Z.data,Z.offset,n.numGlyphs,Y),$=I(s,p);n.glyphs=aa.parse($.data,$.offset,j,n,r)}else if(f){var M=I(s,f);Gt.parse(M.data,M.offset,n,r)}else throw new Error(\"Font doesn't contain TrueType or CFF outlines.\");var N=I(s,m);if(zt.parse(n,N.data,N.offset,n.numberOfHMetrics,n.numGlyphs,n.glyphs,r),_a(n,r),y){var W=I(s,y);n.kerningPairs=Oo.parse(W.data,W.offset)}else n.kerningPairs={};if(c){var _=I(s,c);n.tables.gdef=bo.parse(_.data,_.offset)}if(d){var V=I(s,d);n.tables.gpos=Fo.parse(V.data,V.offset),n.position.init()}if(x){var H=I(s,x);n.tables.gsub=Kt.parse(H.data,H.offset)}if(h){var X=I(s,h);n.tables.fvar=ho.parse(X.data,X.offset,n.names)}if(R){var A=I(s,R);n.tables.meta=Jt.parse(A.data,A.offset),n.metas=n.tables.meta}return n}function Bo(e,r,t){t=t??{};var a=typeof window>\"u\",n=a&&!t.isUrl?wo:Do;return new Promise(function(s,i){n(e,function(u,o){if(u){if(r)return r(u);i(u)}var l;try{l=Dr(o,t)}catch(f){if(r)return r(f,null);i(f)}if(r)return r(null,l);s(l)})})}function Io(e,r){var t=Ne(),a=t.readFileSync(e);return Dr($t(a),r)}var Mo=Object.freeze({__proto__:null,Font:w,Glyph:Q,Path:P,BoundingBox:pe,_parse:k,parse:Dr,load:Bo,loadSync:Io}),Ho=Mo;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../node_modules/@splinetool/runtime/build/opentype.js\n"));

/***/ })

}]);
/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandNationalGeographic = createReactComponent("outline", "brand-national-geographic", "IconBrandNationalGeographic", [["path", { "d": "M7 3h10v18h-10z", "key": "svg-0" }]]);

export { IconBrandNationalGeographic as default };
//# sourceMappingURL=IconBrandNationalGeographic.mjs.map

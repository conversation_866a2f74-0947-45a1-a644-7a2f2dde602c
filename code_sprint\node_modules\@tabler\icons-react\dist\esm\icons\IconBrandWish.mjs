/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandWish = createReactComponent("outline", "brand-wish", "IconBrandWish", [["path", { "d": "M2 6l5.981 2.392l-.639 6.037c-.18 .893 .06 1.819 .65 2.514a3 3 0 0 0 2.381 1.057a4.328 4.328 0 0 0 4.132 -3.57c-.18 .893 .06 1.819 .65 2.514a3 3 0 0 0 2.38 1.056a4.328 4.328 0 0 0 4.132 -3.57l.333 -4.633", "key": "svg-0" }], ["path", { "d": "M14.504 14.429l.334 -3", "key": "svg-1" }]]);

export { IconBrandWish as default };
//# sourceMappingURL=IconBrandWish.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandWindy = createReactComponent("outline", "brand-windy", "IconBrandWindy", [["path", { "d": "M9 4c0 5.5 -.33 16 4 16s7.546 -11.27 8 -13", "key": "svg-0" }], ["path", { "d": "M3 4c.253 5.44 1.449 16 5.894 16c4.444 0 8.42 -10.036 9.106 -14", "key": "svg-1" }]]);

export { IconBrandWindy as default };
//# sourceMappingURL=IconBrandWindy.mjs.map

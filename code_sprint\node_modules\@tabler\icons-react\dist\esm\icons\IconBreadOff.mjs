/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBreadOff = createReactComponent("outline", "bread-off", "IconBreadOff", [["path", { "d": "M8 4l10 .005v-.005a3 3 0 0 1 2 5.235v6.765m-.59 3.418c-.36 .36 -.86 .582 -1.41 .582h-12a2 2 0 0 1 -2 -2v-8.764a3 3 0 0 1 .418 -4.785", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]]);

export { IconBreadOff as default };
//# sourceMappingURL=IconBreadOff.mjs.map

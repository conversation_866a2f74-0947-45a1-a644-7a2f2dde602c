/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandTed = createReactComponent("outline", "brand-ted", "IconBrandTed", [["path", { "d": "M2 8h4", "key": "svg-0" }], ["path", { "d": "M4 8v8", "key": "svg-1" }], ["path", { "d": "M13 8h-4v8h4", "key": "svg-2" }], ["path", { "d": "M9 12h2.5", "key": "svg-3" }], ["path", { "d": "M16 8v8h2a3 3 0 0 0 3 -3v-2a3 3 0 0 0 -3 -3h-2z", "key": "svg-4" }]]);

export { IconBrandTed as default };
//# sourceMappingURL=IconBrandTed.mjs.map

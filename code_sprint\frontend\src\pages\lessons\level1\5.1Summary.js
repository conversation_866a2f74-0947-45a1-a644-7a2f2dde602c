import React from "react";
import { useRouter } from "next/router";

const Summary = () => {
  const router = useRouter();

  const handleStartQuiz = () => {
    router.push("/lessons/quiz"); // Navigate to the Quiz page
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 via-white to-purple-100 p-8 text-gray-700">
      <div className="max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-6">
        <h1 className="text-4xl font-extrabold mb-6 text-purple-700 text-center">
          Summary of Key Concepts
        </h1>
        <p className="text-lg mb-6 text-center">
          Here&apos;s a recap of what we&apos;ve learned in this course:
        </p>

        <ul className="list-disc pl-6 mb-6 text-lg space-y-4">
          <li>
            <strong>Computers:</strong> Powerful machines that process data using input, memory, processing, and output.
          </li>
          <li>
            <strong>Programs:</strong> Sets of instructions that tell computers what to do.
          </li>
          <li>
            <strong>Internet of Things (IoT):</strong> Connects everyday objects to the internet, creating smart homes, cities, and workplaces.
          </li>
          <li>
            <strong>Computer Vision:</strong> Enables computers to &quot;see&quot; and make decisions based on images or videos.
          </li>
          <li>
            <strong>Scratch:</strong> A platform for creating fun, interactive games and animations using simple coding blocks.
          </li>
        </ul>

        <div className="bg-blue-100 p-4 border-l-4 border-blue-500 rounded-lg">
          <p className="text-lg text-center font-semibold">
            Reflect on these topics to prepare for the quiz!
          </p>
        </div>

        <div className="mt-8 text-center">
          <button
            onClick={handleStartQuiz}
            className="px-6 py-3 bg-purple-500 text-white rounded-lg shadow-md hover:bg-purple-600"
          >
            Take the Quiz
          </button>
        </div>
      </div>
    </div>
  );
};

export default Summary;

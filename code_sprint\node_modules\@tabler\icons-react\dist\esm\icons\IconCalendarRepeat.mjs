/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCalendarRepeat = createReactComponent("outline", "calendar-repeat", "IconCalendarRepeat", [["path", { "d": "M12.5 21h-6.5a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v3", "key": "svg-0" }], ["path", { "d": "M16 3v4", "key": "svg-1" }], ["path", { "d": "M8 3v4", "key": "svg-2" }], ["path", { "d": "M4 11h12", "key": "svg-3" }], ["path", { "d": "M20 14l2 2h-3", "key": "svg-4" }], ["path", { "d": "M20 18l2 -2", "key": "svg-5" }], ["path", { "d": "M19 16a3 3 0 1 0 2 5.236", "key": "svg-6" }]]);

export { IconCalendarRepeat as default };
//# sourceMappingURL=IconCalendarRepeat.mjs.map

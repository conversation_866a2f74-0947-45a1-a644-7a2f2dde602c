/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandRevolut = createReactComponent("outline", "brand-revolut", "IconBrandRevolut", [["path", { "d": "M5 10h3v10h-3z", "key": "svg-0" }], ["path", { "d": "M14.5 4h-9.5v3h9.4a1.5 1.5 0 0 1 0 3h-3.4v4l4 6h4l-5 -7h.5a4.5 4.5 0 1 0 0 -9z", "key": "svg-1" }]]);

export { IconBrandRevolut as default };
//# sourceMappingURL=IconBrandRevolut.mjs.map

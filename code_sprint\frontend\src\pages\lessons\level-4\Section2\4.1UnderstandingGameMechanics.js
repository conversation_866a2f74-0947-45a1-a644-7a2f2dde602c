import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const UnderstandingGameMechanics = () => {
  const typedText = useTypingAnimation("Mastering the Game", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <h2 className="text-2xl font-bold text-gray-800 mt-6">
          Practice Makes Perfect
        </h2>
        <p className="text-lg text-gray-700 mb-4">
          In esports, just like in traditional sports, practice is crucial. But
          it&apos;s not just about playing more—it&apos;s about practicing smart. Here&apos;s
          how you can improve your skills efficiently:
        </p>
        <ul className="list-disc pl-6 text-lg text-gray-700 mb-4">
          <li>
            <strong>Focus on Fundamentals:</strong> Mastering the basics of your
            game is the first step. Whether it&apos;s aiming, movement, or game
            strategy, getting these down will make you a stronger player.
          </li>
          <li>
            <strong>Consistent Practice:</strong> Set aside time each day or
            week dedicated to improving your skills. Consistency helps build
            muscle memory and familiarity with the game.
          </li>
          <li>
            <strong>Watch and Learn:</strong> Study replays of your games to
            identify mistakes and areas for improvement. Watching professional
            players can also give you insights into advanced strategies and
            techniques.
          </li>
          <li>
            <strong>Challenge Yourself:</strong> Play against tougher opponents
            to push your limits. Even if you lose, you&apos;ll learn valuable lessons
            that will help you grow as a player.
          </li>
          <li>
            <strong>Stay Calm Under Pressure:</strong> Many esports games
            require split-second decisions. Practice staying calm and focused,
            especially during high-pressure moments in the game.
          </li>
        </ul>
        <hr className="my-6 border-t border-gray-300" />
        <h2 className="text-2xl font-bold text-gray-800 mt-6">
          Understanding Game Mechanics
        </h2>
        <p className="text-lg text-gray-700 mb-4">
          Every game has its own set of rules and mechanics. To excel, you need
          to understand these inside and out:
        </p>
        <ul className="list-disc pl-6 text-lg text-gray-700 mb-4">
          <li>
            <strong>Game Knowledge:</strong> Learn everything you can about your
            game. This includes maps, characters, items, and strategies. The
            more you know, the better you can adapt to different situations.
          </li>
          <li>
            <strong>Meta and Updates:</strong> Games often change with updates
            that introduce new characters, balance changes, or mechanics. Stay
            up-to-date with the latest changes to stay ahead of the competition.
          </li>
          <li>
            <strong>Mind Games:</strong> Predicting your opponent&apos;s moves and
            playing mind games can give you a significant advantage.
            Understanding your opponent&apos;s habits and tendencies can help you
            outsmart them.
          </li>
        </ul>
      </div>
    </div>
  );
};

export default UnderstandingGameMechanics;

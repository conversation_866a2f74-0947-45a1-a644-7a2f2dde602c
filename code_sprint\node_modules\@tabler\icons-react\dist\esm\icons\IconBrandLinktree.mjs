/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandLinktree = createReactComponent("outline", "brand-linktree", "IconBrandLinktree", [["path", { "d": "M4 10h16", "key": "svg-0" }], ["path", { "d": "M6.5 4.5l11 11", "key": "svg-1" }], ["path", { "d": "M6.5 15.5l11 -11", "key": "svg-2" }], ["path", { "d": "M12 10v-8", "key": "svg-3" }], ["path", { "d": "M12 15v7", "key": "svg-4" }]]);

export { IconBrandLinktree as default };
//# sourceMappingURL=IconBrandLinktree.mjs.map

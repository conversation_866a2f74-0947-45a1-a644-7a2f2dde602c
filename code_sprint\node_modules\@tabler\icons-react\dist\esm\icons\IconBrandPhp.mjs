/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandPhp = createReactComponent("outline", "brand-php", "IconBrandPhp", [["path", { "d": "M12 12m-10 0a10 9 0 1 0 20 0a10 9 0 1 0 -20 0", "key": "svg-0" }], ["path", { "d": "M5.5 15l.395 -1.974l.605 -3.026h1.32a1 1 0 0 1 .986 1.164l-.167 1a1 1 0 0 1 -.986 .836h-1.653", "key": "svg-1" }], ["path", { "d": "M15.5 15l.395 -1.974l.605 -3.026h1.32a1 1 0 0 1 .986 1.164l-.167 1a1 1 0 0 1 -.986 .836h-1.653", "key": "svg-2" }], ["path", { "d": "M12 7.5l-1 5.5", "key": "svg-3" }], ["path", { "d": "M11.6 10h2.4l-.5 3", "key": "svg-4" }]]);

export { IconBrandPhp as default };
//# sourceMappingURL=IconBrandPhp.mjs.map

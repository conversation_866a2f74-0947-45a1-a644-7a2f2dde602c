import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Esportsvs = () => {
  const typedText = useTypingAnimation("Esports vs. Traditional Sports", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>How Esports Differ from Traditional Sports</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          You might wonder how esports differ from traditional sports like
          soccer or basketball. Here are a few key distinctions:
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Playing Field:</strong> Traditional sports happen on
            physical fields, courts, or arenas. In esports, the &quot;playing field&quot;
            exists within a video game&apos;s virtual world.
          </li>
          <li>
            <strong>Physical vs. Mental:</strong> Traditional sports demand
            physical prowess, such as running or jumping. Esports, however,
            emphasize mental agility, including quick reflexes, strategic
            thinking, and precise hand-eye coordination.
          </li>
          <li>
            <strong>Teamwork and Strategy:</strong> Both traditional sports and
            esports rely heavily on teamwork and strategy. Whether you&apos;re
            scoring a goal in soccer or coordinating a plan to capture the
            enemy&apos;s base in a game, communication and teamwork are crucial.
          </li>
        </ul>
        <p className="text-lg text-gray-700 mb-4">
          Just like athletes in traditional sports, esports players undergo
          rigorous training, work with coaches, and even follow specific diets
          to stay sharp. Being an esports competitor is more than just gaming—it
          requires dedication and practice!
        </p>
      </div>
    </div>
  );
};

export default Esportsvs;

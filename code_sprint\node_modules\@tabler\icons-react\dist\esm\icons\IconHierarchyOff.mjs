/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconHierarchyOff = createReactComponent("outline", "hierarchy-off", "IconHierarchyOff", [["path", { "d": "M12 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M5 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M17.585 17.587a2 2 0 0 0 2.813 2.843", "key": "svg-2" }], ["path", { "d": "M6.5 17.5l5.5 -4.5l5.5 4.5", "key": "svg-3" }], ["path", { "d": "M12 7v1m0 4v1", "key": "svg-4" }], ["path", { "d": "M3 3l18 18", "key": "svg-5" }]]);

export { IconHierarchyOff as default };
//# sourceMappingURL=IconHierarchyOff.mjs.map

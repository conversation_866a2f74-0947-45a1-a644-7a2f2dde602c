/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandLoom = createReactComponent("outline", "brand-loom", "IconBrandLoom", [["path", { "d": "M17.464 6.518a6 6 0 1 0 -3.023 7.965", "key": "svg-0" }], ["path", { "d": "M17.482 17.464a6 6 0 1 0 -7.965 -3.023", "key": "svg-1" }], ["path", { "d": "M6.54 17.482a6 6 0 1 0 3.024 -7.965", "key": "svg-2" }], ["path", { "d": "M6.518 6.54a6 6 0 1 0 7.965 3.024", "key": "svg-3" }]]);

export { IconBrandLoom as default };
//# sourceMappingURL=IconBrandLoom.mjs.map

import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const DevelopingYourOwnStrategy = () => {
  const typedText = useTypingAnimation("Teamwork makes the Dream Work", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <h2 className="text-2xl font-bold text-gray-800 mt-6">
          Teamwork and Communication
        </h2>
        <p className="text-lg text-gray-700 mb-4">
          In many esports games, teamwork is essential. Here&apos;s how to become a
          great team player:
        </p>
        <ul className="list-disc pl-6 text-lg text-gray-700 mb-4">
          <li>
            <strong>Communication is Key:</strong> In team-based games, clear
            and concise communication can make or break a match. Use voice chat
            or in-game signals to keep your team coordinated.
          </li>
          <li>
            <strong>Know Your Role:</strong> Understand your role within the
            team. Whether you&apos;re a leader calling the shots or a support player
            helping others, knowing your role is crucial for success.
          </li>
          <li>
            <strong>Support Your Teammates:</strong> Good teamwork isn&apos;t just
            about individual skill; it&apos;s about working together. Be ready to
            help your teammates and trust them to do their part.
          </li>
          <li>
            <strong>Adapt and Improvise:</strong> Esports games are dynamic, and
            strategies can change on the fly. Be flexible and willing to adapt
            to new strategies or changes in the game plan.
          </li>
        </ul>
        <hr className="my-6 border-t border-gray-300" />
        <h2 className="text-2xl font-bold text-gray-800 mt-6">
          Mental and Physical Well-being
        </h2>
        <p className="text-lg text-gray-700 mb-4">
          Staying sharp in esports isn&apos;t just about in-game skills. Your mental
          and physical health play a big role too:
        </p>
        <ul className="list-disc pl-6 text-lg text-gray-700 mb-4">
          <li>
            <strong>Stay Positive:</strong> Gaming can be intense, and it&apos;s easy
            to get frustrated. Staying positive and keeping a good attitude,
            even when things go wrong, helps maintain focus and morale.
          </li>
          <li>
            <strong>Take Breaks:</strong> It&apos;s important to take regular breaks
            to avoid burnout. Short breaks during gaming sessions and longer
            periods of rest can help keep you refreshed and ready to perform.
          </li>
          <li>
            <strong>Physical Exercise:</strong> Staying physically active
            improves your overall health and can even enhance your gaming
            performance by keeping your reflexes sharp.
          </li>
          <li>
            <strong>Healthy Diet and Sleep:</strong> Eating well and getting
            enough sleep are essential for maintaining focus and reaction times.
            Make sure you&apos;re fueling your body with the right foods and getting
            enough rest.
          </li>
        </ul>
        <hr className="my-6 border-t border-gray-300" />
        <h2 className="text-2xl font-bold text-gray-800 mt-6">
          Joining Teams and Climbing the Ranks
        </h2>
        <p className="text-lg text-gray-700 mb-4">
          Once you&apos;ve honed your skills, it&apos;s time to take the next step:
        </p>
        <ul className="list-disc pl-6 text-lg text-gray-700 mb-4">
          <li>
            <strong>Find a Team:</strong> Many esports games are team-based, so
            finding a group of players who complement your skills is essential.
            Look for teams that match your level and have similar goals.
          </li>
          <li>
            <strong>Climb the Ladder:</strong> Many games have ranked modes
            where you can compete to improve your ranking. Climbing the ranks is
            a great way to challenge yourself and gauge your progress.
          </li>
          <li>
            <strong>Participate in Tournaments:</strong> Start with local or
            online tournaments to gain experience. As you improve, you can aim
            for larger and more competitive events.
          </li>
        </ul>
      </div>
    </div>
  );
};

export default DevelopingYourOwnStrategy;

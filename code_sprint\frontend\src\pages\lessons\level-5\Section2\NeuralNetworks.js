import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../../level-4/Section1/useTypingAnimation";
import styles from "../../level-4/Section1/animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const Sportsgames = () => {
  const typedText = useTypingAnimation("Neural Networks", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>What are Neural Networks?</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Neural Networks are a specific type of Machine Learning model that is
          inspired by the human brain. Just like your brain is made up of
          billions of neurons connected together, a neural network is made up of
          layers of connected nodes, or "neurons." These networks are designed
          to recognize patterns and learn from data in a way that's similar to
          how our brains work.
        </p>
        <img src="/Neural.png" alt="Neural Networks" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>How Neural Networks Work</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Imagine a neural network as a series of layers. The first layer takes
          in input data, like a picture of a cat. This data is then passed
          through one or more hidden layers, where the network processes the
          information by identifying patterns and features, like the shape of
          the ears or the texture of the fur. Finally, the last layer gives the
          output, like identifying whether the picture is of a cat, a dog, or
          something else.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Each connection between neurons has a weight, which determines how
          much influence one neuron has on another. During the training process,
          the network adjusts these weights based on the data it sees, so it
          gets better at making accurate predictions. This process is called
          "training" the network, and it’s similar to how you might learn to
          recognize different types of animals by seeing lots of pictures of
          them.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Why Are Neural Networks Important?</strong>
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Neural Networks are important because they have the ability to solve
          problems that are too complex for traditional computer programs. For
          example, traditional programming might struggle with understanding the
          nuances of human speech or recognizing objects in a cluttered
          environment, but neural networks excel at these tasks. They are used
          in a wide range of applications, from virtual assistants that
          understand your voice commands to self-driving cars that need to
          navigate busy streets safely.
        </p>
      </div>
    </div>
  );
};

export default Sportsgames;

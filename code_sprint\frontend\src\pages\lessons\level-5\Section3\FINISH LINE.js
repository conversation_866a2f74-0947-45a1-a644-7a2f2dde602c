import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../../level-4/Section1/useTypingAnimation";
import styles from "../../level-4/Section1/useTypingAnimation";
import { Player } from "@lottiefiles/react-lottie-player";

const TheGoodTheBadAndTheElif = () => {
  const [code, setCode] = useState(`Ask me a question: \n`);
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const outputRef = useRef(null);
  const [buttonClicked, setButtonClicked] = useState(false);

  const typedText = useTypingAnimation("FINISH LINE!", 100);

  const [animatedCode, setAnimatedCode] = useState("");

  const codeBlockStyle = {
    backgroundColor: "#f7fafc", // Light gray background
    padding: "16px", // Padding for space around the code
    borderRadius: "8px", // Rounded corners
    color: "#2d3748", // Text color
    marginTop: "16px", // Top margin for spacing
    whiteSpace: "pre-wrap", // Preserve spaces and newlines
    wordWrap: "break-word", // Allow long words to break into the next line
    fontFamily: "monospace", // Monospace font for code
    fontSize: "16px", // Slightly larger font size for readability
  };

  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);

  const runCode = () => {
    try {
      const simulatedInput = userInput?.trim().toLowerCase();

      if (!simulatedInput) {
        setOutput("Error: Please Ask me a question.");
        setShowOutput(true);
        scrollToOutput();
        return;
      }

      let output = "";
      let confetti = false;

      // Simulating chatbot response
      const chatbotResponse = (userInput) => {
        if (userInput.includes("name")) {
          return "I'm a simple chatbot created with Python!";
        } else if (userInput.includes("weather")) {
          return "I'm not sure about the weather, but I hope it's nice!";
        } else {
          return "I don't understand, but I'm here to learn!";
        }
      };

      output = chatbotResponse(simulatedInput);

      // If the output is not an error message, show confetti
      if (
        output !== "Error: Please provide an input." &&
        output !== "Error: An unexpected error occurred."
      ) {
        confetti = true;
      }

      setOutput(output);
      setShowConfetti(confetti);
      setShowOutput(true);
      scrollToOutput();

      if (confetti) {
        setTimeout(() => setShowConfetti(false), 1000);
      }
    } catch {
      setOutput("Error: An unexpected error occurred.");
      setShowOutput(true);
    }
  };

  const scrollToOutput = () => {
    setTimeout(() => {
      if (outputRef.current) {
        outputRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {
      const lines = newCode.split("\n");
      const prompt = "Ask me a question: ";
      const inputWithoutPrompt = lines.slice(1).join("\n");
      setUserInput(inputWithoutPrompt);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #92b4a7, #81667a)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #92b4a7, #81667a)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <p className="text-lg text-gray-700 mb-6">
          🎉 Congrats, you've made it through the world of NLP! 🎉
        </p>
        <p className="text-lg text-gray-700 mb-6">
          You've learned how to make your computer understand and respond to
          human language, create fun games, and even build your own chatbot. But
          this is just the beginning! Keep exploring, keep coding, and who
          knows? Maybe you'll create the next big thing in AI!
        </p>
        <p className="text-lg text-gray-700">Keep learning and stay curious!</p>
      </div>
    </div>
  );
};

export default TheGoodTheBadAndTheElif;

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandLinkedin = createReactComponent("outline", "brand-linkedin", "IconBrandLinkedin", [["path", { "d": "M8 11v5", "key": "svg-0" }], ["path", { "d": "M8 8v.01", "key": "svg-1" }], ["path", { "d": "M12 16v-5", "key": "svg-2" }], ["path", { "d": "M16 16v-3a2 2 0 1 0 -4 0", "key": "svg-3" }], ["path", { "d": "M3 7a4 4 0 0 1 4 -4h10a4 4 0 0 1 4 4v10a4 4 0 0 1 -4 4h-10a4 4 0 0 1 -4 -4z", "key": "svg-4" }]]);

export { IconBrandLinkedin as default };
//# sourceMappingURL=IconBrandLinkedin.mjs.map

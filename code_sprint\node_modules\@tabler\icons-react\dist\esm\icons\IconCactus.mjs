/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCactus = createReactComponent("outline", "cactus", "IconCactus", [["path", { "d": "M6 9v1a3 3 0 0 0 3 3h1", "key": "svg-0" }], ["path", { "d": "M18 8v5a3 3 0 0 1 -3 3h-1", "key": "svg-1" }], ["path", { "d": "M10 21v-16a2 2 0 1 1 4 0v16", "key": "svg-2" }], ["path", { "d": "M7 21h10", "key": "svg-3" }]]);

export { IconCactus as default };
//# sourceMappingURL=IconCactus.mjs.map

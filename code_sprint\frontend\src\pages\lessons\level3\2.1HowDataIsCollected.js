import React from 'react';

export default function HowDataIsCollected() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 text-white p-10">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .card {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    margin: 20px 0;
                    max-width: 600px;
                    text-align: left;
                }

                .image-placeholder {
                    width: 400px;
                    height: 300px;
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px dashed #fff;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 1rem;
                    margin: 16px;
                    flex-shrink: 0; /* Ensures image placeholder doesn't shrink */
                }

                .content-row {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 20px; /* Adds space between cards and placeholders */
                    flex-wrap: wrap; /* Ensures responsiveness on smaller screens */
                }
                `}
            </style>

            <h1 className="text-5xl font-bold mb-8">How Data is Collected</h1>
            <p className="text-lg text-center max-w-3xl mb-12">
                Data collection is the first step in the journey of Data Science. Data is gathered from various sources like <span className="highlight">surveys</span>, <span className="highlight">sensors</span>, and <span className="highlight">online behavior</span> to uncover patterns and insights.
            </p>

            {/* Example 1: Surveys */}
            <div className="content-row">
                <img
                    src="/robot_doing_survey.png" /* Adjusted to your actual image path */
                    alt="Survey Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📋 Surveys</h2>
                    <p className="text-lg">
                        Surveys are an excellent way to collect data directly from people. For example:
                        <br />
                        - A company asks customers about their favorite products.<br />
                        - A school collects feedback on teaching methods from students.
                    </p>
                </div>
            </div>

            {/* Example 2: Sensors */}
            <div className="content-row">
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📡 Sensors</h2>
                    <p className="text-lg">
                        Sensors are used to gather data automatically from the environment. For instance:
                        <br />
                        - A weather station collects temperature and rainfall data.<br />
                        - A smart watch tracks your heart rate and steps.
                    </p>
                </div>
                <img
                    src="/sensor_reading.jpg" /* Adjusted to your actual image path */
                    alt="Sensors Example"
                    className="image-placeholder"
                />
            </div>

            {/* Example 3: Online Behavior */}
            <div className="content-row">
                <img
                    src="/online_behavior_tracking.jpg" /* Adjusted to your actual image path */
                    alt="Online Behavior Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">🌐 Online Behavior</h2>
                    <p className="text-lg">
                        Online activities like searches, clicks, and purchases provide valuable data. For example:
                        <br />
                        - An e-commerce website tracks your browsing habits to recommend products.<br />
                        - A video platform suggests videos based on your viewing history.
                    </p>
                </div>
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-xl font-semibold mt-16 max-w-2xl">
                With the right data, we can uncover trends, make predictions, and create amazing solutions for real-world problems!
            </p>
        </div>
    );
}

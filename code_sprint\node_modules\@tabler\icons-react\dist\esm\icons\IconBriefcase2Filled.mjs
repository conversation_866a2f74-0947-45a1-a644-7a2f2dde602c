/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBriefcase2Filled = createReactComponent("filled", "briefcase-2-filled", "IconBriefcase2Filled", [["path", { "d": "M14 2a3 3 0 0 1 3 3v1h2a3 3 0 0 1 3 3v9a3 3 0 0 1 -3 3h-14a3 3 0 0 1 -3 -3v-9a3 3 0 0 1 3 -3h2v-1a3 3 0 0 1 3 -3zm0 2h-4a1 1 0 0 0 -1 1v1h6v-1a1 1 0 0 0 -1 -1", "key": "svg-0" }]]);

export { IconBriefcase2Filled as default };
//# sourceMappingURL=IconBriefcase2Filled.mjs.map

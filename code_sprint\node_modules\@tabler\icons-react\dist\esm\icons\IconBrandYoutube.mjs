/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandYoutube = createReactComponent("outline", "brand-youtube", "IconBrandYoutube", [["path", { "d": "M2 8a4 4 0 0 1 4 -4h12a4 4 0 0 1 4 4v8a4 4 0 0 1 -4 4h-12a4 4 0 0 1 -4 -4v-8z", "key": "svg-0" }], ["path", { "d": "M10 9l5 3l-5 3z", "key": "svg-1" }]]);

export { IconBrandYoutube as default };
//# sourceMappingURL=IconBrandYoutube.mjs.map

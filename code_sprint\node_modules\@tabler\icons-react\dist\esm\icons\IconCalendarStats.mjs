/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCalendarStats = createReactComponent("outline", "calendar-stats", "IconCalendarStats", [["path", { "d": "M11.795 21h-6.795a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v4", "key": "svg-0" }], ["path", { "d": "M18 14v4h4", "key": "svg-1" }], ["path", { "d": "M18 18m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0", "key": "svg-2" }], ["path", { "d": "M15 3v4", "key": "svg-3" }], ["path", { "d": "M7 3v4", "key": "svg-4" }], ["path", { "d": "M3 11h16", "key": "svg-5" }]]);

export { IconCalendarStats as default };
//# sourceMappingURL=IconCalendarStats.mjs.map

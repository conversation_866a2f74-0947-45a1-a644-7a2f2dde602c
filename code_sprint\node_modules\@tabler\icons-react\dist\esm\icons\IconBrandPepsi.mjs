/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandPepsi = createReactComponent("outline", "brand-pepsi", "IconBrandPepsi", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M4 16c5.713 -2.973 11 -3.5 13.449 -11.162", "key": "svg-1" }], ["path", { "d": "M5 17.5c5.118 -2.859 15 0 14 -11", "key": "svg-2" }]]);

export { IconBrandPepsi as default };
//# sourceMappingURL=IconBrandPepsi.mjs.map

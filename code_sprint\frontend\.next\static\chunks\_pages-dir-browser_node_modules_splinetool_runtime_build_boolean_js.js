"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_boolean_js"],{

/***/ "(pages-dir-browser)/../node_modules/@splinetool/runtime/build/boolean.js":
/*!************************************************************!*\
  !*** ../node_modules/@splinetool/runtime/build/boolean.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\nvar Module = (() => {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(moduleArg = {}) {\n\nvar Module=moduleArg;var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram=\"./this.program\";var quit_=(status,toThrow)=>{throw toThrow};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=\"undefined\"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=url=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];if(typeof WebAssembly!=\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(text)}}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module[\"HEAP8\"]=HEAP8=new Int8Array(b);Module[\"HEAP16\"]=HEAP16=new Int16Array(b);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(b);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(b);Module[\"HEAP32\"]=HEAP32=new Int32Array(b);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(b);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(b);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(b)}var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;if(!Module[\"noFSInit\"]&&!FS.init.initialized)FS.init();FS.ignorePermissions=false;TTY.init();callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function getUniqueRunDependency(id){return id}function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}what=\"Aborted(\"+what+\")\";err(what);ABORT=true;EXITSTATUS=1;what+=\". Build with -sASSERTIONS for more info.\";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix=\"data:application/octet-stream;base64,\";var isDataURI=filename=>filename.startsWith(dataURIPrefix);var wasmBinaryFile;wasmBinaryFile=\"boolean.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw\"both async and sync fetching of the wasm failed\"}function getBinaryPromise(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==\"function\"){return fetch(binaryFile,{credentials:\"same-origin\"}).then(response=>{if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+binaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(()=>getBinarySync(binaryFile))}}return Promise.resolve().then(()=>getBinarySync(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>WebAssembly.instantiate(binary,imports)).then(instance=>instance).then(receiver,reason=>{err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming==\"function\"&&!isDataURI(binaryFile)&&typeof fetch==\"function\"){return fetch(binaryFile,{credentials:\"same-origin\"}).then(response=>{var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err(`wasm streaming compile failed: ${reason}`);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(binaryFile,imports,callback)})})}return instantiateArrayBuffer(binaryFile,imports,callback)}function createWasm(){var info={\"a\":wasmImports};function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports[\"A\"];updateMemoryViews();wasmTable=wasmExports[\"C\"];addOnInit(wasmExports[\"B\"]);removeRunDependency(\"wasm-instantiate\");return wasmExports}addRunDependency(\"wasm-instantiate\");function receiveInstantiationResult(result){receiveInstance(result[\"instance\"])}if(Module[\"instantiateWasm\"]){try{return Module[\"instantiateWasm\"](info,receiveInstance)}catch(e){err(`Module.instantiateWasm callback failed with error: ${e}`);readyPromiseReject(e)}}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult).catch(readyPromiseReject);return{}}var tempDouble;var tempI64;var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var noExitRuntime=Module[\"noExitRuntime\"]||true;function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24;this.set_type=function(type){HEAPU32[this.ptr+4>>2]=type};this.get_type=function(){return HEAPU32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAPU32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAPU32[this.ptr+8>>2]};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)};this.set_adjusted_ptr=function(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr};this.get_adjusted_ptr=function(){return HEAPU32[this.ptr+16>>2]};this.get_exception_ptr=function(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer){return HEAPU32[this.excPtr>>2]}var adjusted=this.get_adjusted_ptr();if(adjusted!==0)return adjusted;return this.excPtr}}var exceptionLast=0;var uncaughtExceptionCount=0;var ___cxa_throw=(ptr,type,destructor)=>{var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast};var tupleRegistrations={};var runDestructors=destructors=>{while(destructors.length){var ptr=destructors.pop();var del=destructors.pop();del(ptr)}};function simpleReadValueFromPointer(pointer){return this[\"fromWireType\"](HEAP32[pointer>>2])}var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var InternalError;var throwInternalError=message=>{throw new InternalError(message)};var whenDependentTypesAreResolved=(myTypes,dependentTypes,getTypeConverters)=>{myTypes.forEach(function(type){typeDependencies[type]=dependentTypes});function onComplete(typeConverters){var myTypeConverters=getTypeConverters(typeConverters);if(myTypeConverters.length!==myTypes.length){throwInternalError(\"Mismatched type converter count\")}for(var i=0;i<myTypes.length;++i){registerType(myTypes[i],myTypeConverters[i])}}var typeConverters=new Array(dependentTypes.length);var unregisteredTypes=[];var registered=0;dependentTypes.forEach((dt,i)=>{if(registeredTypes.hasOwnProperty(dt)){typeConverters[i]=registeredTypes[dt]}else{unregisteredTypes.push(dt);if(!awaitingDependencies.hasOwnProperty(dt)){awaitingDependencies[dt]=[]}awaitingDependencies[dt].push(()=>{typeConverters[i]=registeredTypes[dt];++registered;if(registered===unregisteredTypes.length){onComplete(typeConverters)}})}});if(0===unregisteredTypes.length){onComplete(typeConverters)}};var __embind_finalize_value_array=rawTupleType=>{var reg=tupleRegistrations[rawTupleType];delete tupleRegistrations[rawTupleType];var elements=reg.elements;var elementsLength=elements.length;var elementTypes=elements.map(elt=>elt.getterReturnType).concat(elements.map(elt=>elt.setterArgumentType));var rawConstructor=reg.rawConstructor;var rawDestructor=reg.rawDestructor;whenDependentTypesAreResolved([rawTupleType],elementTypes,function(elementTypes){elements.forEach((elt,i)=>{var getterReturnType=elementTypes[i];var getter=elt.getter;var getterContext=elt.getterContext;var setterArgumentType=elementTypes[i+elementsLength];var setter=elt.setter;var setterContext=elt.setterContext;elt.read=ptr=>getterReturnType[\"fromWireType\"](getter(getterContext,ptr));elt.write=(ptr,o)=>{var destructors=[];setter(setterContext,ptr,setterArgumentType[\"toWireType\"](destructors,o));runDestructors(destructors)}});return[{name:reg.name,\"fromWireType\":ptr=>{var rv=new Array(elementsLength);for(var i=0;i<elementsLength;++i){rv[i]=elements[i].read(ptr)}rawDestructor(ptr);return rv},\"toWireType\":(destructors,o)=>{if(elementsLength!==o.length){throw new TypeError(`Incorrect number of tuple elements for ${reg.name}: expected=${elementsLength}, actual=${o.length}`)}var ptr=rawConstructor();for(var i=0;i<elementsLength;++i){elements[i].write(ptr,o[i])}if(destructors!==null){destructors.push(rawDestructor,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:rawDestructor}]})};var __embind_register_bigint=(primitiveType,name,size,minRange,maxRange)=>{};var embind_init_charCodes=()=>{var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes};var embind_charCodes;var readLatin1String=ptr=>{var ret=\"\";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]]}return ret};var BindingError;var throwBindingError=message=>{throw new BindingError(message)};function sharedRegisterType(rawType,registeredInstance,options={}){var name=registeredInstance.name;if(!rawType){throwBindingError(`type \"${name}\" must have a positive integer typeid pointer`)}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError(`Cannot register type '${name}' twice`)}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(cb=>cb())}}function registerType(rawType,registeredInstance,options={}){if(!(\"argPackAdvance\"in registeredInstance)){throw new TypeError(\"registerType registeredInstance requires argPackAdvance\")}return sharedRegisterType(rawType,registeredInstance,options)}var GenericWireTypeSize=8;var __embind_register_bool=(rawType,name,trueValue,falseValue)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":function(wt){return!!wt},\"toWireType\":function(destructors,o){return o?trueValue:falseValue},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":function(pointer){return this[\"fromWireType\"](HEAPU8[pointer])},destructorFunction:null})};function handleAllocatorInit(){Object.assign(HandleAllocator.prototype,{get(id){return this.allocated[id]},has(id){return this.allocated[id]!==undefined},allocate(handle){var id=this.freelist.pop()||this.allocated.length;this.allocated[id]=handle;return id},free(id){this.allocated[id]=undefined;this.freelist.push(id)}})}function HandleAllocator(){this.allocated=[undefined];this.freelist=[]}var emval_handles=new HandleAllocator;var __emval_decref=handle=>{if(handle>=emval_handles.reserved&&0===--emval_handles.get(handle).refcount){emval_handles.free(handle)}};var count_emval_handles=()=>{var count=0;for(var i=emval_handles.reserved;i<emval_handles.allocated.length;++i){if(emval_handles.allocated[i]!==undefined){++count}}return count};var init_emval=()=>{emval_handles.allocated.push({value:undefined},{value:null},{value:true},{value:false});emval_handles.reserved=emval_handles.allocated.length;Module[\"count_emval_handles\"]=count_emval_handles};var Emval={toValue:handle=>{if(!handle){throwBindingError(\"Cannot use deleted val. handle = \"+handle)}return emval_handles.get(handle).value},toHandle:value=>{switch(value){case undefined:return 1;case null:return 2;case true:return 3;case false:return 4;default:{return emval_handles.allocate({refcount:1,value:value})}}}};var __embind_register_emval=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":handle=>{var rv=Emval.toValue(handle);__emval_decref(handle);return rv},\"toWireType\":(destructors,value)=>Emval.toHandle(value),\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:null})};var ensureOverloadTable=(proto,methodName,humanName)=>{if(undefined===proto[methodName].overloadTable){var prevFunc=proto[methodName];proto[methodName]=function(){if(!proto[methodName].overloadTable.hasOwnProperty(arguments.length)){throwBindingError(`Function '${humanName}' called with an invalid number of arguments (${arguments.length}) - expects one of (${proto[methodName].overloadTable})!`)}return proto[methodName].overloadTable[arguments.length].apply(this,arguments)};proto[methodName].overloadTable=[];proto[methodName].overloadTable[prevFunc.argCount]=prevFunc}};var exposePublicSymbol=(name,value,numArguments)=>{if(Module.hasOwnProperty(name)){if(undefined===numArguments||undefined!==Module[name].overloadTable&&undefined!==Module[name].overloadTable[numArguments]){throwBindingError(`Cannot register public name '${name}' twice`)}ensureOverloadTable(Module,name,name);if(Module.hasOwnProperty(numArguments)){throwBindingError(`Cannot register multiple overloads of a function with the same number of arguments (${numArguments})!`)}Module[name].overloadTable[numArguments]=value}else{Module[name]=value;if(undefined!==numArguments){Module[name].numArguments=numArguments}}};var enumReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?function(pointer){return this[\"fromWireType\"](HEAP8[pointer>>0])}:function(pointer){return this[\"fromWireType\"](HEAPU8[pointer>>0])};case 2:return signed?function(pointer){return this[\"fromWireType\"](HEAP16[pointer>>1])}:function(pointer){return this[\"fromWireType\"](HEAPU16[pointer>>1])};case 4:return signed?function(pointer){return this[\"fromWireType\"](HEAP32[pointer>>2])}:function(pointer){return this[\"fromWireType\"](HEAPU32[pointer>>2])};default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_enum=(rawType,name,size,isSigned)=>{name=readLatin1String(name);function ctor(){}ctor.values={};registerType(rawType,{name:name,constructor:ctor,\"fromWireType\":function(c){return this.constructor.values[c]},\"toWireType\":(destructors,c)=>c.value,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":enumReadValueFromPointer(name,size,isSigned),destructorFunction:null});exposePublicSymbol(name,ctor)};var createNamedFunction=(name,body)=>Object.defineProperty(body,\"name\",{value:name});var getTypeName=type=>{var ptr=___getTypeName(type);var rv=readLatin1String(ptr);_free(ptr);return rv};var requireRegisteredType=(rawType,humanName)=>{var impl=registeredTypes[rawType];if(undefined===impl){throwBindingError(humanName+\" has unknown type \"+getTypeName(rawType))}return impl};var __embind_register_enum_value=(rawEnumType,name,enumValue)=>{var enumType=requireRegisteredType(rawEnumType,\"enum\");name=readLatin1String(name);var Enum=enumType.constructor;var Value=Object.create(enumType.constructor.prototype,{value:{value:enumValue},constructor:{value:createNamedFunction(`${enumType.name}_${name}`,function(){})}});Enum.values[enumValue]=Value;Enum[name]=Value};var floatReadValueFromPointer=(name,width)=>{switch(width){case 4:return function(pointer){return this[\"fromWireType\"](HEAPF32[pointer>>2])};case 8:return function(pointer){return this[\"fromWireType\"](HEAPF64[pointer>>3])};default:throw new TypeError(`invalid float width (${width}): ${name}`)}};var __embind_register_float=(rawType,name,size)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":value=>value,\"toWireType\":(destructors,value)=>value,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":floatReadValueFromPointer(name,size),destructorFunction:null})};function newFunc(constructor,argumentList){if(!(constructor instanceof Function)){throw new TypeError(`new_ called with constructor type ${typeof constructor} which is not a function`)}var dummy=createNamedFunction(constructor.name||\"unknownFunctionName\",function(){});dummy.prototype=constructor.prototype;var obj=new dummy;var r=constructor.apply(obj,argumentList);return r instanceof Object?r:obj}function craftInvokerFunction(humanName,argTypes,classType,cppInvokerFunc,cppTargetFunc,isAsync){var argCount=argTypes.length;if(argCount<2){throwBindingError(\"argTypes array size mismatch! Must at least get return value and 'this' types!\")}var isClassMethodFunc=argTypes[1]!==null&&classType!==null;var needsDestructorStack=false;for(var i=1;i<argTypes.length;++i){if(argTypes[i]!==null&&argTypes[i].destructorFunction===undefined){needsDestructorStack=true;break}}var returns=argTypes[0].name!==\"void\";var argsList=\"\";var argsListWired=\"\";for(var i=0;i<argCount-2;++i){argsList+=(i!==0?\", \":\"\")+\"arg\"+i;argsListWired+=(i!==0?\", \":\"\")+\"arg\"+i+\"Wired\"}var invokerFnBody=`\\n        return function (${argsList}) {\\n        if (arguments.length !== ${argCount-2}) {\\n          throwBindingError('function ${humanName} called with ' + arguments.length + ' arguments, expected ${argCount-2}');\\n        }`;if(needsDestructorStack){invokerFnBody+=\"var destructors = [];\\n\"}var dtorStack=needsDestructorStack?\"destructors\":\"null\";var args1=[\"throwBindingError\",\"invoker\",\"fn\",\"runDestructors\",\"retType\",\"classParam\"];var args2=[throwBindingError,cppInvokerFunc,cppTargetFunc,runDestructors,argTypes[0],argTypes[1]];if(isClassMethodFunc){invokerFnBody+=\"var thisWired = classParam.toWireType(\"+dtorStack+\", this);\\n\"}for(var i=0;i<argCount-2;++i){invokerFnBody+=\"var arg\"+i+\"Wired = argType\"+i+\".toWireType(\"+dtorStack+\", arg\"+i+\"); // \"+argTypes[i+2].name+\"\\n\";args1.push(\"argType\"+i);args2.push(argTypes[i+2])}if(isClassMethodFunc){argsListWired=\"thisWired\"+(argsListWired.length>0?\", \":\"\")+argsListWired}invokerFnBody+=(returns||isAsync?\"var rv = \":\"\")+\"invoker(fn\"+(argsListWired.length>0?\", \":\"\")+argsListWired+\");\\n\";if(needsDestructorStack){invokerFnBody+=\"runDestructors(destructors);\\n\"}else{for(var i=isClassMethodFunc?1:2;i<argTypes.length;++i){var paramName=i===1?\"thisWired\":\"arg\"+(i-2)+\"Wired\";if(argTypes[i].destructorFunction!==null){invokerFnBody+=paramName+\"_dtor(\"+paramName+\"); // \"+argTypes[i].name+\"\\n\";args1.push(paramName+\"_dtor\");args2.push(argTypes[i].destructorFunction)}}}if(returns){invokerFnBody+=\"var ret = retType.fromWireType(rv);\\n\"+\"return ret;\\n\"}else{}invokerFnBody+=\"}\\n\";args1.push(invokerFnBody);var invokerFn=newFunc(Function,args1).apply(null,args2);return createNamedFunction(humanName,invokerFn)}var heap32VectorToArray=(count,firstElement)=>{var array=[];for(var i=0;i<count;i++){array.push(HEAPU32[firstElement+i*4>>2])}return array};var replacePublicSymbol=(name,value,numArguments)=>{if(!Module.hasOwnProperty(name)){throwInternalError(\"Replacing nonexistant public symbol\")}if(undefined!==Module[name].overloadTable&&undefined!==numArguments){Module[name].overloadTable[numArguments]=value}else{Module[name]=value;Module[name].argCount=numArguments}};var dynCallLegacy=(sig,ptr,args)=>{var f=Module[\"dynCall_\"+sig];return args&&args.length?f.apply(null,[ptr].concat(args)):f.call(null,ptr)};var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};var dynCall=(sig,ptr,args)=>{if(sig.includes(\"j\")){return dynCallLegacy(sig,ptr,args)}var rtn=getWasmTableEntry(ptr).apply(null,args);return rtn};var getDynCaller=(sig,ptr)=>{var argCache=[];return function(){argCache.length=0;Object.assign(argCache,arguments);return dynCall(sig,ptr,argCache)}};var embind__requireFunction=(signature,rawFunction)=>{signature=readLatin1String(signature);function makeDynCaller(){if(signature.includes(\"j\")){return getDynCaller(signature,rawFunction)}return getWasmTableEntry(rawFunction)}var fp=makeDynCaller();if(typeof fp!=\"function\"){throwBindingError(`unknown function pointer with signature ${signature}: ${rawFunction}`)}return fp};var extendError=(baseErrorType,errorName)=>{var errorClass=createNamedFunction(errorName,function(message){this.name=errorName;this.message=message;var stack=new Error(message).stack;if(stack!==undefined){this.stack=this.toString()+\"\\n\"+stack.replace(/^Error(:[^\\n]*)?\\n/,\"\")}});errorClass.prototype=Object.create(baseErrorType.prototype);errorClass.prototype.constructor=errorClass;errorClass.prototype.toString=function(){if(this.message===undefined){return this.name}else{return`${this.name}: ${this.message}`}};return errorClass};var UnboundTypeError;var throwUnboundTypeError=(message,types)=>{var unboundTypes=[];var seen={};function visit(type){if(seen[type]){return}if(registeredTypes[type]){return}if(typeDependencies[type]){typeDependencies[type].forEach(visit);return}unboundTypes.push(type);seen[type]=true}types.forEach(visit);throw new UnboundTypeError(`${message}: `+unboundTypes.map(getTypeName).join([\", \"]))};var getFunctionName=signature=>{signature=signature.trim();const argsIndex=signature.indexOf(\"(\");if(argsIndex!==-1){return signature.substr(0,argsIndex)}else{return signature}};var __embind_register_function=(name,argCount,rawArgTypesAddr,signature,rawInvoker,fn,isAsync)=>{var argTypes=heap32VectorToArray(argCount,rawArgTypesAddr);name=readLatin1String(name);name=getFunctionName(name);rawInvoker=embind__requireFunction(signature,rawInvoker);exposePublicSymbol(name,function(){throwUnboundTypeError(`Cannot call ${name} due to unbound types`,argTypes)},argCount-1);whenDependentTypesAreResolved([],argTypes,function(argTypes){var invokerArgsArray=[argTypes[0],null].concat(argTypes.slice(1));replacePublicSymbol(name,craftInvokerFunction(name,invokerArgsArray,null,rawInvoker,fn,isAsync),argCount-1);return[]})};var integerReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?pointer=>HEAP8[pointer>>0]:pointer=>HEAPU8[pointer>>0];case 2:return signed?pointer=>HEAP16[pointer>>1]:pointer=>HEAPU16[pointer>>1];case 4:return signed?pointer=>HEAP32[pointer>>2]:pointer=>HEAPU32[pointer>>2];default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_integer=(primitiveType,name,size,minRange,maxRange)=>{name=readLatin1String(name);if(maxRange===-1){maxRange=4294967295}var fromWireType=value=>value;if(minRange===0){var bitshift=32-8*size;fromWireType=value=>value<<bitshift>>>bitshift}var isUnsignedType=name.includes(\"unsigned\");var checkAssertions=(value,toTypeName)=>{};var toWireType;if(isUnsignedType){toWireType=function(destructors,value){checkAssertions(value,this.name);return value>>>0}}else{toWireType=function(destructors,value){checkAssertions(value,this.name);return value}}registerType(primitiveType,{name:name,\"fromWireType\":fromWireType,\"toWireType\":toWireType,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":integerReadValueFromPointer(name,size,minRange!==0),destructorFunction:null})};var __embind_register_memory_view=(rawType,dataTypeIndex,name)=>{var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){var size=HEAPU32[handle>>2];var data=HEAPU32[handle+4>>2];return new TA(HEAP8.buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":decodeMemoryView,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":decodeMemoryView},{ignoreDuplicateRegistrations:true})};function readPointer(pointer){return this[\"fromWireType\"](HEAPU32[pointer>>2])}var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var UTF8Decoder=typeof TextDecoder!=\"undefined\"?new TextDecoder(\"utf8\"):undefined;var UTF8ArrayToString=(heapOrArray,idx,maxBytesToRead)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str=\"\";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\";var __embind_register_std_string=(rawType,name)=>{name=readLatin1String(name);var stdStringIsUTF8=name===\"std::string\";registerType(rawType,{name:name,\"fromWireType\"(value){var length=HEAPU32[value>>2];var payload=value+4;var str;if(stdStringIsUTF8){var decodeStartPtr=payload;for(var i=0;i<=length;++i){var currentBytePtr=payload+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[payload+i])}str=a.join(\"\")}_free(value);return str},\"toWireType\"(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var length;var valueIsOfTypeString=typeof value==\"string\";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError(\"Cannot pass non-string to std::string\")}if(stdStringIsUTF8&&valueIsOfTypeString){length=lengthBytesUTF8(value)}else{length=value.length}var base=_malloc(4+length+1);var ptr=base+4;HEAPU32[base>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr,length+1)}else{if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError(\"String has UTF-16 code units that do not fit in 8 bits\")}HEAPU8[ptr+i]=charCode}}else{for(var i=0;i<length;++i){HEAPU8[ptr+i]=value[i]}}}if(destructors!==null){destructors.push(_free,base)}return base},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":readPointer,destructorFunction(ptr){_free(ptr)}})};var UTF16Decoder=typeof TextDecoder!=\"undefined\"?new TextDecoder(\"utf-16le\"):undefined;var UTF16ToString=(ptr,maxBytesToRead)=>{var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&HEAPU16[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder)return UTF16Decoder.decode(HEAPU8.subarray(ptr,endPtr));var str=\"\";for(var i=0;!(i>=maxBytesToRead/2);++i){var codeUnit=HEAP16[ptr+i*2>>1];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit)}return str};var stringToUTF16=(str,outPtr,maxBytesToWrite)=>{if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2}HEAP16[outPtr>>1]=0;return outPtr-startPtr};var lengthBytesUTF16=str=>str.length*2;var UTF32ToString=(ptr,maxBytesToRead)=>{var i=0;var str=\"\";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str};var stringToUTF32=(str,outPtr,maxBytesToWrite)=>{if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr};var lengthBytesUTF32=str=>{var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len};var __embind_register_std_wstring=(rawType,charSize,name)=>{name=readLatin1String(name);var decodeString,encodeString,getHeap,lengthBytesUTF,shift;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;getHeap=()=>HEAPU16;shift=1}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;getHeap=()=>HEAPU32;shift=2}registerType(rawType,{name:name,\"fromWireType\":value=>{var length=HEAPU32[value>>2];var HEAP=getHeap();var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||HEAP[currentBytePtr>>shift]==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},\"toWireType\":(destructors,value)=>{if(!(typeof value==\"string\")){throwBindingError(`Cannot pass non-string to C++ string type ${name}`)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length>>shift;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction(ptr){_free(ptr)}})};var __embind_register_value_array=(rawType,name,constructorSignature,rawConstructor,destructorSignature,rawDestructor)=>{tupleRegistrations[rawType]={name:readLatin1String(name),rawConstructor:embind__requireFunction(constructorSignature,rawConstructor),rawDestructor:embind__requireFunction(destructorSignature,rawDestructor),elements:[]}};var __embind_register_value_array_element=(rawTupleType,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext)=>{tupleRegistrations[rawTupleType].elements.push({getterReturnType:getterReturnType,getter:embind__requireFunction(getterSignature,getter),getterContext:getterContext,setterArgumentType:setterArgumentType,setter:embind__requireFunction(setterSignature,setter),setterContext:setterContext})};var __embind_register_void=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,\"argPackAdvance\":0,\"fromWireType\":()=>undefined,\"toWireType\":(destructors,o)=>undefined})};var _abort=()=>{abort(\"\")};var _emscripten_memcpy_js=(dest,src,num)=>HEAPU8.copyWithin(dest,src,src+num);var getHeapMax=()=>2147483648;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false};var ENV={};var getExecutableName=()=>thisProgram||\"./this.program\";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator==\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\";var env={\"USER\":\"web_user\",\"LOGNAME\":\"web_user\",\"PATH\":\"/\",\"PWD\":\"/\",\"HOME\":\"/home/<USER>",\"LANG\":lang,\"_\":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}HEAP8[buffer>>0]=0};var PATH={isAbs:path=>path.charAt(0)===\"/\",splitPath:filename=>{var splitPathRe=/^(\\/?|)([\\s\\S]*?)((?:\\.{1,2}|[^\\/]+?|)(\\.[^.\\/]*|))(?:[\\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last===\".\"){parts.splice(i,1)}else if(last===\"..\"){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift(\"..\")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.substr(-1)===\"/\";path=PATH.normalizeArray(path.split(\"/\").filter(p=>!!p),!isAbsolute).join(\"/\");if(!path&&!isAbsolute){path=\".\"}if(path&&trailingSlash){path+=\"/\"}return(isAbsolute?\"/\":\"\")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return\".\"}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir},basename:path=>{if(path===\"/\")return\"/\";path=PATH.normalize(path);path=path.replace(/\\/$/,\"\");var lastSlash=path.lastIndexOf(\"/\");if(lastSlash===-1)return path;return path.substr(lastSlash+1)},join:function(){var paths=Array.prototype.slice.call(arguments);return PATH.normalize(paths.join(\"/\"))},join2:(l,r)=>PATH.normalize(l+\"/\"+r)};var initRandomFill=()=>{if(typeof crypto==\"object\"&&typeof crypto[\"getRandomValues\"]==\"function\"){return view=>crypto.getRandomValues(view)}else abort(\"initRandomDevice\")};var randomFill=view=>(randomFill=initRandomFill())(view);var PATH_FS={resolve:function(){var resolvedPath=\"\",resolvedAbsolute=false;for(var i=arguments.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?arguments[i]:FS.cwd();if(typeof path!=\"string\"){throw new TypeError(\"Arguments to path.resolve must be strings\")}else if(!path){return\"\"}resolvedPath=path+\"/\"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split(\"/\").filter(p=>!!p),!resolvedAbsolute).join(\"/\");return(resolvedAbsolute?\"/\":\"\")+resolvedPath||\".\"},relative:(from,to)=>{from=PATH_FS.resolve(from).substr(1);to=PATH_FS.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!==\"\")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!==\"\")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split(\"/\"));var toParts=trim(to.split(\"/\"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push(\"..\")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join(\"/\")}};var FS_stdin_getChar_buffer=[];function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var FS_stdin_getChar=()=>{if(!FS_stdin_getChar_buffer.length){var result=null;if(typeof window!=\"undefined\"&&typeof window.prompt==\"function\"){result=window.prompt(\"Input: \");if(result!==null){result+=\"\\n\"}}else if(typeof readline==\"function\"){result=readline();if(result!==null){result+=\"\\n\"}}if(!result){return null}FS_stdin_getChar_buffer=intArrayFromString(result,true)}return FS_stdin_getChar_buffer.shift()};var TTY={ttys:[],init(){},shutdown(){},register(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close(stream){stream.tty.ops.fsync(stream.tty)},fsync(stream){stream.tty.ops.fsync(stream.tty)},read(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.timestamp=Date.now()}return i}},default_tty_ops:{get_char(tty){return FS_stdin_getChar()},put_char(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}},ioctl_tcgets(tty){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets(tty,optional_actions,data){return 0},ioctl_tiocgwinsz(tty){return[24,80]}},default_tty1_ops:{put_char(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}}}};var mmapAlloc=size=>{abort()};var MEMFS={ops_table:null,mount(mount){return MEMFS.createNode(null,\"/\",16384|511,0)},createNode(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}if(!MEMFS.ops_table){MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}}}var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node;parent.timestamp=node.timestamp}return node},getFileDataAsTypedArray(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup(parent,name){throw FS.genericErrors[44]},mknod(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}}delete old_node.parent.contents[old_node.name];old_node.parent.timestamp=Date.now();old_node.name=new_name;new_dir.contents[new_name]=old_node;new_dir.timestamp=old_node.parent.timestamp;old_node.parent=new_dir},unlink(parent,name){delete parent.contents[name];parent.timestamp=Date.now()},rmdir(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.timestamp=Date.now()},readdir(node){var entries=[\".\",\"..\"];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries},symlink(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write(stream,buffer,offset,length,position,canOwn){if(buffer.buffer===HEAP8.buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},allocate(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)},mmap(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents.buffer===HEAP8.buffer){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}HEAP8.set(contents,ptr)}return{ptr:ptr,allocated:allocated}},msync(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};var asyncLoad=(url,onload,onerror,noRunDep)=>{var dep=!noRunDep?getUniqueRunDependency(`al ${url}`):\"\";readAsync(url,arrayBuffer=>{assert(arrayBuffer,`Loading data file \"${url}\" failed (no arrayBuffer).`);onload(new Uint8Array(arrayBuffer));if(dep)removeRunDependency(dep)},event=>{if(onerror){onerror()}else{throw`Loading data file \"${url}\" failed.`}});if(dep)addRunDependency(dep)};var FS_createDataFile=(parent,name,fileData,canRead,canWrite,canOwn)=>{FS.createDataFile(parent,name,fileData,canRead,canWrite,canOwn)};var preloadPlugins=Module[\"preloadPlugins\"]||[];var FS_handledByPreloadPlugin=(byteArray,fullname,finish,onerror)=>{if(typeof Browser!=\"undefined\")Browser.init();var handled=false;preloadPlugins.forEach(plugin=>{if(handled)return;if(plugin[\"canHandle\"](fullname)){plugin[\"handle\"](byteArray,fullname,finish,onerror);handled=true}});return handled};var FS_createPreloadedFile=(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish)=>{var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){if(preFinish)preFinish();if(!dontCreateFile){FS_createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}if(onload)onload();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{if(onerror)onerror();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url==\"string\"){asyncLoad(url,byteArray=>processData(byteArray),onerror)}else{processData(url)}};var FS_modeStringToFlags=str=>{var flagModes={\"r\":0,\"r+\":2,\"w\":512|64|1,\"w+\":512|64|2,\"a\":1024|64|1,\"a+\":1024|64|2};var flags=flagModes[str];if(typeof flags==\"undefined\"){throw new Error(`Unknown file open mode: ${str}`)}return flags};var FS_getMode=(canRead,canWrite)=>{var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:\"/\",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath(path,opts={}){path=PATH_FS.resolve(path);if(!path)return{path:\"\",node:null};var defaults={follow_mount:true,recurse_count:0};opts=Object.assign(defaults,opts);if(opts.recurse_count>8){throw new FS.ErrnoError(32)}var parts=path.split(\"/\").filter(p=>!!p);var current=FS.root;var current_path=\"/\";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH_FS.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count+1});current=lookup.node;if(count++>40){throw new FS.ErrnoError(32)}}}}return{path:current_path,node:current}},getPath(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!==\"/\"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode(parent,name){var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode,parent)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode(parent,name,mode,rdev){var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode(node){FS.hashRemoveNode(node)},isRoot(node){return node===node.parent},isMountpoint(node){return!!node.mounted},isFile(mode){return(mode&61440)===32768},isDir(mode){return(mode&61440)===16384},isLink(mode){return(mode&61440)===40960},isChrdev(mode){return(mode&61440)===8192},isBlkdev(mode){return(mode&61440)===24576},isFIFO(mode){return(mode&61440)===4096},isSocket(mode){return(mode&49152)===49152},flagsToPermissionString(flag){var perms=[\"r\",\"w\",\"rw\"][flag&3];if(flag&512){perms+=\"w\"}return perms},nodePermissions(node,perms){if(FS.ignorePermissions){return 0}if(perms.includes(\"r\")&&!(node.mode&292)){return 2}else if(perms.includes(\"w\")&&!(node.mode&146)){return 2}else if(perms.includes(\"x\")&&!(node.mode&73)){return 2}return 0},mayLookup(dir){var errCode=FS.nodePermissions(dir,\"x\");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate(dir,name){try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,\"wx\")},mayDelete(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,\"wx\");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen(node,flags){if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!==\"r\"||flags&512){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},MAX_OPEN_FDS:4096,nextfd(){for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStreamChecked(fd){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}return stream},getStream:fd=>FS.streams[fd],createStream(stream,fd=-1){if(!FS.FSStream){FS.FSStream=function(){this.shared={}};FS.FSStream.prototype={};Object.defineProperties(FS.FSStream.prototype,{object:{get(){return this.node},set(val){this.node=val}},isRead:{get(){return(this.flags&2097155)!==1}},isWrite:{get(){return(this.flags&2097155)!==0}},isAppend:{get(){return this.flags&1024}},flags:{get(){return this.shared.flags},set(val){this.shared.flags=val}},position:{get(){return this.shared.position},set(val){this.shared.position=val}}})}stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream(fd){FS.streams[fd]=null},chrdev_stream_ops:{open(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;if(stream.stream_ops.open){stream.stream_ops.open(stream)}},llseek(){throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice(dev,ops){FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push.apply(check,m.mounts)}return mounts},syncfs(populate,callback){if(typeof populate==\"function\"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount(type,opts,mountpoint){var root=mountpoint===\"/\";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);node.mount.mounts.splice(idx,1)},lookup(parent,name){return parent.node_ops.lookup(parent,name)},mknod(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name===\".\"||name===\"..\"){throw new FS.ErrnoError(28)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},create(path,mode){mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir(path,mode){mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree(path,mode){var dirs=path.split(\"/\");var d=\"\";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+=\"/\"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev(path,mode,dev){if(typeof dev==\"undefined\"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink(oldpath,newpath){if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!==\".\"){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!==\".\"){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,\"w\");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(54)}return node.node_ops.readdir(node)},unlink(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return PATH_FS.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))},stat(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(44)}if(!node.node_ops.getattr){throw new FS.ErrnoError(63)}return node.node_ops.getattr(node)},lstat(path){return FS.stat(path,true)},chmod(path,mode,dontFollow){var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})},lchmod(path,mode){FS.chmod(path,mode,true)},fchmod(fd,mode){var stream=FS.getStreamChecked(fd);FS.chmod(stream.node,mode)},chown(path,uid,gid,dontFollow){var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{timestamp:Date.now()})},lchown(path,uid,gid){FS.chown(path,uid,gid,true)},fchown(fd,uid,gid){var stream=FS.getStreamChecked(fd);FS.chown(stream.node,uid,gid)},truncate(path,len){if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,\"w\");if(errCode){throw new FS.ErrnoError(errCode)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})},ftruncate(fd,len){var stream=FS.getStreamChecked(fd);if((stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.truncate(stream.node,len)},utime(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})},open(path,flags,mode){if(path===\"\"){throw new FS.ErrnoError(44)}flags=typeof flags==\"string\"?FS_modeStringToFlags(flags):flags;mode=typeof mode==\"undefined\"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path==\"object\"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module[\"logReadFiles\"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed(stream){return stream.fd===null},llseek(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read(stream,buffer,offset,length,position){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!=\"undefined\";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write(stream,buffer,offset,length,position,canOwn){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!=\"undefined\";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},allocate(stream,offset,length){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(offset<0||length<=0){throw new FS.ErrnoError(28)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(43)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(138)}stream.stream_ops.allocate(stream,offset,length)},mmap(stream,length,position,prot,flags){if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync(stream,buffer,offset,length,mmapFlags){if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},munmap:stream=>0,ioctl(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile(path,opts={}){opts.flags=opts.flags||0;opts.encoding=opts.encoding||\"binary\";if(opts.encoding!==\"utf8\"&&opts.encoding!==\"binary\"){throw new Error(`Invalid encoding type \"${opts.encoding}\"`)}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding===\"utf8\"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding===\"binary\"){ret=buf}FS.close(stream);return ret},writeFile(path,data,opts={}){opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data==\"string\"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error(\"Unsupported data type\")}FS.close(stream)},cwd:()=>FS.currentPath,chdir(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,\"x\");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories(){FS.mkdir(\"/tmp\");FS.mkdir(\"/home\");FS.mkdir(\"/home/<USER>")},createDefaultDevices(){FS.mkdir(\"/dev\");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length});FS.mkdev(\"/dev/null\",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev(\"/dev/tty\",FS.makedev(5,0));FS.mkdev(\"/dev/tty1\",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomLeft=randomFill(randomBuffer).byteLength}return randomBuffer[--randomLeft]};FS.createDevice(\"/dev\",\"random\",randomByte);FS.createDevice(\"/dev\",\"urandom\",randomByte);FS.mkdir(\"/dev/shm\");FS.mkdir(\"/dev/shm/tmp\")},createSpecialDirectories(){FS.mkdir(\"/proc\");var proc_self=FS.mkdir(\"/proc/self\");FS.mkdir(\"/proc/self/fd\");FS.mount({mount(){var node=FS.createNode(proc_self,\"fd\",16384|511,73);node.node_ops={lookup(parent,name){var fd=+name;var stream=FS.getStreamChecked(fd);var ret={parent:null,mount:{mountpoint:\"fake\"},node_ops:{readlink:()=>stream.path}};ret.parent=ret;return ret}};return node}},{},\"/proc/self/fd\")},createStandardStreams(){if(Module[\"stdin\"]){FS.createDevice(\"/dev\",\"stdin\",Module[\"stdin\"])}else{FS.symlink(\"/dev/tty\",\"/dev/stdin\")}if(Module[\"stdout\"]){FS.createDevice(\"/dev\",\"stdout\",null,Module[\"stdout\"])}else{FS.symlink(\"/dev/tty\",\"/dev/stdout\")}if(Module[\"stderr\"]){FS.createDevice(\"/dev\",\"stderr\",null,Module[\"stderr\"])}else{FS.symlink(\"/dev/tty1\",\"/dev/stderr\")}var stdin=FS.open(\"/dev/stdin\",0);var stdout=FS.open(\"/dev/stdout\",1);var stderr=FS.open(\"/dev/stderr\",1)},ensureErrnoError(){if(FS.ErrnoError)return;FS.ErrnoError=function ErrnoError(errno,node){this.name=\"ErrnoError\";this.node=node;this.setErrno=function(errno){this.errno=errno};this.setErrno(errno);this.message=\"FS error\"};FS.ErrnoError.prototype=new Error;FS.ErrnoError.prototype.constructor=FS.ErrnoError;[44].forEach(code=>{FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack=\"<generic error, no stack>\"})},staticInit(){FS.ensureErrnoError();FS.nameTable=new Array(4096);FS.mount(MEMFS,{},\"/\");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={\"MEMFS\":MEMFS}},init(input,output,error){FS.init.initialized=true;FS.ensureErrnoError();Module[\"stdin\"]=input||Module[\"stdin\"];Module[\"stdout\"]=output||Module[\"stdout\"];Module[\"stderr\"]=error||Module[\"stderr\"];FS.createStandardStreams()},quit(){FS.init.initialized=false;for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}},findObject(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path===\"/\"}catch(e){ret.error=e.errno}return ret},createPath(parent,path,canRead,canWrite){parent=typeof parent==\"string\"?parent:FS.getPath(parent);var parts=path.split(\"/\").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current},createFile(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent==\"string\"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile(parent,name,data,canRead,canWrite,canOwn){var path=name;if(parent){parent=typeof parent==\"string\"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data==\"string\"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}},createDevice(parent,name,input,output){var path=PATH.join2(typeof parent==\"string\"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open(stream){stream.seekable=false},close(stream){if(output&&output.buffer&&output.buffer.length){output(10)}},read(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.timestamp=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!=\"undefined\"){throw new Error(\"Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.\")}else if(read_){try{obj.contents=intArrayFromString(read_(obj.url),true);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}else{throw new Error(\"Cannot load without read() or XMLHttpRequest.\")}},createLazyFile(parent,name,url,canRead,canWrite){function LazyUint8Array(){this.lengthKnown=false;this.chunks=[]}LazyUint8Array.prototype.get=function LazyUint8Array_get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]};LazyUint8Array.prototype.setDataGetter=function LazyUint8Array_setDataGetter(getter){this.getter=getter};LazyUint8Array.prototype.cacheLength=function LazyUint8Array_cacheLength(){var xhr=new XMLHttpRequest;xhr.open(\"HEAD\",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error(\"Couldn't load \"+url+\". Status: \"+xhr.status);var datalength=Number(xhr.getResponseHeader(\"Content-length\"));var header;var hasByteServing=(header=xhr.getResponseHeader(\"Accept-Ranges\"))&&header===\"bytes\";var usesGzip=(header=xhr.getResponseHeader(\"Content-Encoding\"))&&header===\"gzip\";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error(\"invalid range (\"+from+\", \"+to+\") or no bytes requested!\");if(to>datalength-1)throw new Error(\"only \"+datalength+\" bytes available! programmer error!\");var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);if(datalength!==chunkSize)xhr.setRequestHeader(\"Range\",\"bytes=\"+from+\"-\"+to);xhr.responseType=\"arraybuffer\";if(xhr.overrideMimeType){xhr.overrideMimeType(\"text/plain; charset=x-user-defined\")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error(\"Couldn't load \"+url+\". Status: \"+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||\"\",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]==\"undefined\"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]==\"undefined\")throw new Error(\"doXHR failed!\");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out(\"LazyFiles on gzip forces download of the whole file when length is accessed\")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true};if(typeof XMLHttpRequest!=\"undefined\"){if(!ENVIRONMENT_IS_WORKER)throw\"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc\";var lazyArray=new LazyUint8Array;Object.defineProperties(lazyArray,{length:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._length}},chunkSize:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}});var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=function forceLoadLazyFile(){FS.forceLoadFile(node);return fn.apply(null,arguments)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,HEAP8,ptr,length,position);return{ptr:ptr,allocated:true}};node.stream_ops=stream_ops;return node}};var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return PATH.join2(dir,path)},doStat(func,path,buf){try{var stat=func(path)}catch(e){if(e&&e.node&&PATH.normalize(path)!==PATH.normalize(FS.getPath(e.node))){return-54}throw e}HEAP32[buf>>2]=stat.dev;HEAP32[buf+4>>2]=stat.mode;HEAPU32[buf+8>>2]=stat.nlink;HEAP32[buf+12>>2]=stat.uid;HEAP32[buf+16>>2]=stat.gid;HEAP32[buf+20>>2]=stat.rdev;tempI64=[stat.size>>>0,(tempDouble=stat.size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+24>>2]=tempI64[0],HEAP32[buf+28>>2]=tempI64[1];HEAP32[buf+32>>2]=4096;HEAP32[buf+36>>2]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();tempI64=[Math.floor(atime/1e3)>>>0,(tempDouble=Math.floor(atime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+40>>2]=tempI64[0],HEAP32[buf+44>>2]=tempI64[1];HEAPU32[buf+48>>2]=atime%1e3*1e3;tempI64=[Math.floor(mtime/1e3)>>>0,(tempDouble=Math.floor(mtime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+56>>2]=tempI64[0],HEAP32[buf+60>>2]=tempI64[1];HEAPU32[buf+64>>2]=mtime%1e3*1e3;tempI64=[Math.floor(ctime/1e3)>>>0,(tempDouble=Math.floor(ctime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+72>>2]=tempI64[0],HEAP32[buf+76>>2]=tempI64[1];HEAPU32[buf+80>>2]=ctime%1e3*1e3;tempI64=[stat.ino>>>0,(tempDouble=stat.ino,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+88>>2]=tempI64[0],HEAP32[buf+92>>2]=tempI64[1];return 0},doMsync(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=HEAPU8.slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},varargs:undefined,get(){var ret=HEAP32[+SYSCALLS.varargs>>2];SYSCALLS.varargs+=4;return ret},getp(){return SYSCALLS.get()},getStr(ptr){var ret=UTF8ToString(ptr);return ret},getStreamFromFD(fd){var stream=FS.getStreamChecked(fd);return stream}};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;getEnvStrings().forEach((string,i)=>{var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(string=>bufSize+=string.length+1);HEAPU32[penviron_buf_size>>2]=bufSize;return 0};function _fd_close(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var doReadv=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.read(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break;if(typeof offset!==\"undefined\"){offset+=curr}}return ret};function _fd_read(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var convertI32PairToI53Checked=(lo,hi)=>hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN;function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);try{if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);tempI64=[stream.position>>>0,(tempDouble=stream.position,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[newOffset>>2]=tempI64[0],HEAP32[newOffset+4>>2]=tempI64[1];if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var doWritev=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(typeof offset!==\"undefined\"){offset+=curr}}return ret};function _fd_write(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var isLeapYear=year=>year%4===0&&(year%100!==0||year%400===0);var arraySum=(array,index)=>{var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum};var MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];var addDays=(date,days)=>{var newDate=new Date(date.getTime());while(days>0){var leap=isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate};var writeArrayToMemory=(array,buffer)=>{HEAP8.set(array,buffer)};var _strftime=(s,maxsize,format,tm)=>{var tm_zone=HEAPU32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):\"\"};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_1[rule])}var WEEKDAYS=[\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"];var MONTHS=[\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];function leadingSomething(value,digits,character){var str=typeof value==\"number\"?value.toString():value||\"\";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,\"0\")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}return thisDate.getFullYear()}return thisDate.getFullYear()-1}var EXPANSION_RULES_2={\"%a\":date=>WEEKDAYS[date.tm_wday].substring(0,3),\"%A\":date=>WEEKDAYS[date.tm_wday],\"%b\":date=>MONTHS[date.tm_mon].substring(0,3),\"%B\":date=>MONTHS[date.tm_mon],\"%C\":date=>{var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},\"%d\":date=>leadingNulls(date.tm_mday,2),\"%e\":date=>leadingSomething(date.tm_mday,2,\" \"),\"%g\":date=>getWeekBasedYear(date).toString().substring(2),\"%G\":date=>getWeekBasedYear(date),\"%H\":date=>leadingNulls(date.tm_hour,2),\"%I\":date=>{var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},\"%j\":date=>leadingNulls(date.tm_mday+arraySum(isLeapYear(date.tm_year+1900)?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR,date.tm_mon-1),3),\"%m\":date=>leadingNulls(date.tm_mon+1,2),\"%M\":date=>leadingNulls(date.tm_min,2),\"%n\":()=>\"\\n\",\"%p\":date=>{if(date.tm_hour>=0&&date.tm_hour<12){return\"AM\"}return\"PM\"},\"%S\":date=>leadingNulls(date.tm_sec,2),\"%t\":()=>\"\\t\",\"%u\":date=>date.tm_wday||7,\"%U\":date=>{var days=date.tm_yday+7-date.tm_wday;return leadingNulls(Math.floor(days/7),2)},\"%V\":date=>{var val=Math.floor((date.tm_yday+7-(date.tm_wday+6)%7)/7);if((date.tm_wday+371-date.tm_yday-2)%7<=2){val++}if(!val){val=52;var dec31=(date.tm_wday+7-date.tm_yday-1)%7;if(dec31==4||dec31==5&&isLeapYear(date.tm_year%400-1)){val++}}else if(val==53){var jan1=(date.tm_wday+371-date.tm_yday)%7;if(jan1!=4&&(jan1!=3||!isLeapYear(date.tm_year)))val=1}return leadingNulls(val,2)},\"%w\":date=>date.tm_wday,\"%W\":date=>{var days=date.tm_yday+7-(date.tm_wday+6)%7;return leadingNulls(Math.floor(days/7),2)},\"%y\":date=>(date.tm_year+1900).toString().substring(2),\"%Y\":date=>date.tm_year+1900,\"%z\":date=>{var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?\"+\":\"-\")+String(\"0000\"+off).slice(-4)},\"%Z\":date=>date.tm_zone,\"%%\":()=>\"%\"};pattern=pattern.replace(/%%/g,\"\\0\\0\");for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_2[rule](date))}}pattern=pattern.replace(/\\0\\0/g,\"%\");var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1};var _strftime_l=(s,maxsize,format,tm,loc)=>_strftime(s,maxsize,format,tm);var getCFunc=ident=>{var func=Module[\"_\"+ident];return func};var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var ccall=(ident,returnType,argTypes,args,opts)=>{var toC={\"string\":str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},\"array\":arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType===\"string\"){return UTF8ToString(ret)}if(returnType===\"boolean\")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret};InternalError=Module[\"InternalError\"]=class InternalError extends Error{constructor(message){super(message);this.name=\"InternalError\"}};embind_init_charCodes();BindingError=Module[\"BindingError\"]=class BindingError extends Error{constructor(message){super(message);this.name=\"BindingError\"}};handleAllocatorInit();init_emval();UnboundTypeError=Module[\"UnboundTypeError\"]=extendError(Error,\"UnboundTypeError\");var FSNode=function(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev};var readMode=292|73;var writeMode=146;Object.defineProperties(FSNode.prototype,{read:{get:function(){return(this.mode&readMode)===readMode},set:function(val){val?this.mode|=readMode:this.mode&=~readMode}},write:{get:function(){return(this.mode&writeMode)===writeMode},set:function(val){val?this.mode|=writeMode:this.mode&=~writeMode}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}});FS.FSNode=FSNode;FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();var wasmImports={a:___cxa_throw,m:__embind_finalize_value_array,p:__embind_register_bigint,k:__embind_register_bool,y:__embind_register_emval,z:__embind_register_enum,f:__embind_register_enum_value,j:__embind_register_float,d:__embind_register_function,e:__embind_register_integer,b:__embind_register_memory_view,h:__embind_register_std_string,g:__embind_register_std_wstring,n:__embind_register_value_array,c:__embind_register_value_array_element,l:__embind_register_void,i:_abort,x:_emscripten_memcpy_js,u:_emscripten_resize_heap,r:_environ_get,s:_environ_sizes_get,w:_fd_close,t:_fd_read,o:_fd_seek,v:_fd_write,q:_strftime_l};var wasmExports=createWasm();var ___wasm_call_ctors=()=>(___wasm_call_ctors=wasmExports[\"B\"])();var ___getTypeName=a0=>(___getTypeName=wasmExports[\"D\"])(a0);var ___errno_location=()=>(___errno_location=wasmExports[\"__errno_location\"])();var _malloc=Module[\"_malloc\"]=a0=>(_malloc=Module[\"_malloc\"]=wasmExports[\"E\"])(a0);var _free=Module[\"_free\"]=a0=>(_free=Module[\"_free\"]=wasmExports[\"F\"])(a0);var stackSave=()=>(stackSave=wasmExports[\"G\"])();var stackRestore=a0=>(stackRestore=wasmExports[\"H\"])(a0);var stackAlloc=a0=>(stackAlloc=wasmExports[\"I\"])(a0);var ___cxa_increment_exception_refcount=a0=>(___cxa_increment_exception_refcount=wasmExports[\"__cxa_increment_exception_refcount\"])(a0);var ___cxa_is_pointer_type=a0=>(___cxa_is_pointer_type=wasmExports[\"J\"])(a0);var dynCall_jiji=Module[\"dynCall_jiji\"]=(a0,a1,a2,a3,a4)=>(dynCall_jiji=Module[\"dynCall_jiji\"]=wasmExports[\"K\"])(a0,a1,a2,a3,a4);var dynCall_viijii=Module[\"dynCall_viijii\"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_viijii=Module[\"dynCall_viijii\"]=wasmExports[\"L\"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_iiiiij=Module[\"dynCall_iiiiij\"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_iiiiij=Module[\"dynCall_iiiiij\"]=wasmExports[\"M\"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_iiiiijj=Module[\"dynCall_iiiiijj\"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8)=>(dynCall_iiiiijj=Module[\"dynCall_iiiiijj\"]=wasmExports[\"N\"])(a0,a1,a2,a3,a4,a5,a6,a7,a8);var dynCall_iiiiiijj=Module[\"dynCall_iiiiiijj\"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(dynCall_iiiiiijj=Module[\"dynCall_iiiiiijj\"]=wasmExports[\"O\"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);Module[\"ccall\"]=ccall;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(){if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}run();\n\n\n  return moduleArg.ready\n}\n);\n})();\n;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Module);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../node_modules/@splinetool/runtime/build/boolean.js\n"));

/***/ })

}]);
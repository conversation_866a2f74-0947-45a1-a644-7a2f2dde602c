/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBubbleMinus = createReactComponent("outline", "bubble-minus", "IconBubbleMinus", [["path", { "d": "M12.4 19a4.2 4.2 0 0 1 -1.57 -.298l-3.83 2.298v-3.134a2.668 2.668 0 0 1 -1.795 -3.773a4.8 4.8 0 0 1 2.908 -8.933a5.335 5.335 0 0 1 9.194 1.078a5.333 5.333 0 0 1 3.404 8.771", "key": "svg-0" }], ["path", { "d": "M16 19h6", "key": "svg-1" }]]);

export { IconBubbleMinus as default };
//# sourceMappingURL=IconBubbleMinus.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandMastercard = createReactComponent("outline", "brand-mastercard", "IconBrandMastercard", [["path", { "d": "M14 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M12 9.765a3 3 0 1 0 0 4.47", "key": "svg-1" }], ["path", { "d": "M3 5m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-2" }]]);

export { IconBrandMastercard as default };
//# sourceMappingURL=IconBrandMastercard.mjs.map

import React from "react";
import Link from "next/link";

const StepTwo = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 2: Set Up Scratch</h1>
        <p className="text-center mt-2 text-lg">
          Let’s get ready to use Scratch and start coding your awesome projects!
        </p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">How Do You Want to Use Scratch?</h2>
          <p className="text-lg mb-4">
            Scratch can be used **online** (with the internet) or **offline** (without the internet). Choose the option that works best for
            you!
          </p>

          {/* Fun Illustration */}
          <div className="flex justify-center mb-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 144023.png"
              alt="Scratch Setup Illustration"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Online Setup */}
          <div className="bg-blue-100 p-4 border-l-4 border-blue-500 rounded-lg mb-6">
            <h3 className="text-xl font-semibold mb-2">Option 1: Use Scratch Online</h3>
            <p className="text-lg mb-4">Here’s how to use Scratch online:</p>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                Visit the Scratch website:{" "}
                <a
                  href="https://scratch.mit.edu"
                  target="_blank"
                  rel="noreferrer"
                  className="text-blue-600 underline"
                >
                  https://scratch.mit.edu
                </a>
              </li>
              <li>Click on "Join Scratch" to create your free account. (Ask an adult to help!)</li>
              <li>Once logged in, click on "Create" and start coding your first project!</li>
            </ul>
          </div>

          {/* Offline Setup */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg mb-6">
            <h3 className="text-xl font-semibold mb-2">Option 2: Use Scratch Offline</h3>
            <p className="text-lg mb-4">No internet? No problem! Here’s how to use Scratch offline:</p>
            <ul className="list-disc pl-6 space-y-2">
              <li>
                Go to the Scratch download page:{" "}
                <a
                  href="https://scratch.mit.edu/download"
                  target="_blank"
                  rel="noreferrer"
                  className="text-blue-600 underline"
                >
                  https://scratch.mit.edu/download
                </a>
              </li>
              <li>Download Scratch for your computer (Windows, Mac, or Linux).</li>
              <li>Install Scratch and open the app. You’re ready to code offline!</li>
            </ul>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-1">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 1</button>
        </Link>
        <Link href="/lessons/level1/step-3">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 3</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepTwo;

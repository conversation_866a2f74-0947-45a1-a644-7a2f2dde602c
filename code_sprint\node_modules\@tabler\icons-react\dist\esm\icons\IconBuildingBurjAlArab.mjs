/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBuildingBurjAlArab = createReactComponent("outline", "building-burj-al-arab", "IconBuildingBurjAlArab", [["path", { "d": "M3 21h18", "key": "svg-0" }], ["path", { "d": "M7 21v-18", "key": "svg-1" }], ["path", { "d": "M7 4c5.675 .908 10 5.613 10 11.28a11 11 0 0 1 -1.605 5.72", "key": "svg-2" }], ["path", { "d": "M5 9h12", "key": "svg-3" }], ["path", { "d": "M7 13h4", "key": "svg-4" }], ["path", { "d": "M7 17h4", "key": "svg-5" }]]);

export { IconBuildingBurjAlArab as default };
//# sourceMappingURL=IconBuildingBurjAlArab.mjs.map

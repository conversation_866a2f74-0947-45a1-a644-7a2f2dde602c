/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconHierarchy2 = createReactComponent("outline", "hierarchy-2", "IconHierarchy2", [["path", { "d": "M10 3h4v4h-4z", "key": "svg-0" }], ["path", { "d": "M3 17h4v4h-4z", "key": "svg-1" }], ["path", { "d": "M17 17h4v4h-4z", "key": "svg-2" }], ["path", { "d": "M7 17l5 -4l5 4", "key": "svg-3" }], ["path", { "d": "M12 7l0 6", "key": "svg-4" }]]);

export { IconHierarchy2 as default };
//# sourceMappingURL=IconHierarchy2.mjs.map

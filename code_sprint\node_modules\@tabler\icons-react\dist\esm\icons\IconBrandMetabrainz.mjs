/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandMetabrainz = createReactComponent("outline", "brand-metabrainz", "IconBrandMetabrainz", [["path", { "d": "M3 7v10l7 4v-18z", "key": "svg-0" }], ["path", { "d": "M21 7v10l-7 4v-18z", "key": "svg-1" }]]);

export { IconBrandMetabrainz as default };
//# sourceMappingURL=IconBrandMetabrainz.mjs.map

import React from 'react';
import { useRouter } from 'next/router';

const PaymentFailed = () => {
  const router = useRouter();

  const handleRetry = () => {
    router.push('/go-premium');
  };

  const handleDashboard = () => {
    router.push('/dashboard');
  };

  const { message } = router.query;

  return (
    <div style={{ textAlign: 'center', marginTop: '50px' }}>
      <h1>Payment Failed</h1>
      <p>Unfortunately, your payment could not be processed.</p>
      {message && <p style={{ color: 'red', marginTop: '10px' }}>{message}</p>}
      <div style={{ marginTop: '20px' }}>
        <button onClick={handleRetry} style={{ marginRight: '10px', padding: '10px 20px' }}>
          Retry
        </button>
        <button onClick={handleDashboard} style={{ padding: '10px 20px' }}>
          Go to Dashboard
        </button>
      </div>
    </div>
  );
};

export default PaymentFailed;
import React from 'react';

export default function UnderstandingMeanMedianMode() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-yellow-300 via-orange-400 to-red-500 text-white p-6">
            <style>
                {`
                .highlight {
                    color: #ffe600;
                    font-weight: bold;
                }

                .example-box {
                    background-color: rgba(255, 255, 255, 0.3);
                    padding: 16px;
                    border-radius: 8px;
                    margin-top: 20px;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                }
                `}
            </style>

            <h1 className="text-4xl font-bold mb-6">Understanding Mean, Median, and Mode</h1>
            <p className="text-lg text-center max-w-3xl mb-8">
                Let&apos;s explore three magical ways to work with numbers: 
                <span className="highlight"> Mean</span>, <span className="highlight">Median</span>, and 
                <span className="highlight"> Mode</span>. These tools help us understand and compare numbers better!
            </p>

            {/* Mean Section */}
            <div className="example-box max-w-2xl">
                <h2 className="text-2xl font-bold mb-4">🌟 What is Mean?</h2>
                <p className="text-lg">
                    The <span className="highlight">Mean</span> is like sharing equally. Imagine you and your friends have candies, and you want everyone to have the same number. 
                    You count all the candies, then divide them equally among everyone.
                </p>
                <p className="text-lg mt-4">
                    For example: If your group has candies like this: <span className="highlight">3, 5, 7</span>, 
                    you add them up: <span className="highlight">3 + 5 + 7 = 15</span>, then divide by the number of friends (3).
                    So, <span className="highlight">15 ÷ 3 = 5</span>. The Mean is <span className="highlight">5</span> candies each!
                </p>
            </div>

            {/* Median Section */}
            <div className="example-box max-w-2xl">
                <h2 className="text-2xl font-bold mb-4">🏅 What is Median?</h2>
                <p className="text-lg">
                    The <span className="highlight">Median</span> is the middle value. Imagine lining up toys from smallest to largest. 
                    The toy in the center is the Median!
                </p>
                <p className="text-lg mt-4">
                    For example: If you have toy sizes: <span className="highlight">1, 3, 5, 7, 9</span>, 
                    the middle toy is <span className="highlight">5</span>. So, the Median is <span className="highlight">5</span>.
                    If there&apos;s an even number, find the average of the two middle numbers!
                </p>
            </div>

            {/* Mode Section */}
            <div className="example-box max-w-2xl">
                <h2 className="text-2xl font-bold mb-4">🎯 What is Mode?</h2>
                <p className="text-lg">
                    The <span className="highlight">Mode</span> is the most popular! It&apos;s the number or item that appears the most in a group.
                </p>
                <p className="text-lg mt-4">
                    For example: If your class votes for their favorite fruit and the results are:
                    <span className="highlight"> Apple (3 votes), Banana (5 votes), Cherry (2 votes)</span>, 
                    the Mode is <span className="highlight">Banana</span> because it got the most votes!
                </p>
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-xl font-semibold mt-8 max-w-2xl">
                See how fun and easy it is? Use Mean, Median, and Mode to solve puzzles with numbers!
            </p>
        </div>
    );
}

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandVue = createReactComponent("outline", "brand-vue", "IconBrandVue", [["path", { "d": "M16.5 4l-4.5 8l-4.5 -8", "key": "svg-0" }], ["path", { "d": "M3 4l9 16l9 -16", "key": "svg-1" }]]);

export { IconBrandVue as default };
//# sourceMappingURL=IconBrandVue.mjs.map

import React from "react";
import Link from "next/link";

const StepEight = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 8: Add Sounds</h1>
        <p className="text-center mt-2 text-lg">Let’s make your Scratch project more exciting with sounds!</p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">Why Add Sounds?</h2>
          <p className="text-lg mb-4">
            Sounds make your project fun and engaging! You can add background music, sound effects, or even your own voice to make your
            Scratch project come alive.
          </p>

          {/* Types of Sounds */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">Types of Sounds You Can Add:</h3>
          <ul className="list-disc pl-6 space-y-4">
            <li>
              <strong>Sound Effects:</strong> Add effects like meows, barks, or clicks to match your sprite’s actions.
            </li>
            <li>
              <strong>Music:</strong> Play background music to make your project more immersive.
            </li>
            <li>
              <strong>Record Your Voice:</strong> Use the microphone to add your voice or custom sounds.
            </li>
          </ul>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 151033.png"
              alt="Add Sounds"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Step-by-Step Instructions */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">How to Add Sounds:</h3>
          <ol className="list-decimal pl-6 space-y-8">
            {/* Step 1 */}
            <li>
              <strong>Choose a Sound:</strong>
              <p className="text-lg mt-2">
                Go to the "Sounds" tab at the top of the Scratch editor. Click the "Choose a Sound" button to select a sound from the library.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 151331.png"
                  alt="Placeholder: Choose a Sound"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 2 */}
            <li>
              <strong>Record Your Own Sound:</strong>
              <p className="text-lg mt-2">
                Click on the microphone icon to record your voice or a custom sound. Use it to personalize your project!
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 151105.png"
                  alt="Placeholder: Record a Sound"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 3 */}
            <li>
              <strong>Add a Sound Block:</strong>
              <p className="text-lg mt-2">
                Go to the "Sound" category in the blocks area. Drag a block like "play sound [meow]" or "start sound [bark]" into your
                script.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 151133.png"
                  alt="Placeholder: Sound Blocks"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>
          </ol>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Add Sounds to Your Project!</h3>
            <p className="text-lg mb-4">
              Add at least one sound to your Scratch project. It could be a sound effect, background music, or your voice. Try combining
              different sounds for a fun experience!
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-7">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 7</button>
        </Link>
        <Link href="/lessons/level1/step-9">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 9</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepEight;

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandProducthunt = createReactComponent("outline", "brand-producthunt", "IconBrandProducthunt", [["path", { "d": "M10 16v-8h2.5a2.5 2.5 0 1 1 0 5h-2.5", "key": "svg-0" }], ["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-1" }]]);

export { IconBrandProducthunt as default };
//# sourceMappingURL=IconBrandProducthunt.mjs.map

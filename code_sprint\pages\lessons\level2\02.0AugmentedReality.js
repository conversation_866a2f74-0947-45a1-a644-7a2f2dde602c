import React from "react";

export default function AugmentedReality() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-green-400 to-blue-600 text-white p-10">
      <h1 className="text-5xl font-bold mb-8">Augmented Reality</h1>
      <p className="text-lg max-w-3xl text-center mb-8">
        Augmented Reality (AR) adds digital content—like animations and 3D models—on top of the real world using AR glasses, smartphones, and tablets. It’s like blending your world with magic!
      </p>

      <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg max-w-2xl mb-8">
        <h2 className="text-3xl font-bold text-yellow-300 mb-4">What You Will Learn:</h2>
        <ul className="list-disc ml-5 space-y-2 text-lg text-gray-800">
          <li>What is Augmented Reality?</li>
          <li>How AR Works</li>
          <li>Types of Augmented Reality</li>
          <li>Applications of Augmented Reality</li>
        </ul>
      </div>

      {/* How AR Works Section */}
      <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg max-w-2xl mb-8">
        <h2 className="text-3xl font-bold text-yellow-300 mb-4">1. How AR Works</h2>
        <p className="text-lg text-gray-800">
          AR works by using your device’s camera and sensors to see the world around you. Then, it adds digital images or information on top of what you’re looking at. Here’s how it happens:
        </p>
        <ul className="list-disc ml-5 space-y-2 text-lg text-gray-800">
          <li>Seeing the World: Your AR device (like a smartphone or tablet) uses its camera to take in the view around you.</li>
          <li>Understanding What It Sees: The device’s sensors and software help it understand where things are, like walls, tables, or people.</li>
          <li>Adding the Digital Magic: Once the device knows what’s around you, it overlays digital stuff—like 3D models, animations, or helpful info—right onto your screen, blending the real world with the virtual one.</li>
        </ul>
        <img 
          src="path-to-image/ar-how-it-works.jpg" 
          alt="How AR Works" 
          className="w-full rounded-lg mt-4"
        />
      </div>

      {/* Types of Augmented Reality Section */}
      <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg max-w-2xl mb-8">
        <h2 className="text-3xl font-bold text-yellow-300 mb-4">2. Types of Augmented Reality</h2>
        <ul className="list-disc ml-5 space-y-2 text-lg text-gray-800">
          <li><strong>Marker-Based AR:</strong> This type uses a specific trigger, like a QR code or a special picture. When your device’s camera sees the trigger, it shows you the digital content connected to it.</li>
          <li><strong>Marker-Less AR:</strong> This type doesn’t need a specific trigger. It uses the camera and sensors in your device to understand the environment around you and then places digital objects wherever they fit.</li>
        </ul>
        <img 
          src="path-to-image/ar-types.jpg" 
          alt="Types of AR" 
          className="w-full rounded-lg mt-4"
        />
      </div>

      {/* Applications Section */}
      <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg max-w-2xl mb-8">
        <h2 className="text-3xl font-bold text-yellow-300 mb-4">3. Applications of Augmented Reality</h2>
        <ul className="list-disc ml-5 space-y-2 text-lg text-gray-800">
          <li><strong>Gaming:</strong> Play exciting AR games like Pokémon GO, where virtual creatures appear in real-world locations.</li>
          <li><strong>Education:</strong> Learn about dinosaurs by seeing them appear right in your classroom, making lessons fun and interactive.</li>
          <li><strong>Shopping:</strong> Try on clothes or see how furniture would look in your room, all without leaving your house.</li>
          <li><strong>Healthcare:</strong> Doctors can use AR to see detailed 3D images of the body during surgery, helping them be more precise.</li>
        </ul>
        <img 
          src="path-to-image/ar-applications.jpg" 
          alt="AR Applications" 
          className="w-full rounded-lg mt-4"
        />
      </div>
    </div>
  );
}


/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandVisualStudio = createReactComponent("outline", "brand-visual-studio", "IconBrandVisualStudio", [["path", { "d": "M4 8l2 -1l10 13l4 -2v-12l-4 -2l-10 13l-2 -1z", "key": "svg-0" }]]);

export { IconBrandVisualStudio as default };
//# sourceMappingURL=IconBrandVisualStudio.mjs.map

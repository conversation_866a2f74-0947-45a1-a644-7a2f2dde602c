/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCameraBitcoin = createReactComponent("outline", "camera-bitcoin", "IconCameraBitcoin", [["path", { "d": "M12 20h-7a2 2 0 0 1 -2 -2v-9a2 2 0 0 1 2 -2h1a2 2 0 0 0 2 -2a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1a2 2 0 0 0 2 2h1a2 2 0 0 1 2 2v1", "key": "svg-0" }], ["path", { "d": "M14.477 11.307a3 3 0 1 0 -2.477 4.693", "key": "svg-1" }], ["path", { "d": "M17 21v-6", "key": "svg-2" }], ["path", { "d": "M19 15v-1.5", "key": "svg-3" }], ["path", { "d": "M19 22.5v-1.5", "key": "svg-4" }], ["path", { "d": "M17 18h3", "key": "svg-5" }], ["path", { "d": "M19 18h.5a1.5 1.5 0 0 1 0 3h-3.5", "key": "svg-6" }], ["path", { "d": "M19 18h.5a1.5 1.5 0 0 0 0 -3h-3.5", "key": "svg-7" }]]);

export { IconCameraBitcoin as default };
//# sourceMappingURL=IconCameraBitcoin.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandWalmart = createReactComponent("outline", "brand-walmart", "IconBrandWalmart", [["path", { "d": "M12 8.04v-5.04", "key": "svg-0" }], ["path", { "d": "M15.5 10l4.5 -2.5", "key": "svg-1" }], ["path", { "d": "M15.5 14l4.5 2.5", "key": "svg-2" }], ["path", { "d": "M12 15.96v5.04", "key": "svg-3" }], ["path", { "d": "M8.5 14l-4.5 2.5", "key": "svg-4" }], ["path", { "d": "M8.5 10l-4.5 -2.505", "key": "svg-5" }]]);

export { IconBrandWalmart as default };
//# sourceMappingURL=IconBrandWalmart.mjs.map

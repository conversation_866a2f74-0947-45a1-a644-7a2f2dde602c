/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandSuperhuman = createReactComponent("outline", "brand-superhuman", "IconBrandSuperhuman", [["path", { "d": "M16 12l4 3l-8 7l-8 -7l4 -3", "key": "svg-0" }], ["path", { "d": "M12 3l-8 6l8 6l8 -6z", "key": "svg-1" }], ["path", { "d": "M12 15h8", "key": "svg-2" }]]);

export { IconBrandSuperhuman as default };
//# sourceMappingURL=IconBrandSuperhuman.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandPagekit = createReactComponent("outline", "brand-pagekit", "IconBrandPagekit", [["path", { "d": "M12.077 20h-5.077v-16h11v14h-5.077", "key": "svg-0" }]]);

export { IconBrandPagekit as default };
//# sourceMappingURL=IconBrandPagekit.mjs.map

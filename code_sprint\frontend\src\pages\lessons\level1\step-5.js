import React from "react";
import Link from "next/link";

const StepFive = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 5: Understand the Scratch Editor</h1>
        <p className="text-center mt-2 text-lg">Let’s learn about the Scratch editor and where everything is!</p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">The Scratch Editor</h2>
          <p className="text-lg mb-4">
            The Scratch editor is where all the magic happens! This is where you can drag and drop blocks, create animations, and design
            your project. Let’s take a closer look at the main parts of the Scratch editor:
          </p>

          {/* Step-by-Step Instructions */}
          <ol className="list-decimal pl-6 space-y-8">
            {/* Step 1 */}
            <li>
              <strong>Stage:</strong>
              <p className="text-lg mt-2">
                The stage is like the screen where your sprites and animations appear. This is where your project comes to life!
              </p>
              {/* Image Placeholder */}
              {/* <div className="flex justify-center mt-4">
                <img
                  src="https://via.placeholder.com/300x200?text=Stage"
                  alt="Placeholder: Stage"
                  className="rounded-lg shadow-md"
                />
              </div> */}
            </li>

            {/* Step 2 */}
            <li>
              <strong>Sprite List:</strong>
              <p className="text-lg mt-2">
                The sprite list shows all the characters or objects in your project. You can add, delete, or select sprites from here.
              </p>
              {/* Image Placeholder */}
              
            </li>

            {/* Step 3 */}
            <li>
              <strong>Code Blocks Area:</strong>
              <p className="text-lg mt-2">
                This is where you’ll find all the blocks to make your sprite move, talk, and do amazing things. Blocks are grouped into
                categories like Motion, Looks, and Sounds.
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 142942.png"
                  alt="Placeholder: Blocks Area"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 4 */}
            <li>
              <strong>Script Area:</strong>
              <p className="text-lg mt-2">
                Drag and drop blocks into this area to create your program. This is where you "write" your code by connecting blocks
                together!
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 142901.png"
                  alt="Placeholder: Script Area"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            {/* Step 5 */}
            <li>
              <strong>Backdrop and Costumes:</strong>
              <p className="text-lg mt-2">
                Add cool backgrounds or costumes for your sprites here. Use this to make your project colorful and fun!
              </p>
              {/* Image Placeholder */}
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 143021.png"
                  alt="Placeholder: Backdrop and Costumes"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>
          </ol>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 143044.png"
              alt="Scratch Editor Overview"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Explore the Scratch Editor!</h3>
            <p className="text-lg mb-4">
              Open the Scratch editor and explore each part. Click on the stage, sprite list, blocks, and backdrops to see what they do.
              Try adding a new sprite or backdrop!
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-4">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 4</button>
        </Link>
        <Link href="/lessons/level1/step-6">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 6</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepFive;

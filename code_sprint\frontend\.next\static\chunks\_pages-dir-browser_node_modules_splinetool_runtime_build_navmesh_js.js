"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_navmesh_js"],{

/***/ "(pages-dir-browser)/../node_modules/@splinetool/runtime/build/navmesh.js":
/*!************************************************************!*\
  !*** ../node_modules/@splinetool/runtime/build/navmesh.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\nvar Module = (function() {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(Module) {\n  Module = Module || {};\n\nvar Module=typeof Module!==\"undefined\"?Module:{};var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram=\"./this.program\";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!==\"undefined\"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];function convertJsFunctionToWasm(func,sig){if(typeof WebAssembly.Function===\"function\"){var typeNames={\"i\":\"i32\",\"j\":\"i64\",\"f\":\"f32\",\"d\":\"f64\"};var type={parameters:[],results:sig[0]==\"v\"?[]:[typeNames[sig[0]]]};for(var i=1;i<sig.length;++i){type.parameters.push(typeNames[sig[i]])}return new WebAssembly.Function(type,func)}var typeSection=[1,0,1,96];var sigRet=sig.slice(0,1);var sigParam=sig.slice(1);var typeCodes={\"i\":127,\"j\":126,\"f\":125,\"d\":124};typeSection.push(sigParam.length);for(var i=0;i<sigParam.length;++i){typeSection.push(typeCodes[sigParam[i]])}if(sigRet==\"v\"){typeSection.push(0)}else{typeSection=typeSection.concat([1,typeCodes[sigRet]])}typeSection[1]=typeSection.length-2;var bytes=new Uint8Array([0,97,115,109,1,0,0,0].concat(typeSection,[2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0]));var module=new WebAssembly.Module(bytes);var instance=new WebAssembly.Instance(module,{\"e\":{\"f\":func}});var wrappedFunc=instance.exports[\"f\"];return wrappedFunc}var freeTableIndexes=[];var functionsInTableMap;function getEmptyTableSlot(){if(freeTableIndexes.length){return freeTableIndexes.pop()}try{wasmTable.grow(1)}catch(err){if(!(err instanceof RangeError)){throw err}throw\"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.\"}return wasmTable.length-1}function updateTableMap(offset,count){for(var i=offset;i<offset+count;i++){var item=getWasmTableEntry(i);if(item){functionsInTableMap.set(item,i)}}}function addFunction(func,sig){if(!functionsInTableMap){functionsInTableMap=new WeakMap;updateTableMap(0,wasmTable.length)}if(functionsInTableMap.has(func)){return functionsInTableMap.get(func)}var ret=getEmptyTableSlot();try{setWasmTableEntry(ret,func)}catch(err){if(!(err instanceof TypeError)){throw err}var wrapped=convertJsFunctionToWasm(func,sig);setWasmTableEntry(ret,wrapped)}functionsInTableMap.set(func,ret);return ret}var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];var noExitRuntime=Module[\"noExitRuntime\"]||true;if(typeof WebAssembly!==\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(\"Assertion failed: \"+text)}}var UTF8Decoder=typeof TextDecoder!==\"undefined\"?new TextDecoder(\"utf8\"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str=\"\";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\"}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}function writeArrayToMemory(array,buffer){HEAP8.set(array,buffer)}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module[\"HEAP8\"]=HEAP8=new Int8Array(buf);Module[\"HEAP16\"]=HEAP16=new Int16Array(buf);Module[\"HEAP32\"]=HEAP32=new Int32Array(buf);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(buf);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(buf);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(buf);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(buf);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module[\"INITIAL_MEMORY\"]||16777216;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module[\"preloadedImages\"]={};Module[\"preloadedAudios\"]={};function abort(what){{if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}}what=\"Aborted(\"+what+\")\";err(what);ABORT=true;EXITSTATUS=1;what+=\". Build with -s ASSERTIONS=1 for more info.\";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix=\"data:application/octet-stream;base64,\";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile=\"navmesh.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw\"both async and sync fetching of the wasm failed\"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+wasmBinaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(function(){return getBinary(wasmBinaryFile)})}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={\"a\":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module[\"asm\"]=exports;wasmMemory=Module[\"asm\"][\"m\"];updateGlobalBufferAndViews(wasmMemory.buffer);wasmTable=Module[\"asm\"][\"Jb\"];addOnInit(Module[\"asm\"][\"n\"]);removeRunDependency(\"wasm-instantiate\")}addRunDependency(\"wasm-instantiate\");function receiveInstantiationResult(result){receiveInstance(result[\"instance\"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err(\"failed to asynchronously prepare wasm: \"+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming===\"function\"&&!isDataURI(wasmBinaryFile)&&typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err(\"wasm streaming compile failed: \"+reason);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module[\"instantiateWasm\"]){try{var exports=Module[\"instantiateWasm\"](info,receiveInstance);return exports}catch(e){err(\"Module.instantiateWasm callback failed with error: \"+e);return false}}instantiateAsync().catch(readyPromiseReject);return{}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback==\"function\"){callback(Module);continue}var func=callback.func;if(typeof func===\"number\"){if(callback.arg===undefined){getWasmTableEntry(func)()}else{getWasmTableEntry(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func}function setWasmTableEntry(idx,func){wasmTable.set(idx,func);wasmTableMirror[idx]=func}function ___cxa_allocate_exception(size){return _malloc(size+16)+16}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-16;this.set_type=function(type){HEAP32[this.ptr+4>>2]=type};this.get_type=function(){return HEAP32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAP32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAP32[this.ptr+8>>2]};this.set_refcount=function(refcount){HEAP32[this.ptr>>2]=refcount};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_type(type);this.set_destructor(destructor);this.set_refcount(0);this.set_caught(false);this.set_rethrown(false)};this.add_ref=function(){var value=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=value+1};this.release_ref=function(){var prev=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=prev-1;return prev===1}}var exceptionLast=0;var uncaughtExceptionCount=0;function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw ptr}function _abort(){abort(\"\")}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}var ENV={};function getExecutableName(){return thisProgram||\"./this.program\"}function getEnvStrings(){if(!getEnvStrings.strings){var lang=(typeof navigator===\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\";var env={\"USER\":\"web_user\",\"LOGNAME\":\"web_user\",\"PATH\":\"/\",\"PWD\":\"/\",\"HOME\":\"/home/<USER>",\"LANG\":lang,\"_\":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(x+\"=\"+env[x])}getEnvStrings.strings=strings}return getEnvStrings.strings}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){return low}};function _environ_get(__environ,environ_buf){var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAP32[__environ+i*4>>2]=ptr;writeAsciiToMemory(string,ptr);bufSize+=string.length+1});return 0}function _environ_sizes_get(penviron_count,penviron_buf_size){var strings=getEnvStrings();HEAP32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});HEAP32[penviron_buf_size>>2]=bufSize;return 0}function _fd_close(fd){return 0}function _fd_read(fd,iov,iovcnt,pnum){var stream=SYSCALLS.getStreamFromFD(fd);var num=SYSCALLS.doReadv(stream,iov,iovcnt);HEAP32[pnum>>2]=num;return 0}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov>>2];var len=HEAP32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}function __isLeapYear(year){return year%4===0&&(year%100!==0||year%400===0)}function __arraySum(array,index){var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum}var __MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var __MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];function __addDays(date,days){var newDate=new Date(date.getTime());while(days>0){var leap=__isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate}function _strftime(s,maxsize,format,tm){var tm_zone=HEAP32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):\"\"};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_1[rule])}var WEEKDAYS=[\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"];var MONTHS=[\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];function leadingSomething(value,digits,character){var str=typeof value===\"number\"?value.toString():value||\"\";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,\"0\")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=__addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}else{return thisDate.getFullYear()}}else{return thisDate.getFullYear()-1}}var EXPANSION_RULES_2={\"%a\":function(date){return WEEKDAYS[date.tm_wday].substring(0,3)},\"%A\":function(date){return WEEKDAYS[date.tm_wday]},\"%b\":function(date){return MONTHS[date.tm_mon].substring(0,3)},\"%B\":function(date){return MONTHS[date.tm_mon]},\"%C\":function(date){var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},\"%d\":function(date){return leadingNulls(date.tm_mday,2)},\"%e\":function(date){return leadingSomething(date.tm_mday,2,\" \")},\"%g\":function(date){return getWeekBasedYear(date).toString().substring(2)},\"%G\":function(date){return getWeekBasedYear(date)},\"%H\":function(date){return leadingNulls(date.tm_hour,2)},\"%I\":function(date){var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},\"%j\":function(date){return leadingNulls(date.tm_mday+__arraySum(__isLeapYear(date.tm_year+1900)?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,date.tm_mon-1),3)},\"%m\":function(date){return leadingNulls(date.tm_mon+1,2)},\"%M\":function(date){return leadingNulls(date.tm_min,2)},\"%n\":function(){return\"\\n\"},\"%p\":function(date){if(date.tm_hour>=0&&date.tm_hour<12){return\"AM\"}else{return\"PM\"}},\"%S\":function(date){return leadingNulls(date.tm_sec,2)},\"%t\":function(){return\"\\t\"},\"%u\":function(date){return date.tm_wday||7},\"%U\":function(date){var janFirst=new Date(date.tm_year+1900,0,1);var firstSunday=janFirst.getDay()===0?janFirst:__addDays(janFirst,7-janFirst.getDay());var endDate=new Date(date.tm_year+1900,date.tm_mon,date.tm_mday);if(compareByDay(firstSunday,endDate)<0){var februaryFirstUntilEndMonth=__arraySum(__isLeapYear(endDate.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,endDate.getMonth()-1)-31;var firstSundayUntilEndJanuary=31-firstSunday.getDate();var days=firstSundayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();return leadingNulls(Math.ceil(days/7),2)}return compareByDay(firstSunday,janFirst)===0?\"01\":\"00\"},\"%V\":function(date){var janFourthThisYear=new Date(date.tm_year+1900,0,4);var janFourthNextYear=new Date(date.tm_year+1901,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);var endDate=__addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);if(compareByDay(endDate,firstWeekStartThisYear)<0){return\"53\"}if(compareByDay(firstWeekStartNextYear,endDate)<=0){return\"01\"}var daysDifference;if(firstWeekStartThisYear.getFullYear()<date.tm_year+1900){daysDifference=date.tm_yday+32-firstWeekStartThisYear.getDate()}else{daysDifference=date.tm_yday+1-firstWeekStartThisYear.getDate()}return leadingNulls(Math.ceil(daysDifference/7),2)},\"%w\":function(date){return date.tm_wday},\"%W\":function(date){var janFirst=new Date(date.tm_year,0,1);var firstMonday=janFirst.getDay()===1?janFirst:__addDays(janFirst,janFirst.getDay()===0?1:7-janFirst.getDay()+1);var endDate=new Date(date.tm_year+1900,date.tm_mon,date.tm_mday);if(compareByDay(firstMonday,endDate)<0){var februaryFirstUntilEndMonth=__arraySum(__isLeapYear(endDate.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,endDate.getMonth()-1)-31;var firstMondayUntilEndJanuary=31-firstMonday.getDate();var days=firstMondayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();return leadingNulls(Math.ceil(days/7),2)}return compareByDay(firstMonday,janFirst)===0?\"01\":\"00\"},\"%y\":function(date){return(date.tm_year+1900).toString().substring(2)},\"%Y\":function(date){return date.tm_year+1900},\"%z\":function(date){var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?\"+\":\"-\")+String(\"0000\"+off).slice(-4)},\"%Z\":function(date){return date.tm_zone},\"%%\":function(){return\"%\"}};for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_2[rule](date))}}var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1}function _strftime_l(s,maxsize,format,tm){return _strftime(s,maxsize,format,tm)}function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var asmLibraryArg={\"l\":___cxa_allocate_exception,\"k\":___cxa_throw,\"b\":_abort,\"j\":_emscripten_memcpy_big,\"a\":_emscripten_resize_heap,\"g\":_environ_get,\"h\":_environ_sizes_get,\"c\":_fd_close,\"e\":_fd_read,\"i\":_fd_seek,\"d\":_fd_write,\"f\":_strftime_l};var asm=createWasm();var ___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=function(){return(___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=Module[\"asm\"][\"n\"]).apply(null,arguments)};var _emscripten_bind_VoidPtr___destroy___0=Module[\"_emscripten_bind_VoidPtr___destroy___0\"]=function(){return(_emscripten_bind_VoidPtr___destroy___0=Module[\"_emscripten_bind_VoidPtr___destroy___0\"]=Module[\"asm\"][\"o\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_rcConfig_0=Module[\"_emscripten_bind_rcConfig_rcConfig_0\"]=function(){return(_emscripten_bind_rcConfig_rcConfig_0=Module[\"_emscripten_bind_rcConfig_rcConfig_0\"]=Module[\"asm\"][\"p\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_width_0=Module[\"_emscripten_bind_rcConfig_get_width_0\"]=function(){return(_emscripten_bind_rcConfig_get_width_0=Module[\"_emscripten_bind_rcConfig_get_width_0\"]=Module[\"asm\"][\"q\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_width_1=Module[\"_emscripten_bind_rcConfig_set_width_1\"]=function(){return(_emscripten_bind_rcConfig_set_width_1=Module[\"_emscripten_bind_rcConfig_set_width_1\"]=Module[\"asm\"][\"r\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_height_0=Module[\"_emscripten_bind_rcConfig_get_height_0\"]=function(){return(_emscripten_bind_rcConfig_get_height_0=Module[\"_emscripten_bind_rcConfig_get_height_0\"]=Module[\"asm\"][\"s\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_height_1=Module[\"_emscripten_bind_rcConfig_set_height_1\"]=function(){return(_emscripten_bind_rcConfig_set_height_1=Module[\"_emscripten_bind_rcConfig_set_height_1\"]=Module[\"asm\"][\"t\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_tileSize_0=Module[\"_emscripten_bind_rcConfig_get_tileSize_0\"]=function(){return(_emscripten_bind_rcConfig_get_tileSize_0=Module[\"_emscripten_bind_rcConfig_get_tileSize_0\"]=Module[\"asm\"][\"u\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_tileSize_1=Module[\"_emscripten_bind_rcConfig_set_tileSize_1\"]=function(){return(_emscripten_bind_rcConfig_set_tileSize_1=Module[\"_emscripten_bind_rcConfig_set_tileSize_1\"]=Module[\"asm\"][\"v\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_borderSize_0=Module[\"_emscripten_bind_rcConfig_get_borderSize_0\"]=function(){return(_emscripten_bind_rcConfig_get_borderSize_0=Module[\"_emscripten_bind_rcConfig_get_borderSize_0\"]=Module[\"asm\"][\"w\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_borderSize_1=Module[\"_emscripten_bind_rcConfig_set_borderSize_1\"]=function(){return(_emscripten_bind_rcConfig_set_borderSize_1=Module[\"_emscripten_bind_rcConfig_set_borderSize_1\"]=Module[\"asm\"][\"x\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_cs_0=Module[\"_emscripten_bind_rcConfig_get_cs_0\"]=function(){return(_emscripten_bind_rcConfig_get_cs_0=Module[\"_emscripten_bind_rcConfig_get_cs_0\"]=Module[\"asm\"][\"y\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_cs_1=Module[\"_emscripten_bind_rcConfig_set_cs_1\"]=function(){return(_emscripten_bind_rcConfig_set_cs_1=Module[\"_emscripten_bind_rcConfig_set_cs_1\"]=Module[\"asm\"][\"z\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_ch_0=Module[\"_emscripten_bind_rcConfig_get_ch_0\"]=function(){return(_emscripten_bind_rcConfig_get_ch_0=Module[\"_emscripten_bind_rcConfig_get_ch_0\"]=Module[\"asm\"][\"A\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_ch_1=Module[\"_emscripten_bind_rcConfig_set_ch_1\"]=function(){return(_emscripten_bind_rcConfig_set_ch_1=Module[\"_emscripten_bind_rcConfig_set_ch_1\"]=Module[\"asm\"][\"B\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_bmin_1=Module[\"_emscripten_bind_rcConfig_get_bmin_1\"]=function(){return(_emscripten_bind_rcConfig_get_bmin_1=Module[\"_emscripten_bind_rcConfig_get_bmin_1\"]=Module[\"asm\"][\"C\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_bmin_2=Module[\"_emscripten_bind_rcConfig_set_bmin_2\"]=function(){return(_emscripten_bind_rcConfig_set_bmin_2=Module[\"_emscripten_bind_rcConfig_set_bmin_2\"]=Module[\"asm\"][\"D\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_bmax_1=Module[\"_emscripten_bind_rcConfig_get_bmax_1\"]=function(){return(_emscripten_bind_rcConfig_get_bmax_1=Module[\"_emscripten_bind_rcConfig_get_bmax_1\"]=Module[\"asm\"][\"E\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_bmax_2=Module[\"_emscripten_bind_rcConfig_set_bmax_2\"]=function(){return(_emscripten_bind_rcConfig_set_bmax_2=Module[\"_emscripten_bind_rcConfig_set_bmax_2\"]=Module[\"asm\"][\"F\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableSlopeAngle_0=Module[\"_emscripten_bind_rcConfig_get_walkableSlopeAngle_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableSlopeAngle_0=Module[\"_emscripten_bind_rcConfig_get_walkableSlopeAngle_0\"]=Module[\"asm\"][\"G\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableSlopeAngle_1=Module[\"_emscripten_bind_rcConfig_set_walkableSlopeAngle_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableSlopeAngle_1=Module[\"_emscripten_bind_rcConfig_set_walkableSlopeAngle_1\"]=Module[\"asm\"][\"H\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableHeight_0=Module[\"_emscripten_bind_rcConfig_get_walkableHeight_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableHeight_0=Module[\"_emscripten_bind_rcConfig_get_walkableHeight_0\"]=Module[\"asm\"][\"I\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableHeight_1=Module[\"_emscripten_bind_rcConfig_set_walkableHeight_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableHeight_1=Module[\"_emscripten_bind_rcConfig_set_walkableHeight_1\"]=Module[\"asm\"][\"J\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableClimb_0=Module[\"_emscripten_bind_rcConfig_get_walkableClimb_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableClimb_0=Module[\"_emscripten_bind_rcConfig_get_walkableClimb_0\"]=Module[\"asm\"][\"K\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableClimb_1=Module[\"_emscripten_bind_rcConfig_set_walkableClimb_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableClimb_1=Module[\"_emscripten_bind_rcConfig_set_walkableClimb_1\"]=Module[\"asm\"][\"L\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableRadius_0=Module[\"_emscripten_bind_rcConfig_get_walkableRadius_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableRadius_0=Module[\"_emscripten_bind_rcConfig_get_walkableRadius_0\"]=Module[\"asm\"][\"M\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableRadius_1=Module[\"_emscripten_bind_rcConfig_set_walkableRadius_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableRadius_1=Module[\"_emscripten_bind_rcConfig_set_walkableRadius_1\"]=Module[\"asm\"][\"N\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_maxEdgeLen_0=Module[\"_emscripten_bind_rcConfig_get_maxEdgeLen_0\"]=function(){return(_emscripten_bind_rcConfig_get_maxEdgeLen_0=Module[\"_emscripten_bind_rcConfig_get_maxEdgeLen_0\"]=Module[\"asm\"][\"O\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_maxEdgeLen_1=Module[\"_emscripten_bind_rcConfig_set_maxEdgeLen_1\"]=function(){return(_emscripten_bind_rcConfig_set_maxEdgeLen_1=Module[\"_emscripten_bind_rcConfig_set_maxEdgeLen_1\"]=Module[\"asm\"][\"P\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_maxSimplificationError_0=Module[\"_emscripten_bind_rcConfig_get_maxSimplificationError_0\"]=function(){return(_emscripten_bind_rcConfig_get_maxSimplificationError_0=Module[\"_emscripten_bind_rcConfig_get_maxSimplificationError_0\"]=Module[\"asm\"][\"Q\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_maxSimplificationError_1=Module[\"_emscripten_bind_rcConfig_set_maxSimplificationError_1\"]=function(){return(_emscripten_bind_rcConfig_set_maxSimplificationError_1=Module[\"_emscripten_bind_rcConfig_set_maxSimplificationError_1\"]=Module[\"asm\"][\"R\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_minRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_minRegionArea_0\"]=function(){return(_emscripten_bind_rcConfig_get_minRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_minRegionArea_0\"]=Module[\"asm\"][\"S\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_minRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_minRegionArea_1\"]=function(){return(_emscripten_bind_rcConfig_set_minRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_minRegionArea_1\"]=Module[\"asm\"][\"T\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_mergeRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_mergeRegionArea_0\"]=function(){return(_emscripten_bind_rcConfig_get_mergeRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_mergeRegionArea_0\"]=Module[\"asm\"][\"U\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_mergeRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_mergeRegionArea_1\"]=function(){return(_emscripten_bind_rcConfig_set_mergeRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_mergeRegionArea_1\"]=Module[\"asm\"][\"V\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_maxVertsPerPoly_0=Module[\"_emscripten_bind_rcConfig_get_maxVertsPerPoly_0\"]=function(){return(_emscripten_bind_rcConfig_get_maxVertsPerPoly_0=Module[\"_emscripten_bind_rcConfig_get_maxVertsPerPoly_0\"]=Module[\"asm\"][\"W\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_maxVertsPerPoly_1=Module[\"_emscripten_bind_rcConfig_set_maxVertsPerPoly_1\"]=function(){return(_emscripten_bind_rcConfig_set_maxVertsPerPoly_1=Module[\"_emscripten_bind_rcConfig_set_maxVertsPerPoly_1\"]=Module[\"asm\"][\"X\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_detailSampleDist_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleDist_0\"]=function(){return(_emscripten_bind_rcConfig_get_detailSampleDist_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleDist_0\"]=Module[\"asm\"][\"Y\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_detailSampleDist_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleDist_1\"]=function(){return(_emscripten_bind_rcConfig_set_detailSampleDist_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleDist_1\"]=Module[\"asm\"][\"Z\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_detailSampleMaxError_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleMaxError_0\"]=function(){return(_emscripten_bind_rcConfig_get_detailSampleMaxError_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleMaxError_0\"]=Module[\"asm\"][\"_\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_detailSampleMaxError_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleMaxError_1\"]=function(){return(_emscripten_bind_rcConfig_set_detailSampleMaxError_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleMaxError_1\"]=Module[\"asm\"][\"$\"]).apply(null,arguments)};var _emscripten_bind_rcConfig___destroy___0=Module[\"_emscripten_bind_rcConfig___destroy___0\"]=function(){return(_emscripten_bind_rcConfig___destroy___0=Module[\"_emscripten_bind_rcConfig___destroy___0\"]=Module[\"asm\"][\"aa\"]).apply(null,arguments)};var _emscripten_bind_Vec3_Vec3_0=Module[\"_emscripten_bind_Vec3_Vec3_0\"]=function(){return(_emscripten_bind_Vec3_Vec3_0=Module[\"_emscripten_bind_Vec3_Vec3_0\"]=Module[\"asm\"][\"ba\"]).apply(null,arguments)};var _emscripten_bind_Vec3_Vec3_3=Module[\"_emscripten_bind_Vec3_Vec3_3\"]=function(){return(_emscripten_bind_Vec3_Vec3_3=Module[\"_emscripten_bind_Vec3_Vec3_3\"]=Module[\"asm\"][\"ca\"]).apply(null,arguments)};var _emscripten_bind_Vec3_get_x_0=Module[\"_emscripten_bind_Vec3_get_x_0\"]=function(){return(_emscripten_bind_Vec3_get_x_0=Module[\"_emscripten_bind_Vec3_get_x_0\"]=Module[\"asm\"][\"da\"]).apply(null,arguments)};var _emscripten_bind_Vec3_set_x_1=Module[\"_emscripten_bind_Vec3_set_x_1\"]=function(){return(_emscripten_bind_Vec3_set_x_1=Module[\"_emscripten_bind_Vec3_set_x_1\"]=Module[\"asm\"][\"ea\"]).apply(null,arguments)};var _emscripten_bind_Vec3_get_y_0=Module[\"_emscripten_bind_Vec3_get_y_0\"]=function(){return(_emscripten_bind_Vec3_get_y_0=Module[\"_emscripten_bind_Vec3_get_y_0\"]=Module[\"asm\"][\"fa\"]).apply(null,arguments)};var _emscripten_bind_Vec3_set_y_1=Module[\"_emscripten_bind_Vec3_set_y_1\"]=function(){return(_emscripten_bind_Vec3_set_y_1=Module[\"_emscripten_bind_Vec3_set_y_1\"]=Module[\"asm\"][\"ga\"]).apply(null,arguments)};var _emscripten_bind_Vec3_get_z_0=Module[\"_emscripten_bind_Vec3_get_z_0\"]=function(){return(_emscripten_bind_Vec3_get_z_0=Module[\"_emscripten_bind_Vec3_get_z_0\"]=Module[\"asm\"][\"ha\"]).apply(null,arguments)};var _emscripten_bind_Vec3_set_z_1=Module[\"_emscripten_bind_Vec3_set_z_1\"]=function(){return(_emscripten_bind_Vec3_set_z_1=Module[\"_emscripten_bind_Vec3_set_z_1\"]=Module[\"asm\"][\"ia\"]).apply(null,arguments)};var _emscripten_bind_Vec3___destroy___0=Module[\"_emscripten_bind_Vec3___destroy___0\"]=function(){return(_emscripten_bind_Vec3___destroy___0=Module[\"_emscripten_bind_Vec3___destroy___0\"]=Module[\"asm\"][\"ja\"]).apply(null,arguments)};var _emscripten_bind_Triangle_Triangle_0=Module[\"_emscripten_bind_Triangle_Triangle_0\"]=function(){return(_emscripten_bind_Triangle_Triangle_0=Module[\"_emscripten_bind_Triangle_Triangle_0\"]=Module[\"asm\"][\"ka\"]).apply(null,arguments)};var _emscripten_bind_Triangle_getPoint_1=Module[\"_emscripten_bind_Triangle_getPoint_1\"]=function(){return(_emscripten_bind_Triangle_getPoint_1=Module[\"_emscripten_bind_Triangle_getPoint_1\"]=Module[\"asm\"][\"la\"]).apply(null,arguments)};var _emscripten_bind_Triangle___destroy___0=Module[\"_emscripten_bind_Triangle___destroy___0\"]=function(){return(_emscripten_bind_Triangle___destroy___0=Module[\"_emscripten_bind_Triangle___destroy___0\"]=Module[\"asm\"][\"ma\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh_DebugNavMesh_0=Module[\"_emscripten_bind_DebugNavMesh_DebugNavMesh_0\"]=function(){return(_emscripten_bind_DebugNavMesh_DebugNavMesh_0=Module[\"_emscripten_bind_DebugNavMesh_DebugNavMesh_0\"]=Module[\"asm\"][\"na\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh_getTriangleCount_0=Module[\"_emscripten_bind_DebugNavMesh_getTriangleCount_0\"]=function(){return(_emscripten_bind_DebugNavMesh_getTriangleCount_0=Module[\"_emscripten_bind_DebugNavMesh_getTriangleCount_0\"]=Module[\"asm\"][\"oa\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh_getTriangle_1=Module[\"_emscripten_bind_DebugNavMesh_getTriangle_1\"]=function(){return(_emscripten_bind_DebugNavMesh_getTriangle_1=Module[\"_emscripten_bind_DebugNavMesh_getTriangle_1\"]=Module[\"asm\"][\"pa\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh___destroy___0=Module[\"_emscripten_bind_DebugNavMesh___destroy___0\"]=function(){return(_emscripten_bind_DebugNavMesh___destroy___0=Module[\"_emscripten_bind_DebugNavMesh___destroy___0\"]=Module[\"asm\"][\"qa\"]).apply(null,arguments)};var _emscripten_bind_dtNavMesh___destroy___0=Module[\"_emscripten_bind_dtNavMesh___destroy___0\"]=function(){return(_emscripten_bind_dtNavMesh___destroy___0=Module[\"_emscripten_bind_dtNavMesh___destroy___0\"]=Module[\"asm\"][\"ra\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_NavmeshData_0=Module[\"_emscripten_bind_NavmeshData_NavmeshData_0\"]=function(){return(_emscripten_bind_NavmeshData_NavmeshData_0=Module[\"_emscripten_bind_NavmeshData_NavmeshData_0\"]=Module[\"asm\"][\"sa\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_get_dataPointer_0=Module[\"_emscripten_bind_NavmeshData_get_dataPointer_0\"]=function(){return(_emscripten_bind_NavmeshData_get_dataPointer_0=Module[\"_emscripten_bind_NavmeshData_get_dataPointer_0\"]=Module[\"asm\"][\"ta\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_set_dataPointer_1=Module[\"_emscripten_bind_NavmeshData_set_dataPointer_1\"]=function(){return(_emscripten_bind_NavmeshData_set_dataPointer_1=Module[\"_emscripten_bind_NavmeshData_set_dataPointer_1\"]=Module[\"asm\"][\"ua\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_get_size_0=Module[\"_emscripten_bind_NavmeshData_get_size_0\"]=function(){return(_emscripten_bind_NavmeshData_get_size_0=Module[\"_emscripten_bind_NavmeshData_get_size_0\"]=Module[\"asm\"][\"va\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_set_size_1=Module[\"_emscripten_bind_NavmeshData_set_size_1\"]=function(){return(_emscripten_bind_NavmeshData_set_size_1=Module[\"_emscripten_bind_NavmeshData_set_size_1\"]=Module[\"asm\"][\"wa\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData___destroy___0=Module[\"_emscripten_bind_NavmeshData___destroy___0\"]=function(){return(_emscripten_bind_NavmeshData___destroy___0=Module[\"_emscripten_bind_NavmeshData___destroy___0\"]=Module[\"asm\"][\"xa\"]).apply(null,arguments)};var _emscripten_bind_NavPath_getPointCount_0=Module[\"_emscripten_bind_NavPath_getPointCount_0\"]=function(){return(_emscripten_bind_NavPath_getPointCount_0=Module[\"_emscripten_bind_NavPath_getPointCount_0\"]=Module[\"asm\"][\"ya\"]).apply(null,arguments)};var _emscripten_bind_NavPath_getPoint_1=Module[\"_emscripten_bind_NavPath_getPoint_1\"]=function(){return(_emscripten_bind_NavPath_getPoint_1=Module[\"_emscripten_bind_NavPath_getPoint_1\"]=Module[\"asm\"][\"za\"]).apply(null,arguments)};var _emscripten_bind_NavPath___destroy___0=Module[\"_emscripten_bind_NavPath___destroy___0\"]=function(){return(_emscripten_bind_NavPath___destroy___0=Module[\"_emscripten_bind_NavPath___destroy___0\"]=Module[\"asm\"][\"Aa\"]).apply(null,arguments)};var _emscripten_bind_dtObstacleRef___destroy___0=Module[\"_emscripten_bind_dtObstacleRef___destroy___0\"]=function(){return(_emscripten_bind_dtObstacleRef___destroy___0=Module[\"_emscripten_bind_dtObstacleRef___destroy___0\"]=Module[\"asm\"][\"Ba\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0=Module[\"_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0=Module[\"_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0\"]=Module[\"asm\"][\"Ca\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_radius_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_radius_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_radius_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_radius_0\"]=Module[\"asm\"][\"Da\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_radius_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_radius_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_radius_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_radius_1\"]=Module[\"asm\"][\"Ea\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_height_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_height_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_height_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_height_0\"]=Module[\"asm\"][\"Fa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_height_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_height_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_height_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_height_1\"]=Module[\"asm\"][\"Ga\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0\"]=Module[\"asm\"][\"Ha\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1\"]=Module[\"asm\"][\"Ia\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0\"]=Module[\"asm\"][\"Ja\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1\"]=Module[\"asm\"][\"Ka\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0\"]=Module[\"asm\"][\"La\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1\"]=Module[\"asm\"][\"Ma\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0\"]=Module[\"asm\"][\"Na\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1\"]=Module[\"asm\"][\"Oa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_separationWeight_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_separationWeight_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_separationWeight_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_separationWeight_0\"]=Module[\"asm\"][\"Pa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_separationWeight_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1\"]=Module[\"asm\"][\"Qa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_updateFlags_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_updateFlags_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_updateFlags_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_updateFlags_0\"]=Module[\"asm\"][\"Ra\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_updateFlags_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1\"]=Module[\"asm\"][\"Sa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0\"]=Module[\"asm\"][\"Ta\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1\"]=Module[\"asm\"][\"Ua\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0\"]=Module[\"asm\"][\"Va\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1\"]=Module[\"asm\"][\"Wa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_userData_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_userData_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_userData_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_userData_0\"]=Module[\"asm\"][\"Xa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_userData_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_userData_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_userData_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_userData_1\"]=Module[\"asm\"][\"Ya\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams___destroy___0=Module[\"_emscripten_bind_dtCrowdAgentParams___destroy___0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams___destroy___0=Module[\"_emscripten_bind_dtCrowdAgentParams___destroy___0\"]=Module[\"asm\"][\"Za\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_NavMesh_0=Module[\"_emscripten_bind_NavMesh_NavMesh_0\"]=function(){return(_emscripten_bind_NavMesh_NavMesh_0=Module[\"_emscripten_bind_NavMesh_NavMesh_0\"]=Module[\"asm\"][\"_a\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_destroy_0=Module[\"_emscripten_bind_NavMesh_destroy_0\"]=function(){return(_emscripten_bind_NavMesh_destroy_0=Module[\"_emscripten_bind_NavMesh_destroy_0\"]=Module[\"asm\"][\"$a\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_build_5=Module[\"_emscripten_bind_NavMesh_build_5\"]=function(){return(_emscripten_bind_NavMesh_build_5=Module[\"_emscripten_bind_NavMesh_build_5\"]=Module[\"asm\"][\"ab\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_buildFromNavmeshData_1=Module[\"_emscripten_bind_NavMesh_buildFromNavmeshData_1\"]=function(){return(_emscripten_bind_NavMesh_buildFromNavmeshData_1=Module[\"_emscripten_bind_NavMesh_buildFromNavmeshData_1\"]=Module[\"asm\"][\"bb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getNavmeshData_0=Module[\"_emscripten_bind_NavMesh_getNavmeshData_0\"]=function(){return(_emscripten_bind_NavMesh_getNavmeshData_0=Module[\"_emscripten_bind_NavMesh_getNavmeshData_0\"]=Module[\"asm\"][\"cb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_freeNavmeshData_1=Module[\"_emscripten_bind_NavMesh_freeNavmeshData_1\"]=function(){return(_emscripten_bind_NavMesh_freeNavmeshData_1=Module[\"_emscripten_bind_NavMesh_freeNavmeshData_1\"]=Module[\"asm\"][\"db\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getDebugNavMesh_0=Module[\"_emscripten_bind_NavMesh_getDebugNavMesh_0\"]=function(){return(_emscripten_bind_NavMesh_getDebugNavMesh_0=Module[\"_emscripten_bind_NavMesh_getDebugNavMesh_0\"]=Module[\"asm\"][\"eb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getClosestPoint_1=Module[\"_emscripten_bind_NavMesh_getClosestPoint_1\"]=function(){return(_emscripten_bind_NavMesh_getClosestPoint_1=Module[\"_emscripten_bind_NavMesh_getClosestPoint_1\"]=Module[\"asm\"][\"fb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getRandomPointAround_2=Module[\"_emscripten_bind_NavMesh_getRandomPointAround_2\"]=function(){return(_emscripten_bind_NavMesh_getRandomPointAround_2=Module[\"_emscripten_bind_NavMesh_getRandomPointAround_2\"]=Module[\"asm\"][\"gb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_moveAlong_2=Module[\"_emscripten_bind_NavMesh_moveAlong_2\"]=function(){return(_emscripten_bind_NavMesh_moveAlong_2=Module[\"_emscripten_bind_NavMesh_moveAlong_2\"]=Module[\"asm\"][\"hb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getNavMesh_0=Module[\"_emscripten_bind_NavMesh_getNavMesh_0\"]=function(){return(_emscripten_bind_NavMesh_getNavMesh_0=Module[\"_emscripten_bind_NavMesh_getNavMesh_0\"]=Module[\"asm\"][\"ib\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_computePath_2=Module[\"_emscripten_bind_NavMesh_computePath_2\"]=function(){return(_emscripten_bind_NavMesh_computePath_2=Module[\"_emscripten_bind_NavMesh_computePath_2\"]=Module[\"asm\"][\"jb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_setDefaultQueryExtent_1=Module[\"_emscripten_bind_NavMesh_setDefaultQueryExtent_1\"]=function(){return(_emscripten_bind_NavMesh_setDefaultQueryExtent_1=Module[\"_emscripten_bind_NavMesh_setDefaultQueryExtent_1\"]=Module[\"asm\"][\"kb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getDefaultQueryExtent_0=Module[\"_emscripten_bind_NavMesh_getDefaultQueryExtent_0\"]=function(){return(_emscripten_bind_NavMesh_getDefaultQueryExtent_0=Module[\"_emscripten_bind_NavMesh_getDefaultQueryExtent_0\"]=Module[\"asm\"][\"lb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_addCylinderObstacle_3=Module[\"_emscripten_bind_NavMesh_addCylinderObstacle_3\"]=function(){return(_emscripten_bind_NavMesh_addCylinderObstacle_3=Module[\"_emscripten_bind_NavMesh_addCylinderObstacle_3\"]=Module[\"asm\"][\"mb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_addBoxObstacle_3=Module[\"_emscripten_bind_NavMesh_addBoxObstacle_3\"]=function(){return(_emscripten_bind_NavMesh_addBoxObstacle_3=Module[\"_emscripten_bind_NavMesh_addBoxObstacle_3\"]=Module[\"asm\"][\"nb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_removeObstacle_1=Module[\"_emscripten_bind_NavMesh_removeObstacle_1\"]=function(){return(_emscripten_bind_NavMesh_removeObstacle_1=Module[\"_emscripten_bind_NavMesh_removeObstacle_1\"]=Module[\"asm\"][\"ob\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_update_0=Module[\"_emscripten_bind_NavMesh_update_0\"]=function(){return(_emscripten_bind_NavMesh_update_0=Module[\"_emscripten_bind_NavMesh_update_0\"]=Module[\"asm\"][\"pb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh___destroy___0=Module[\"_emscripten_bind_NavMesh___destroy___0\"]=function(){return(_emscripten_bind_NavMesh___destroy___0=Module[\"_emscripten_bind_NavMesh___destroy___0\"]=Module[\"asm\"][\"qb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_Crowd_3=Module[\"_emscripten_bind_Crowd_Crowd_3\"]=function(){return(_emscripten_bind_Crowd_Crowd_3=Module[\"_emscripten_bind_Crowd_Crowd_3\"]=Module[\"asm\"][\"rb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_destroy_0=Module[\"_emscripten_bind_Crowd_destroy_0\"]=function(){return(_emscripten_bind_Crowd_destroy_0=Module[\"_emscripten_bind_Crowd_destroy_0\"]=Module[\"asm\"][\"sb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_addAgent_2=Module[\"_emscripten_bind_Crowd_addAgent_2\"]=function(){return(_emscripten_bind_Crowd_addAgent_2=Module[\"_emscripten_bind_Crowd_addAgent_2\"]=Module[\"asm\"][\"tb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_removeAgent_1=Module[\"_emscripten_bind_Crowd_removeAgent_1\"]=function(){return(_emscripten_bind_Crowd_removeAgent_1=Module[\"_emscripten_bind_Crowd_removeAgent_1\"]=Module[\"asm\"][\"ub\"]).apply(null,arguments)};var _emscripten_bind_Crowd_update_1=Module[\"_emscripten_bind_Crowd_update_1\"]=function(){return(_emscripten_bind_Crowd_update_1=Module[\"_emscripten_bind_Crowd_update_1\"]=Module[\"asm\"][\"vb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentPosition_1=Module[\"_emscripten_bind_Crowd_getAgentPosition_1\"]=function(){return(_emscripten_bind_Crowd_getAgentPosition_1=Module[\"_emscripten_bind_Crowd_getAgentPosition_1\"]=Module[\"asm\"][\"wb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentVelocity_1=Module[\"_emscripten_bind_Crowd_getAgentVelocity_1\"]=function(){return(_emscripten_bind_Crowd_getAgentVelocity_1=Module[\"_emscripten_bind_Crowd_getAgentVelocity_1\"]=Module[\"asm\"][\"xb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentNextTargetPath_1=Module[\"_emscripten_bind_Crowd_getAgentNextTargetPath_1\"]=function(){return(_emscripten_bind_Crowd_getAgentNextTargetPath_1=Module[\"_emscripten_bind_Crowd_getAgentNextTargetPath_1\"]=Module[\"asm\"][\"yb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentState_1=Module[\"_emscripten_bind_Crowd_getAgentState_1\"]=function(){return(_emscripten_bind_Crowd_getAgentState_1=Module[\"_emscripten_bind_Crowd_getAgentState_1\"]=Module[\"asm\"][\"zb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_overOffmeshConnection_1=Module[\"_emscripten_bind_Crowd_overOffmeshConnection_1\"]=function(){return(_emscripten_bind_Crowd_overOffmeshConnection_1=Module[\"_emscripten_bind_Crowd_overOffmeshConnection_1\"]=Module[\"asm\"][\"Ab\"]).apply(null,arguments)};var _emscripten_bind_Crowd_agentGoto_2=Module[\"_emscripten_bind_Crowd_agentGoto_2\"]=function(){return(_emscripten_bind_Crowd_agentGoto_2=Module[\"_emscripten_bind_Crowd_agentGoto_2\"]=Module[\"asm\"][\"Bb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_agentTeleport_2=Module[\"_emscripten_bind_Crowd_agentTeleport_2\"]=function(){return(_emscripten_bind_Crowd_agentTeleport_2=Module[\"_emscripten_bind_Crowd_agentTeleport_2\"]=Module[\"asm\"][\"Cb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentParameters_1=Module[\"_emscripten_bind_Crowd_getAgentParameters_1\"]=function(){return(_emscripten_bind_Crowd_getAgentParameters_1=Module[\"_emscripten_bind_Crowd_getAgentParameters_1\"]=Module[\"asm\"][\"Db\"]).apply(null,arguments)};var _emscripten_bind_Crowd_setAgentParameters_2=Module[\"_emscripten_bind_Crowd_setAgentParameters_2\"]=function(){return(_emscripten_bind_Crowd_setAgentParameters_2=Module[\"_emscripten_bind_Crowd_setAgentParameters_2\"]=Module[\"asm\"][\"Eb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_setDefaultQueryExtent_1=Module[\"_emscripten_bind_Crowd_setDefaultQueryExtent_1\"]=function(){return(_emscripten_bind_Crowd_setDefaultQueryExtent_1=Module[\"_emscripten_bind_Crowd_setDefaultQueryExtent_1\"]=Module[\"asm\"][\"Fb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getDefaultQueryExtent_0=Module[\"_emscripten_bind_Crowd_getDefaultQueryExtent_0\"]=function(){return(_emscripten_bind_Crowd_getDefaultQueryExtent_0=Module[\"_emscripten_bind_Crowd_getDefaultQueryExtent_0\"]=Module[\"asm\"][\"Gb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getCorners_1=Module[\"_emscripten_bind_Crowd_getCorners_1\"]=function(){return(_emscripten_bind_Crowd_getCorners_1=Module[\"_emscripten_bind_Crowd_getCorners_1\"]=Module[\"asm\"][\"Hb\"]).apply(null,arguments)};var _emscripten_bind_Crowd___destroy___0=Module[\"_emscripten_bind_Crowd___destroy___0\"]=function(){return(_emscripten_bind_Crowd___destroy___0=Module[\"_emscripten_bind_Crowd___destroy___0\"]=Module[\"asm\"][\"Ib\"]).apply(null,arguments)};var _malloc=Module[\"_malloc\"]=function(){return(_malloc=Module[\"_malloc\"]=Module[\"asm\"][\"Kb\"]).apply(null,arguments)};var _free=Module[\"_free\"]=function(){return(_free=Module[\"_free\"]=Module[\"asm\"][\"Lb\"]).apply(null,arguments)};Module[\"UTF8ToString\"]=UTF8ToString;Module[\"addFunction\"]=addFunction;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}Module[\"run\"]=run;if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}run();function WrapperObject(){}WrapperObject.prototype=Object.create(WrapperObject.prototype);WrapperObject.prototype.constructor=WrapperObject;WrapperObject.prototype.__class__=WrapperObject;WrapperObject.__cache__={};Module[\"WrapperObject\"]=WrapperObject;function getCache(__class__){return(__class__||WrapperObject).__cache__}Module[\"getCache\"]=getCache;function wrapPointer(ptr,__class__){var cache=getCache(__class__);var ret=cache[ptr];if(ret)return ret;ret=Object.create((__class__||WrapperObject).prototype);ret.ptr=ptr;return cache[ptr]=ret}Module[\"wrapPointer\"]=wrapPointer;function castObject(obj,__class__){return wrapPointer(obj.ptr,__class__)}Module[\"castObject\"]=castObject;Module[\"NULL\"]=wrapPointer(0);function destroy(obj){if(!obj[\"__destroy__\"])throw\"Error: Cannot destroy object. (Did you create it yourself?)\";obj[\"__destroy__\"]();delete getCache(obj.__class__)[obj.ptr]}Module[\"destroy\"]=destroy;function compare(obj1,obj2){return obj1.ptr===obj2.ptr}Module[\"compare\"]=compare;function getPointer(obj){return obj.ptr}Module[\"getPointer\"]=getPointer;function getClass(obj){return obj.__class__}Module[\"getClass\"]=getClass;var ensureCache={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(ensureCache.needed){for(var i=0;i<ensureCache.temps.length;i++){Module[\"_free\"](ensureCache.temps[i])}ensureCache.temps.length=0;Module[\"_free\"](ensureCache.buffer);ensureCache.buffer=0;ensureCache.size+=ensureCache.needed;ensureCache.needed=0}if(!ensureCache.buffer){ensureCache.size+=128;ensureCache.buffer=Module[\"_malloc\"](ensureCache.size);assert(ensureCache.buffer)}ensureCache.pos=0},alloc:function(array,view){assert(ensureCache.buffer);var bytes=view.BYTES_PER_ELEMENT;var len=array.length*bytes;len=len+7&-8;var ret;if(ensureCache.pos+len>=ensureCache.size){assert(len>0);ensureCache.needed+=len;ret=Module[\"_malloc\"](len);ensureCache.temps.push(ret)}else{ret=ensureCache.buffer+ensureCache.pos;ensureCache.pos+=len}return ret},copy:function(array,view,offset){offset>>>=0;var bytes=view.BYTES_PER_ELEMENT;switch(bytes){case 2:offset>>>=1;break;case 4:offset>>>=2;break;case 8:offset>>>=3;break}for(var i=0;i<array.length;i++){view[offset+i]=array[i]}}};function ensureInt32(value){if(typeof value===\"object\"){var offset=ensureCache.alloc(value,HEAP32);ensureCache.copy(value,HEAP32,offset);return offset}return value}function ensureFloat32(value){if(typeof value===\"object\"){var offset=ensureCache.alloc(value,HEAPF32);ensureCache.copy(value,HEAPF32,offset);return offset}return value}function VoidPtr(){throw\"cannot construct a VoidPtr, no constructor in IDL\"}VoidPtr.prototype=Object.create(WrapperObject.prototype);VoidPtr.prototype.constructor=VoidPtr;VoidPtr.prototype.__class__=VoidPtr;VoidPtr.__cache__={};Module[\"VoidPtr\"]=VoidPtr;VoidPtr.prototype[\"__destroy__\"]=VoidPtr.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_VoidPtr___destroy___0(self)};function rcConfig(){this.ptr=_emscripten_bind_rcConfig_rcConfig_0();getCache(rcConfig)[this.ptr]=this}rcConfig.prototype=Object.create(WrapperObject.prototype);rcConfig.prototype.constructor=rcConfig;rcConfig.prototype.__class__=rcConfig;rcConfig.__cache__={};Module[\"rcConfig\"]=rcConfig;rcConfig.prototype[\"get_width\"]=rcConfig.prototype.get_width=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_width_0(self)};rcConfig.prototype[\"set_width\"]=rcConfig.prototype.set_width=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_width_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"width\",{get:rcConfig.prototype.get_width,set:rcConfig.prototype.set_width});rcConfig.prototype[\"get_height\"]=rcConfig.prototype.get_height=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_height_0(self)};rcConfig.prototype[\"set_height\"]=rcConfig.prototype.set_height=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_height_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"height\",{get:rcConfig.prototype.get_height,set:rcConfig.prototype.set_height});rcConfig.prototype[\"get_tileSize\"]=rcConfig.prototype.get_tileSize=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_tileSize_0(self)};rcConfig.prototype[\"set_tileSize\"]=rcConfig.prototype.set_tileSize=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_tileSize_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"tileSize\",{get:rcConfig.prototype.get_tileSize,set:rcConfig.prototype.set_tileSize});rcConfig.prototype[\"get_borderSize\"]=rcConfig.prototype.get_borderSize=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_borderSize_0(self)};rcConfig.prototype[\"set_borderSize\"]=rcConfig.prototype.set_borderSize=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_borderSize_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"borderSize\",{get:rcConfig.prototype.get_borderSize,set:rcConfig.prototype.set_borderSize});rcConfig.prototype[\"get_cs\"]=rcConfig.prototype.get_cs=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_cs_0(self)};rcConfig.prototype[\"set_cs\"]=rcConfig.prototype.set_cs=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_cs_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"cs\",{get:rcConfig.prototype.get_cs,set:rcConfig.prototype.set_cs});rcConfig.prototype[\"get_ch\"]=rcConfig.prototype.get_ch=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_ch_0(self)};rcConfig.prototype[\"set_ch\"]=rcConfig.prototype.set_ch=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_ch_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"ch\",{get:rcConfig.prototype.get_ch,set:rcConfig.prototype.set_ch});rcConfig.prototype[\"get_bmin\"]=rcConfig.prototype.get_bmin=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;return _emscripten_bind_rcConfig_get_bmin_1(self,arg0)};rcConfig.prototype[\"set_bmin\"]=rcConfig.prototype.set_bmin=function(arg0,arg1){var self=this.ptr;ensureCache.prepare();if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;if(arg1&&typeof arg1===\"object\")arg1=arg1.ptr;_emscripten_bind_rcConfig_set_bmin_2(self,arg0,arg1)};Object.defineProperty(rcConfig.prototype,\"bmin\",{get:rcConfig.prototype.get_bmin,set:rcConfig.prototype.set_bmin});rcConfig.prototype[\"get_bmax\"]=rcConfig.prototype.get_bmax=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;return _emscripten_bind_rcConfig_get_bmax_1(self,arg0)};rcConfig.prototype[\"set_bmax\"]=rcConfig.prototype.set_bmax=function(arg0,arg1){var self=this.ptr;ensureCache.prepare();if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;if(arg1&&typeof arg1===\"object\")arg1=arg1.ptr;_emscripten_bind_rcConfig_set_bmax_2(self,arg0,arg1)};Object.defineProperty(rcConfig.prototype,\"bmax\",{get:rcConfig.prototype.get_bmax,set:rcConfig.prototype.set_bmax});rcConfig.prototype[\"get_walkableSlopeAngle\"]=rcConfig.prototype.get_walkableSlopeAngle=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableSlopeAngle_0(self)};rcConfig.prototype[\"set_walkableSlopeAngle\"]=rcConfig.prototype.set_walkableSlopeAngle=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableSlopeAngle_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableSlopeAngle\",{get:rcConfig.prototype.get_walkableSlopeAngle,set:rcConfig.prototype.set_walkableSlopeAngle});rcConfig.prototype[\"get_walkableHeight\"]=rcConfig.prototype.get_walkableHeight=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableHeight_0(self)};rcConfig.prototype[\"set_walkableHeight\"]=rcConfig.prototype.set_walkableHeight=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableHeight_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableHeight\",{get:rcConfig.prototype.get_walkableHeight,set:rcConfig.prototype.set_walkableHeight});rcConfig.prototype[\"get_walkableClimb\"]=rcConfig.prototype.get_walkableClimb=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableClimb_0(self)};rcConfig.prototype[\"set_walkableClimb\"]=rcConfig.prototype.set_walkableClimb=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableClimb_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableClimb\",{get:rcConfig.prototype.get_walkableClimb,set:rcConfig.prototype.set_walkableClimb});rcConfig.prototype[\"get_walkableRadius\"]=rcConfig.prototype.get_walkableRadius=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableRadius_0(self)};rcConfig.prototype[\"set_walkableRadius\"]=rcConfig.prototype.set_walkableRadius=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableRadius_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableRadius\",{get:rcConfig.prototype.get_walkableRadius,set:rcConfig.prototype.set_walkableRadius});rcConfig.prototype[\"get_maxEdgeLen\"]=rcConfig.prototype.get_maxEdgeLen=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_maxEdgeLen_0(self)};rcConfig.prototype[\"set_maxEdgeLen\"]=rcConfig.prototype.set_maxEdgeLen=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_maxEdgeLen_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"maxEdgeLen\",{get:rcConfig.prototype.get_maxEdgeLen,set:rcConfig.prototype.set_maxEdgeLen});rcConfig.prototype[\"get_maxSimplificationError\"]=rcConfig.prototype.get_maxSimplificationError=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_maxSimplificationError_0(self)};rcConfig.prototype[\"set_maxSimplificationError\"]=rcConfig.prototype.set_maxSimplificationError=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_maxSimplificationError_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"maxSimplificationError\",{get:rcConfig.prototype.get_maxSimplificationError,set:rcConfig.prototype.set_maxSimplificationError});rcConfig.prototype[\"get_minRegionArea\"]=rcConfig.prototype.get_minRegionArea=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_minRegionArea_0(self)};rcConfig.prototype[\"set_minRegionArea\"]=rcConfig.prototype.set_minRegionArea=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_minRegionArea_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"minRegionArea\",{get:rcConfig.prototype.get_minRegionArea,set:rcConfig.prototype.set_minRegionArea});rcConfig.prototype[\"get_mergeRegionArea\"]=rcConfig.prototype.get_mergeRegionArea=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_mergeRegionArea_0(self)};rcConfig.prototype[\"set_mergeRegionArea\"]=rcConfig.prototype.set_mergeRegionArea=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_mergeRegionArea_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"mergeRegionArea\",{get:rcConfig.prototype.get_mergeRegionArea,set:rcConfig.prototype.set_mergeRegionArea});rcConfig.prototype[\"get_maxVertsPerPoly\"]=rcConfig.prototype.get_maxVertsPerPoly=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_maxVertsPerPoly_0(self)};rcConfig.prototype[\"set_maxVertsPerPoly\"]=rcConfig.prototype.set_maxVertsPerPoly=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_maxVertsPerPoly_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"maxVertsPerPoly\",{get:rcConfig.prototype.get_maxVertsPerPoly,set:rcConfig.prototype.set_maxVertsPerPoly});rcConfig.prototype[\"get_detailSampleDist\"]=rcConfig.prototype.get_detailSampleDist=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_detailSampleDist_0(self)};rcConfig.prototype[\"set_detailSampleDist\"]=rcConfig.prototype.set_detailSampleDist=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_detailSampleDist_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"detailSampleDist\",{get:rcConfig.prototype.get_detailSampleDist,set:rcConfig.prototype.set_detailSampleDist});rcConfig.prototype[\"get_detailSampleMaxError\"]=rcConfig.prototype.get_detailSampleMaxError=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_detailSampleMaxError_0(self)};rcConfig.prototype[\"set_detailSampleMaxError\"]=rcConfig.prototype.set_detailSampleMaxError=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_detailSampleMaxError_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"detailSampleMaxError\",{get:rcConfig.prototype.get_detailSampleMaxError,set:rcConfig.prototype.set_detailSampleMaxError});rcConfig.prototype[\"__destroy__\"]=rcConfig.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_rcConfig___destroy___0(self)};function Vec3(x,y,z){if(x&&typeof x===\"object\")x=x.ptr;if(y&&typeof y===\"object\")y=y.ptr;if(z&&typeof z===\"object\")z=z.ptr;if(x===undefined){this.ptr=_emscripten_bind_Vec3_Vec3_0();getCache(Vec3)[this.ptr]=this;return}if(y===undefined){this.ptr=_emscripten_bind_Vec3_Vec3_1(x);getCache(Vec3)[this.ptr]=this;return}if(z===undefined){this.ptr=_emscripten_bind_Vec3_Vec3_2(x,y);getCache(Vec3)[this.ptr]=this;return}this.ptr=_emscripten_bind_Vec3_Vec3_3(x,y,z);getCache(Vec3)[this.ptr]=this}Vec3.prototype=Object.create(WrapperObject.prototype);Vec3.prototype.constructor=Vec3;Vec3.prototype.__class__=Vec3;Vec3.__cache__={};Module[\"Vec3\"]=Vec3;Vec3.prototype[\"get_x\"]=Vec3.prototype.get_x=function(){var self=this.ptr;return _emscripten_bind_Vec3_get_x_0(self)};Vec3.prototype[\"set_x\"]=Vec3.prototype.set_x=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_Vec3_set_x_1(self,arg0)};Object.defineProperty(Vec3.prototype,\"x\",{get:Vec3.prototype.get_x,set:Vec3.prototype.set_x});Vec3.prototype[\"get_y\"]=Vec3.prototype.get_y=function(){var self=this.ptr;return _emscripten_bind_Vec3_get_y_0(self)};Vec3.prototype[\"set_y\"]=Vec3.prototype.set_y=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_Vec3_set_y_1(self,arg0)};Object.defineProperty(Vec3.prototype,\"y\",{get:Vec3.prototype.get_y,set:Vec3.prototype.set_y});Vec3.prototype[\"get_z\"]=Vec3.prototype.get_z=function(){var self=this.ptr;return _emscripten_bind_Vec3_get_z_0(self)};Vec3.prototype[\"set_z\"]=Vec3.prototype.set_z=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_Vec3_set_z_1(self,arg0)};Object.defineProperty(Vec3.prototype,\"z\",{get:Vec3.prototype.get_z,set:Vec3.prototype.set_z});Vec3.prototype[\"__destroy__\"]=Vec3.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Vec3___destroy___0(self)};function Triangle(){this.ptr=_emscripten_bind_Triangle_Triangle_0();getCache(Triangle)[this.ptr]=this}Triangle.prototype=Object.create(WrapperObject.prototype);Triangle.prototype.constructor=Triangle;Triangle.prototype.__class__=Triangle;Triangle.__cache__={};Module[\"Triangle\"]=Triangle;Triangle.prototype[\"getPoint\"]=Triangle.prototype.getPoint=function(n){var self=this.ptr;if(n&&typeof n===\"object\")n=n.ptr;return wrapPointer(_emscripten_bind_Triangle_getPoint_1(self,n),Vec3)};Triangle.prototype[\"__destroy__\"]=Triangle.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Triangle___destroy___0(self)};function DebugNavMesh(){this.ptr=_emscripten_bind_DebugNavMesh_DebugNavMesh_0();getCache(DebugNavMesh)[this.ptr]=this}DebugNavMesh.prototype=Object.create(WrapperObject.prototype);DebugNavMesh.prototype.constructor=DebugNavMesh;DebugNavMesh.prototype.__class__=DebugNavMesh;DebugNavMesh.__cache__={};Module[\"DebugNavMesh\"]=DebugNavMesh;DebugNavMesh.prototype[\"getTriangleCount\"]=DebugNavMesh.prototype.getTriangleCount=function(){var self=this.ptr;return _emscripten_bind_DebugNavMesh_getTriangleCount_0(self)};DebugNavMesh.prototype[\"getTriangle\"]=DebugNavMesh.prototype.getTriangle=function(n){var self=this.ptr;if(n&&typeof n===\"object\")n=n.ptr;return wrapPointer(_emscripten_bind_DebugNavMesh_getTriangle_1(self,n),Triangle)};DebugNavMesh.prototype[\"__destroy__\"]=DebugNavMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DebugNavMesh___destroy___0(self)};function dtNavMesh(){throw\"cannot construct a dtNavMesh, no constructor in IDL\"}dtNavMesh.prototype=Object.create(WrapperObject.prototype);dtNavMesh.prototype.constructor=dtNavMesh;dtNavMesh.prototype.__class__=dtNavMesh;dtNavMesh.__cache__={};Module[\"dtNavMesh\"]=dtNavMesh;dtNavMesh.prototype[\"__destroy__\"]=dtNavMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_dtNavMesh___destroy___0(self)};function NavmeshData(){this.ptr=_emscripten_bind_NavmeshData_NavmeshData_0();getCache(NavmeshData)[this.ptr]=this}NavmeshData.prototype=Object.create(WrapperObject.prototype);NavmeshData.prototype.constructor=NavmeshData;NavmeshData.prototype.__class__=NavmeshData;NavmeshData.__cache__={};Module[\"NavmeshData\"]=NavmeshData;NavmeshData.prototype[\"get_dataPointer\"]=NavmeshData.prototype.get_dataPointer=function(){var self=this.ptr;return _emscripten_bind_NavmeshData_get_dataPointer_0(self)};NavmeshData.prototype[\"set_dataPointer\"]=NavmeshData.prototype.set_dataPointer=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_NavmeshData_set_dataPointer_1(self,arg0)};Object.defineProperty(NavmeshData.prototype,\"dataPointer\",{get:NavmeshData.prototype.get_dataPointer,set:NavmeshData.prototype.set_dataPointer});NavmeshData.prototype[\"get_size\"]=NavmeshData.prototype.get_size=function(){var self=this.ptr;return _emscripten_bind_NavmeshData_get_size_0(self)};NavmeshData.prototype[\"set_size\"]=NavmeshData.prototype.set_size=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_NavmeshData_set_size_1(self,arg0)};Object.defineProperty(NavmeshData.prototype,\"size\",{get:NavmeshData.prototype.get_size,set:NavmeshData.prototype.set_size});NavmeshData.prototype[\"__destroy__\"]=NavmeshData.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NavmeshData___destroy___0(self)};function NavPath(){throw\"cannot construct a NavPath, no constructor in IDL\"}NavPath.prototype=Object.create(WrapperObject.prototype);NavPath.prototype.constructor=NavPath;NavPath.prototype.__class__=NavPath;NavPath.__cache__={};Module[\"NavPath\"]=NavPath;NavPath.prototype[\"getPointCount\"]=NavPath.prototype.getPointCount=function(){var self=this.ptr;return _emscripten_bind_NavPath_getPointCount_0(self)};NavPath.prototype[\"getPoint\"]=NavPath.prototype.getPoint=function(n){var self=this.ptr;if(n&&typeof n===\"object\")n=n.ptr;return wrapPointer(_emscripten_bind_NavPath_getPoint_1(self,n),Vec3)};NavPath.prototype[\"__destroy__\"]=NavPath.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NavPath___destroy___0(self)};function dtObstacleRef(){throw\"cannot construct a dtObstacleRef, no constructor in IDL\"}dtObstacleRef.prototype=Object.create(WrapperObject.prototype);dtObstacleRef.prototype.constructor=dtObstacleRef;dtObstacleRef.prototype.__class__=dtObstacleRef;dtObstacleRef.__cache__={};Module[\"dtObstacleRef\"]=dtObstacleRef;dtObstacleRef.prototype[\"__destroy__\"]=dtObstacleRef.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_dtObstacleRef___destroy___0(self)};function dtCrowdAgentParams(){this.ptr=_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0();getCache(dtCrowdAgentParams)[this.ptr]=this}dtCrowdAgentParams.prototype=Object.create(WrapperObject.prototype);dtCrowdAgentParams.prototype.constructor=dtCrowdAgentParams;dtCrowdAgentParams.prototype.__class__=dtCrowdAgentParams;dtCrowdAgentParams.__cache__={};Module[\"dtCrowdAgentParams\"]=dtCrowdAgentParams;dtCrowdAgentParams.prototype[\"get_radius\"]=dtCrowdAgentParams.prototype.get_radius=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_radius_0(self)};dtCrowdAgentParams.prototype[\"set_radius\"]=dtCrowdAgentParams.prototype.set_radius=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_radius_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"radius\",{get:dtCrowdAgentParams.prototype.get_radius,set:dtCrowdAgentParams.prototype.set_radius});dtCrowdAgentParams.prototype[\"get_height\"]=dtCrowdAgentParams.prototype.get_height=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_height_0(self)};dtCrowdAgentParams.prototype[\"set_height\"]=dtCrowdAgentParams.prototype.set_height=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_height_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"height\",{get:dtCrowdAgentParams.prototype.get_height,set:dtCrowdAgentParams.prototype.set_height});dtCrowdAgentParams.prototype[\"get_maxAcceleration\"]=dtCrowdAgentParams.prototype.get_maxAcceleration=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0(self)};dtCrowdAgentParams.prototype[\"set_maxAcceleration\"]=dtCrowdAgentParams.prototype.set_maxAcceleration=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"maxAcceleration\",{get:dtCrowdAgentParams.prototype.get_maxAcceleration,set:dtCrowdAgentParams.prototype.set_maxAcceleration});dtCrowdAgentParams.prototype[\"get_maxSpeed\"]=dtCrowdAgentParams.prototype.get_maxSpeed=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0(self)};dtCrowdAgentParams.prototype[\"set_maxSpeed\"]=dtCrowdAgentParams.prototype.set_maxSpeed=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"maxSpeed\",{get:dtCrowdAgentParams.prototype.get_maxSpeed,set:dtCrowdAgentParams.prototype.set_maxSpeed});dtCrowdAgentParams.prototype[\"get_collisionQueryRange\"]=dtCrowdAgentParams.prototype.get_collisionQueryRange=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0(self)};dtCrowdAgentParams.prototype[\"set_collisionQueryRange\"]=dtCrowdAgentParams.prototype.set_collisionQueryRange=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"collisionQueryRange\",{get:dtCrowdAgentParams.prototype.get_collisionQueryRange,set:dtCrowdAgentParams.prototype.set_collisionQueryRange});dtCrowdAgentParams.prototype[\"get_pathOptimizationRange\"]=dtCrowdAgentParams.prototype.get_pathOptimizationRange=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0(self)};dtCrowdAgentParams.prototype[\"set_pathOptimizationRange\"]=dtCrowdAgentParams.prototype.set_pathOptimizationRange=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"pathOptimizationRange\",{get:dtCrowdAgentParams.prototype.get_pathOptimizationRange,set:dtCrowdAgentParams.prototype.set_pathOptimizationRange});dtCrowdAgentParams.prototype[\"get_separationWeight\"]=dtCrowdAgentParams.prototype.get_separationWeight=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_separationWeight_0(self)};dtCrowdAgentParams.prototype[\"set_separationWeight\"]=dtCrowdAgentParams.prototype.set_separationWeight=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"separationWeight\",{get:dtCrowdAgentParams.prototype.get_separationWeight,set:dtCrowdAgentParams.prototype.set_separationWeight});dtCrowdAgentParams.prototype[\"get_updateFlags\"]=dtCrowdAgentParams.prototype.get_updateFlags=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_updateFlags_0(self)};dtCrowdAgentParams.prototype[\"set_updateFlags\"]=dtCrowdAgentParams.prototype.set_updateFlags=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"updateFlags\",{get:dtCrowdAgentParams.prototype.get_updateFlags,set:dtCrowdAgentParams.prototype.set_updateFlags});dtCrowdAgentParams.prototype[\"get_obstacleAvoidanceType\"]=dtCrowdAgentParams.prototype.get_obstacleAvoidanceType=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0(self)};dtCrowdAgentParams.prototype[\"set_obstacleAvoidanceType\"]=dtCrowdAgentParams.prototype.set_obstacleAvoidanceType=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"obstacleAvoidanceType\",{get:dtCrowdAgentParams.prototype.get_obstacleAvoidanceType,set:dtCrowdAgentParams.prototype.set_obstacleAvoidanceType});dtCrowdAgentParams.prototype[\"get_queryFilterType\"]=dtCrowdAgentParams.prototype.get_queryFilterType=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0(self)};dtCrowdAgentParams.prototype[\"set_queryFilterType\"]=dtCrowdAgentParams.prototype.set_queryFilterType=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"queryFilterType\",{get:dtCrowdAgentParams.prototype.get_queryFilterType,set:dtCrowdAgentParams.prototype.set_queryFilterType});dtCrowdAgentParams.prototype[\"get_userData\"]=dtCrowdAgentParams.prototype.get_userData=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_dtCrowdAgentParams_get_userData_0(self),VoidPtr)};dtCrowdAgentParams.prototype[\"set_userData\"]=dtCrowdAgentParams.prototype.set_userData=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_userData_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"userData\",{get:dtCrowdAgentParams.prototype.get_userData,set:dtCrowdAgentParams.prototype.set_userData});dtCrowdAgentParams.prototype[\"__destroy__\"]=dtCrowdAgentParams.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_dtCrowdAgentParams___destroy___0(self)};function NavMesh(){this.ptr=_emscripten_bind_NavMesh_NavMesh_0();getCache(NavMesh)[this.ptr]=this}NavMesh.prototype=Object.create(WrapperObject.prototype);NavMesh.prototype.constructor=NavMesh;NavMesh.prototype.__class__=NavMesh;NavMesh.__cache__={};Module[\"NavMesh\"]=NavMesh;NavMesh.prototype[\"destroy\"]=NavMesh.prototype.destroy=function(){var self=this.ptr;_emscripten_bind_NavMesh_destroy_0(self)};NavMesh.prototype[\"build\"]=NavMesh.prototype.build=function(positions,positionCount,indices,indexCount,config){var self=this.ptr;ensureCache.prepare();if(typeof positions==\"object\"){positions=ensureFloat32(positions)}if(positionCount&&typeof positionCount===\"object\")positionCount=positionCount.ptr;if(typeof indices==\"object\"){indices=ensureInt32(indices)}if(indexCount&&typeof indexCount===\"object\")indexCount=indexCount.ptr;if(config&&typeof config===\"object\")config=config.ptr;_emscripten_bind_NavMesh_build_5(self,positions,positionCount,indices,indexCount,config)};NavMesh.prototype[\"buildFromNavmeshData\"]=NavMesh.prototype.buildFromNavmeshData=function(data){var self=this.ptr;if(data&&typeof data===\"object\")data=data.ptr;_emscripten_bind_NavMesh_buildFromNavmeshData_1(self,data)};NavMesh.prototype[\"getNavmeshData\"]=NavMesh.prototype.getNavmeshData=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getNavmeshData_0(self),NavmeshData)};NavMesh.prototype[\"freeNavmeshData\"]=NavMesh.prototype.freeNavmeshData=function(data){var self=this.ptr;if(data&&typeof data===\"object\")data=data.ptr;_emscripten_bind_NavMesh_freeNavmeshData_1(self,data)};NavMesh.prototype[\"getDebugNavMesh\"]=NavMesh.prototype.getDebugNavMesh=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getDebugNavMesh_0(self),DebugNavMesh)};NavMesh.prototype[\"getClosestPoint\"]=NavMesh.prototype.getClosestPoint=function(position){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;return wrapPointer(_emscripten_bind_NavMesh_getClosestPoint_1(self,position),Vec3)};NavMesh.prototype[\"getRandomPointAround\"]=NavMesh.prototype.getRandomPointAround=function(position,maxRadius){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(maxRadius&&typeof maxRadius===\"object\")maxRadius=maxRadius.ptr;return wrapPointer(_emscripten_bind_NavMesh_getRandomPointAround_2(self,position,maxRadius),Vec3)};NavMesh.prototype[\"moveAlong\"]=NavMesh.prototype.moveAlong=function(position,destination){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(destination&&typeof destination===\"object\")destination=destination.ptr;return wrapPointer(_emscripten_bind_NavMesh_moveAlong_2(self,position,destination),Vec3)};NavMesh.prototype[\"getNavMesh\"]=NavMesh.prototype.getNavMesh=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getNavMesh_0(self),dtNavMesh)};NavMesh.prototype[\"computePath\"]=NavMesh.prototype.computePath=function(start,end){var self=this.ptr;if(start&&typeof start===\"object\")start=start.ptr;if(end&&typeof end===\"object\")end=end.ptr;return wrapPointer(_emscripten_bind_NavMesh_computePath_2(self,start,end),NavPath)};NavMesh.prototype[\"setDefaultQueryExtent\"]=NavMesh.prototype.setDefaultQueryExtent=function(extent){var self=this.ptr;if(extent&&typeof extent===\"object\")extent=extent.ptr;_emscripten_bind_NavMesh_setDefaultQueryExtent_1(self,extent)};NavMesh.prototype[\"getDefaultQueryExtent\"]=NavMesh.prototype.getDefaultQueryExtent=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getDefaultQueryExtent_0(self),Vec3)};NavMesh.prototype[\"addCylinderObstacle\"]=NavMesh.prototype.addCylinderObstacle=function(position,radius,height){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(radius&&typeof radius===\"object\")radius=radius.ptr;if(height&&typeof height===\"object\")height=height.ptr;return wrapPointer(_emscripten_bind_NavMesh_addCylinderObstacle_3(self,position,radius,height),dtObstacleRef)};NavMesh.prototype[\"addBoxObstacle\"]=NavMesh.prototype.addBoxObstacle=function(position,extent,angle){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(extent&&typeof extent===\"object\")extent=extent.ptr;if(angle&&typeof angle===\"object\")angle=angle.ptr;return wrapPointer(_emscripten_bind_NavMesh_addBoxObstacle_3(self,position,extent,angle),dtObstacleRef)};NavMesh.prototype[\"removeObstacle\"]=NavMesh.prototype.removeObstacle=function(obstacle){var self=this.ptr;if(obstacle&&typeof obstacle===\"object\")obstacle=obstacle.ptr;_emscripten_bind_NavMesh_removeObstacle_1(self,obstacle)};NavMesh.prototype[\"update\"]=NavMesh.prototype.update=function(){var self=this.ptr;_emscripten_bind_NavMesh_update_0(self)};NavMesh.prototype[\"__destroy__\"]=NavMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NavMesh___destroy___0(self)};function Crowd(maxAgents,maxAgentRadius,nav){if(maxAgents&&typeof maxAgents===\"object\")maxAgents=maxAgents.ptr;if(maxAgentRadius&&typeof maxAgentRadius===\"object\")maxAgentRadius=maxAgentRadius.ptr;if(nav&&typeof nav===\"object\")nav=nav.ptr;this.ptr=_emscripten_bind_Crowd_Crowd_3(maxAgents,maxAgentRadius,nav);getCache(Crowd)[this.ptr]=this}Crowd.prototype=Object.create(WrapperObject.prototype);Crowd.prototype.constructor=Crowd;Crowd.prototype.__class__=Crowd;Crowd.__cache__={};Module[\"Crowd\"]=Crowd;Crowd.prototype[\"destroy\"]=Crowd.prototype.destroy=function(){var self=this.ptr;_emscripten_bind_Crowd_destroy_0(self)};Crowd.prototype[\"addAgent\"]=Crowd.prototype.addAgent=function(position,params){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(params&&typeof params===\"object\")params=params.ptr;return _emscripten_bind_Crowd_addAgent_2(self,position,params)};Crowd.prototype[\"removeAgent\"]=Crowd.prototype.removeAgent=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;_emscripten_bind_Crowd_removeAgent_1(self,idx)};Crowd.prototype[\"update\"]=Crowd.prototype.update=function(dt){var self=this.ptr;if(dt&&typeof dt===\"object\")dt=dt.ptr;_emscripten_bind_Crowd_update_1(self,dt)};Crowd.prototype[\"getAgentPosition\"]=Crowd.prototype.getAgentPosition=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentPosition_1(self,idx),Vec3)};Crowd.prototype[\"getAgentVelocity\"]=Crowd.prototype.getAgentVelocity=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentVelocity_1(self,idx),Vec3)};Crowd.prototype[\"getAgentNextTargetPath\"]=Crowd.prototype.getAgentNextTargetPath=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentNextTargetPath_1(self,idx),Vec3)};Crowd.prototype[\"getAgentState\"]=Crowd.prototype.getAgentState=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return _emscripten_bind_Crowd_getAgentState_1(self,idx)};Crowd.prototype[\"overOffmeshConnection\"]=Crowd.prototype.overOffmeshConnection=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return!!_emscripten_bind_Crowd_overOffmeshConnection_1(self,idx)};Crowd.prototype[\"agentGoto\"]=Crowd.prototype.agentGoto=function(idx,destination){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;if(destination&&typeof destination===\"object\")destination=destination.ptr;_emscripten_bind_Crowd_agentGoto_2(self,idx,destination)};Crowd.prototype[\"agentTeleport\"]=Crowd.prototype.agentTeleport=function(idx,destination){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;if(destination&&typeof destination===\"object\")destination=destination.ptr;_emscripten_bind_Crowd_agentTeleport_2(self,idx,destination)};Crowd.prototype[\"getAgentParameters\"]=Crowd.prototype.getAgentParameters=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentParameters_1(self,idx),dtCrowdAgentParams)};Crowd.prototype[\"setAgentParameters\"]=Crowd.prototype.setAgentParameters=function(idx,params){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;if(params&&typeof params===\"object\")params=params.ptr;_emscripten_bind_Crowd_setAgentParameters_2(self,idx,params)};Crowd.prototype[\"setDefaultQueryExtent\"]=Crowd.prototype.setDefaultQueryExtent=function(extent){var self=this.ptr;if(extent&&typeof extent===\"object\")extent=extent.ptr;_emscripten_bind_Crowd_setDefaultQueryExtent_1(self,extent)};Crowd.prototype[\"getDefaultQueryExtent\"]=Crowd.prototype.getDefaultQueryExtent=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_Crowd_getDefaultQueryExtent_0(self),Vec3)};Crowd.prototype[\"getCorners\"]=Crowd.prototype.getCorners=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getCorners_1(self,idx),NavPath)};Crowd.prototype[\"__destroy__\"]=Crowd.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Crowd___destroy___0(self)};\n\n\n  return Module.ready\n}\n);\n})();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Module);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../node_modules/@splinetool/runtime/build/navmesh.js\n"));

/***/ })

}]);
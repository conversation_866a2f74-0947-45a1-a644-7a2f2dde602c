import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const Pyramid = () => {
  const [code, setCode] = useState(`
for i in range(1, 6):
    print(' ' * (5 - i) + '$' * (2 * i - 1))
  `);
  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);

  const outputRef = useRef(null);

  const typedText = useTypingAnimation("Pyramid!", 100);

  const [animatedCode, setAnimatedCode] = useState("");
  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);


  const runCode = () => {
    try {
      const expectedCode = `
for i in range(1, 6):
    print(' ' * (5 - i) + '$' * (2 * i - 1))
      `.trim();

      if (code.trim() === expectedCode) {
        setOutput(`    $\n   $$$\n  $$$$$\n $$$$$$$\n$$$$$$$$$`);
        setShowOutput(true);
        setShowConfetti(true);
      } else {
        setOutput("Error: Please Correct The Code!");
        setShowOutput(true);
        setShowConfetti(false);
      }

      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);

      setTimeout(() => {
        document
          .querySelector(`.${styles.confetti}`)
          ?.classList.add(styles.hidden);
        setTimeout(() => setShowConfetti(false), 500);
      }, 1000);
    } catch (error) {
      console.error(error);
      setOutput("Error: Incorrect Code! Please try again.");
      setShowOutput(true);
    }
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {
      setCode(newCode);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #03045e, #0077b6)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "800px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #03045e, #0077b6)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
        <p className="text-lg text-gray-700">
          Python runs code one line at a time, from top to bottom.
        </p>
        <p className="mt-4 text-gray-700">
          We can print multiple messages by using multiple{" "}
          <code className="font-mono text-blue-500">print()</code> functions.
          For example, if we want to show two greetings:
        </p>
        <div className="bg-gray-100 p-4 rounded-lg text-left mt-4">
        <pre className="text-sm text-gray-800">
            {`print('🍕 Pizza time!')
          print('☕ Coffee break!')`}
          </pre>
          <p className="mt-2 text-gray-800">This will output:</p>
          <pre className="mt-2 text-sm text-gray-800">
            🍕 Pizza time!{"\n"}☕ Coffee break!
          </pre>
        </div>
        <hr className="my-6 border-t border-gray-300" />
        <p className="text-lg font-bold text-black mt-6">INSTRUCTIONS</p>
        <p className="mt-6 text-gray-700">
          Suppose we want the output to look exactly like this cool pyramid
          pattern using dollar signs:
        </p>
        <pre className="mt-4 text-lg font-mono text-gray-800">
          {"    $"}
          {"\n"}
          {"   $$$"}
          {"\n"}
          {"  $$$$$"}
          {"\n"}
          {" $$$$$$$"}
          {"\n"}
          {"$$$$$$$$$"}
        </pre>
        <p className="mt-4 text-gray-700">How can you do that?</p>
        <p className="text-gray-700">
          Create a <code className="font-mono text-blue-500">pyramid.py</code>{" "}
          program that prints this pattern exactly as shown.
        </p>
        <p className="mt-2 text-gray-700">
          It might take some trial and error, but give it a try!
        </p>
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#c8e2a1",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#e9f5db",
              color: "#d4a373",
              minHeight: "120px",
              borderRadius: "10px",
            }}
          />
        </div>
        <div>
          {}
          {!showOutput && (
            <button
              onClick={runCode}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: "#ccd5ae",
                color: "#432818",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
              }}
            >
              Run Code 🎉
            </button>
          )}

          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "76%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#e3d5ca",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#524C4F",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#444", marginTop: "10px" }}
              >
                {output}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Pyramid;

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandOpenvpn = createReactComponent("outline", "brand-openvpn", "IconBrandOpenvpn", [["path", { "d": "M15.618 20.243l-2.193 -5.602a3 3 0 1 0 -2.849 0l-2.193 5.603", "key": "svg-0" }], ["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-1" }]]);

export { IconBrandOpenvpn as default };
//# sourceMappingURL=IconBrandOpenvpn.mjs.map

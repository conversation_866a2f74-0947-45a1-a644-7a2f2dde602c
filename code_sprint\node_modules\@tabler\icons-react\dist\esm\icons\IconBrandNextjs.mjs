/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandNextjs = createReactComponent("outline", "brand-nextjs", "IconBrandNextjs", [["path", { "d": "M9 15v-6l7.745 10.65a9 9 0 1 1 2.255 -1.993", "key": "svg-0" }], ["path", { "d": "M15 12v-3", "key": "svg-1" }]]);

export { IconBrandNextjs as default };
//# sourceMappingURL=IconBrandNextjs.mjs.map

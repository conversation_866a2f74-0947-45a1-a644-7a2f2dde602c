/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandMatrix = createReactComponent("outline", "brand-matrix", "IconBrandMatrix", [["path", { "d": "M4 3h-1v18h1", "key": "svg-0" }], ["path", { "d": "M20 21h1v-18h-1", "key": "svg-1" }], ["path", { "d": "M7 9v6", "key": "svg-2" }], ["path", { "d": "M12 15v-3.5a2.5 2.5 0 1 0 -5 0v.5", "key": "svg-3" }], ["path", { "d": "M17 15v-3.5a2.5 2.5 0 1 0 -5 0v.5", "key": "svg-4" }]]);

export { IconBrandMatrix as default };
//# sourceMappingURL=IconBrandMatrix.mjs.map

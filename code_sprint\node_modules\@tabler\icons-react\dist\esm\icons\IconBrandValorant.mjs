/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandValorant = createReactComponent("outline", "brand-valorant", "IconBrandValorant", [["path", { "d": "M14.5 14h4.5l2 -2v-6z", "key": "svg-0" }], ["path", { "d": "M9 19h5l-11 -13v6z", "key": "svg-1" }]]);

export { IconBrandValorant as default };
//# sourceMappingURL=IconBrandValorant.mjs.map

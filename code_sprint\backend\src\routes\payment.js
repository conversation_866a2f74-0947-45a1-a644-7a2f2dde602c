const express = require('express');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const dbConnect = require('../utils/dbConnect');
const { getHashedString } = require('../utils/common');

const router = express.Router();

// Process payment route
router.post('/process', async (req, res) => {
  await dbConnect();

  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided or invalid token format' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const { level, amount } = req.body;

    if (!level) {
      return res.status(400).json({ message: 'Level and amount are required' });
    }

    // Paycaps payment integration here
    const orderId = `ORDER_${Date.now()}_${user._id.toString()}`;
    
    const paymentParams = {
      APP_ID: process.env.PAYCAPS_APP_ID,
      ORDER_ID: orderId,
      RETURN_URL: `${req.headers.origin}/api/payment/callback`,
      MERCHANTNAME: process.env.PAYCAPS_MERCHANT_NAME,
      AMOUNT: amount * 100, // Amount in phils/paisa/cents
      CURRENCY_CODE: 784, // Numerical code for AED as in paycaps documentation
      TXNTYPE: 'SALE',
      CUST_NAME: `${user.firstName} ${user.lastName}` || 'test_name',
      CUST_EMAIL: user.email || '<EMAIL>',
      CUST_PHONE: user.parentPhone || 526395565,
      CUST_CITY: user.emirate || 'test_city',
      CUST_COUNTRY: user.country || 'United Arab Emirates',
      CUST_ZIP: user.zip || 123,
    };
    
    const hash = getHashedString(paymentParams);
    paymentParams.HASH = hash;

    try {
      await User.findByIdAndUpdate(decoded.userId, {
        pendingOrderId: orderId,
        pendingPremiumLevel: level,
      });
      
      return res.status(200).json({ 
        message: 'Payment initiated', 
        paymentParams 
      });
      
    } catch (error) {
      console.error('Payment initiation failed:', error);
      return res.status(500).json({ message: 'Payment initiation failed', error: error.message });
    }    
  } catch (error) {
    console.error('Error during payment processing:', error);
    res.status(500).json({ message: 'Payment failed', error: error.message });
  }
});

// Payment callback route
router.post('/callback', async (req, res) => {
  await dbConnect();
  
  try {
    const {
      APP_ID,
      ORDER_ID,
      AMOUNT,
      CURRENCY_CODE,
      TXNTYPE,
      CUST_NAME,
      CUST_EMAIL,
      CUST_PHONE,
      CUST_CITY,
      CUST_COUNTRY,
      CUST_ZIP,
      MERCHANTNAME,
      RETURN_URL,
      RESPONSE_CODE,
      RESPONSE_MESSAGE,
    } = req.body;
    
    const responseParams = {
      APP_ID,
      ORDER_ID,
      AMOUNT,
      CURRENCY_CODE,
      TXNTYPE,
      CUST_NAME,
      CUST_EMAIL,
      CUST_PHONE,
      CUST_CITY,
      CUST_COUNTRY,
      CUST_ZIP,
      MERCHANTNAME,
      RETURN_URL
    };
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Response Params:\n', JSON.stringify(responseParams, null, 3));
    }
    
    const user = await User.findOne({ pendingOrderId: ORDER_ID });
    console.log('User:\n', JSON.stringify(user, null, 3));
    
    if (!user) {
      return res.redirect(302, `${process.env.FRONTEND_URL}/payment/failed?orderId=${ORDER_ID}`);
    }
    
    if (RESPONSE_CODE === '000') {
      user.isPremium = true;
      user.premiumLevel = user.pendingPremiumLevel;
      user.pendingOrderId = null;
      user.pendingPremiumLevel = null;
      await user.save();
      return res.redirect(302, `${process.env.FRONTEND_URL}/payment/success?orderId=${ORDER_ID}`);
    } else {
      // Payment failed
      user.pendingOrderId = null;
      user.pendingPremiumLevel = null;
      await user.save();
      return res.redirect(302, `${process.env.FRONTEND_URL}/payment/failed?orderId=${ORDER_ID}&message=${RESPONSE_MESSAGE}`);
    }
  } catch (error) {
    console.error('Error processing payment callback:', error);
    return res.status(500).json({ message: 'Error processing payment callback', error: error.message });
  }
});

module.exports = router;

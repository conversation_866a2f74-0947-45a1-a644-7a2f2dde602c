import React from "react";
import Link from "next/link";

const StepThree = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 3: Create Your First Project</h1>
        <p className="text-center mt-2 text-lg">Let’s build something cool in Scratch!</p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">Let’s Make a Talking Sprite!</h2>
          <p className="text-lg mb-4">
            In this step, we’ll create a simple Scratch project where a sprite says "Hello!" when you click the green flag. Follow these
            steps:
          </p>

          {/* Step-by-Step Instructions */}
          <ol className="list-decimal pl-6 space-y-4">
            <li>
              Open the Scratch editor by clicking{" "}
              <a
                href="https://scratch.mit.edu/create"
                target="_blank"
                rel="noreferrer"
                className="text-blue-600 underline"
              >
                here
              </a>
              .
            </li>
            <li>
              Drag the <span className="text-purple-700 font-semibold">"when green flag clicked"</span> block from the Events section into
              the workspace.
            </li>
            <li>
              Drag the <span className="text-purple-700 font-semibold">"say [Hello!] for [2] seconds"</span> block from the Looks section
              and connect it under the green flag block.
            </li>
            <li>Click the green flag to test your project. Your sprite should say "Hello!"</li>
          </ol>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 142615.png"
              alt="Scratch Project Illustration"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Challenge: Customize Your Sprite!</h3>
            <p className="text-lg mb-4">
              Try changing what the sprite says or the amount of time it says it. You can even add another block to make it move or change
              its costume!
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-2">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 2</button>
        </Link>
        <Link href="/lessons/level1/step-4">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Next: Step 4</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepThree;

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandLinqpad = createReactComponent("outline", "brand-linqpad", "IconBrandLinqpad", [["path", { "d": "M5 21h3.5l2.5 -6l2.5 -1l2.5 7h4l1 -4.5l-2 -1l-7 -12l-6 -.5l1.5 4l2.5 .5l1 2.5l-7 8z", "key": "svg-0" }]]);

export { IconBrandLinqpad as default };
//# sourceMappingURL=IconBrandLinqpad.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandTesla = createReactComponent("outline", "brand-tesla", "IconBrandTesla", [["path", { "d": "M12 21l3 -11c2.359 0 3 0 3 1c0 0 1.18 -1.745 2 -3c-3.077 -1.464 -6 -1 -6 -1l-2 2l-2 -2s-2.923 -.464 -6 1c.82 1.255 2 3 2 3c0 -1 .744 -1 3 -1z", "key": "svg-0" }], ["path", { "d": "M20 5c-5.114 -2 -10.886 -2 -16 0", "key": "svg-1" }]]);

export { IconBrandTesla as default };
//# sourceMappingURL=IconBrandTesla.mjs.map

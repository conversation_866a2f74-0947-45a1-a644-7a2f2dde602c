/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandWhatsapp = createReactComponent("outline", "brand-whatsapp", "IconBrandWhatsapp", [["path", { "d": "M3 21l1.65 -3.8a9 9 0 1 1 3.4 2.9l-5.05 .9", "key": "svg-0" }], ["path", { "d": "M9 10a.5 .5 0 0 0 1 0v-1a.5 .5 0 0 0 -1 0v1a5 5 0 0 0 5 5h1a.5 .5 0 0 0 0 -1h-1a.5 .5 0 0 0 0 1", "key": "svg-1" }]]);

export { IconBrandWhatsapp as default };
//# sourceMappingURL=IconBrandWhatsapp.mjs.map

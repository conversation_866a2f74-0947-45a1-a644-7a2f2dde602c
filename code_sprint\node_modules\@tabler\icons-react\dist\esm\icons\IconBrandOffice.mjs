/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandOffice = createReactComponent("outline", "brand-office", "IconBrandOffice", [["path", { "d": "M4 18h9v-12l-5 2v5l-4 2v-8l9 -4l7 2v13l-7 3z", "key": "svg-0" }]]);

export { IconBrandOffice as default };
//# sourceMappingURL=IconBrandOffice.mjs.map

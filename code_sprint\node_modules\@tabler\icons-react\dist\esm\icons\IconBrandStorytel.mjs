/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandStorytel = createReactComponent("outline", "brand-storytel", "IconBrandStorytel", [["path", { "d": "M4.103 22c2.292 -2.933 16.825 -2.43 16.825 -11.538c0 -6.298 -4.974 -8.462 -8.451 -8.462c-3.477 0 -9.477 3.036 -9.477 11.241c0 6.374 1.103 8.759 1.103 8.759z", "key": "svg-0" }]]);

export { IconBrandStorytel as default };
//# sourceMappingURL=IconBrandStorytel.mjs.map

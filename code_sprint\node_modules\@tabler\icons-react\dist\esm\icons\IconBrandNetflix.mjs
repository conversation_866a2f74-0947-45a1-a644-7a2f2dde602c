/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandNetflix = createReactComponent("outline", "brand-netflix", "IconBrandNetflix", [["path", { "d": "M9 3l10 18h-4l-10 -18z", "key": "svg-0" }], ["path", { "d": "M5 3v18h4v-10.5", "key": "svg-1" }], ["path", { "d": "M19 21v-18h-4v10.5", "key": "svg-2" }]]);

export { IconBrandNetflix as default };
//# sourceMappingURL=IconBrandNetflix.mjs.map

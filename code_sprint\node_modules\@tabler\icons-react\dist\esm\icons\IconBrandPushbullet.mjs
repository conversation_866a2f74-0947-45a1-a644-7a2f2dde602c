/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandPushbullet = createReactComponent("outline", "brand-pushbullet", "IconBrandPushbullet", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M11 8v8h2a4 4 0 1 0 0 -8h-2z", "key": "svg-1" }], ["path", { "d": "M8 8v8", "key": "svg-2" }]]);

export { IconBrandPushbullet as default };
//# sourceMappingURL=IconBrandPushbullet.mjs.map

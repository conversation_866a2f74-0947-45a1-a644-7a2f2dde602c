"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_howler_js"],{

/***/ "(pages-dir-browser)/../node_modules/@splinetool/runtime/build/howler.js":
/*!***********************************************************!*\
  !*** ../node_modules/@splinetool/runtime/build/howler.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/*! howler.js v2.2.3 | (c) 2013-2020, James Simpson of GoldFire Studios | MIT License | howlerjs.com */\n!function(){\"use strict\";var e=function(){this.init()};e.prototype={init:function(){var e=this||n;return e._counter=1e3,e._html5AudioPool=[],e.html5PoolSize=10,e._codecs={},e._howls=[],e._muted=!1,e._volume=1,e._canPlayEvent=\"canplaythrough\",e._navigator=\"undefined\"!=typeof window&&window.navigator?window.navigator:null,e.masterGain=null,e.noAudio=!1,e.usingWebAudio=!0,e.autoSuspend=!0,e.ctx=null,e.autoUnlock=!0,e._setup(),e},volume:function(e){var o=this||n;if(e=parseFloat(e),o.ctx||_(),void 0!==e&&e>=0&&e<=1){if(o._volume=e,o._muted)return o;o.usingWebAudio&&o.masterGain.gain.setValueAtTime(e,n.ctx.currentTime);for(var t=0;t<o._howls.length;t++)if(!o._howls[t]._webAudio)for(var r=o._howls[t]._getSoundIds(),a=0;a<r.length;a++){var u=o._howls[t]._soundById(r[a]);u&&u._node&&(u._node.volume=u._volume*e)}return o}return o._volume},mute:function(e){var o=this||n;o.ctx||_(),o._muted=e,o.usingWebAudio&&o.masterGain.gain.setValueAtTime(e?0:o._volume,n.ctx.currentTime);for(var t=0;t<o._howls.length;t++)if(!o._howls[t]._webAudio)for(var r=o._howls[t]._getSoundIds(),a=0;a<r.length;a++){var u=o._howls[t]._soundById(r[a]);u&&u._node&&(u._node.muted=!!e||u._muted)}return o},stop:function(){for(var e=this||n,o=0;o<e._howls.length;o++)e._howls[o].stop();return e},unload:function(){for(var e=this||n,o=e._howls.length-1;o>=0;o--)e._howls[o].unload();return e.usingWebAudio&&e.ctx&&void 0!==e.ctx.close&&(e.ctx.close(),e.ctx=null,_()),e},codecs:function(e){return(this||n)._codecs[e.replace(/^x-/,\"\")]},_setup:function(){var e=this||n;if(e.state=e.ctx?e.ctx.state||\"suspended\":\"suspended\",e._autoSuspend(),!e.usingWebAudio)if(\"undefined\"!=typeof Audio)try{var o=new Audio;void 0===o.oncanplaythrough&&(e._canPlayEvent=\"canplay\")}catch(n){e.noAudio=!0}else e.noAudio=!0;try{var o=new Audio;o.muted&&(e.noAudio=!0)}catch(e){}return e.noAudio||e._setupCodecs(),e},_setupCodecs:function(){var e=this||n,o=null;try{o=\"undefined\"!=typeof Audio?new Audio:null}catch(n){return e}if(!o||\"function\"!=typeof o.canPlayType)return e;var t=o.canPlayType(\"audio/mpeg;\").replace(/^no$/,\"\"),r=e._navigator?e._navigator.userAgent:\"\",a=r.match(/OPR\\/([0-6].)/g),u=a&&parseInt(a[0].split(\"/\")[1],10)<33,d=-1!==r.indexOf(\"Safari\")&&-1===r.indexOf(\"Chrome\"),i=r.match(/Version\\/(.*?) /),_=d&&i&&parseInt(i[1],10)<15;return e._codecs={mp3:!(u||!t&&!o.canPlayType(\"audio/mp3;\").replace(/^no$/,\"\")),mpeg:!!t,opus:!!o.canPlayType('audio/ogg; codecs=\"opus\"').replace(/^no$/,\"\"),ogg:!!o.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/,\"\"),oga:!!o.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/,\"\"),wav:!!(o.canPlayType('audio/wav; codecs=\"1\"')||o.canPlayType(\"audio/wav\")).replace(/^no$/,\"\"),aac:!!o.canPlayType(\"audio/aac;\").replace(/^no$/,\"\"),caf:!!o.canPlayType(\"audio/x-caf;\").replace(/^no$/,\"\"),m4a:!!(o.canPlayType(\"audio/x-m4a;\")||o.canPlayType(\"audio/m4a;\")||o.canPlayType(\"audio/aac;\")).replace(/^no$/,\"\"),m4b:!!(o.canPlayType(\"audio/x-m4b;\")||o.canPlayType(\"audio/m4b;\")||o.canPlayType(\"audio/aac;\")).replace(/^no$/,\"\"),mp4:!!(o.canPlayType(\"audio/x-mp4;\")||o.canPlayType(\"audio/mp4;\")||o.canPlayType(\"audio/aac;\")).replace(/^no$/,\"\"),weba:!(_||!o.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/,\"\")),webm:!(_||!o.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/,\"\")),dolby:!!o.canPlayType('audio/mp4; codecs=\"ec-3\"').replace(/^no$/,\"\"),flac:!!(o.canPlayType(\"audio/x-flac;\")||o.canPlayType(\"audio/flac;\")).replace(/^no$/,\"\")},e},_unlockAudio:function(){var e=this||n;if(!e._audioUnlocked&&e.ctx){e._audioUnlocked=!1,e.autoUnlock=!1,e._mobileUnloaded||44100===e.ctx.sampleRate||(e._mobileUnloaded=!0,e.unload()),e._scratchBuffer=e.ctx.createBuffer(1,1,22050);var o=function(n){for(;e._html5AudioPool.length<e.html5PoolSize;)try{var t=new Audio;t._unlocked=!0,e._releaseHtml5Audio(t)}catch(n){e.noAudio=!0;break}for(var r=0;r<e._howls.length;r++)if(!e._howls[r]._webAudio)for(var a=e._howls[r]._getSoundIds(),u=0;u<a.length;u++){var d=e._howls[r]._soundById(a[u]);d&&d._node&&!d._node._unlocked&&(d._node._unlocked=!0,d._node.load())}e._autoResume();var i=e.ctx.createBufferSource();i.buffer=e._scratchBuffer,i.connect(e.ctx.destination),void 0===i.start?i.noteOn(0):i.start(0),\"function\"==typeof e.ctx.resume&&e.ctx.resume(),i.onended=function(){i.disconnect(0),e._audioUnlocked=!0,document.removeEventListener(\"touchstart\",o,!0),document.removeEventListener(\"touchend\",o,!0),document.removeEventListener(\"click\",o,!0),document.removeEventListener(\"keydown\",o,!0);for(var n=0;n<e._howls.length;n++)e._howls[n]._emit(\"unlock\")}};return document.addEventListener(\"touchstart\",o,!0),document.addEventListener(\"touchend\",o,!0),document.addEventListener(\"click\",o,!0),document.addEventListener(\"keydown\",o,!0),e}},_obtainHtml5Audio:function(){var e=this||n;if(e._html5AudioPool.length)return e._html5AudioPool.pop();var o=(new Audio).play();return o&&\"undefined\"!=typeof Promise&&(o instanceof Promise||\"function\"==typeof o.then)&&o.catch(function(){console.warn(\"HTML5 Audio pool exhausted, returning potentially locked audio object.\")}),new Audio},_releaseHtml5Audio:function(e){var o=this||n;return e._unlocked&&o._html5AudioPool.push(e),o},_autoSuspend:function(){var e=this;if(e.autoSuspend&&e.ctx&&void 0!==e.ctx.suspend&&n.usingWebAudio){for(var o=0;o<e._howls.length;o++)if(e._howls[o]._webAudio)for(var t=0;t<e._howls[o]._sounds.length;t++)if(!e._howls[o]._sounds[t]._paused)return e;return e._suspendTimer&&clearTimeout(e._suspendTimer),e._suspendTimer=setTimeout(function(){if(e.autoSuspend){e._suspendTimer=null,e.state=\"suspending\";var n=function(){e.state=\"suspended\",e._resumeAfterSuspend&&(delete e._resumeAfterSuspend,e._autoResume())};e.ctx.suspend().then(n,n)}},3e4),e}},_autoResume:function(){var e=this;if(e.ctx&&void 0!==e.ctx.resume&&n.usingWebAudio)return\"running\"===e.state&&\"interrupted\"!==e.ctx.state&&e._suspendTimer?(clearTimeout(e._suspendTimer),e._suspendTimer=null):\"suspended\"===e.state||\"running\"===e.state&&\"interrupted\"===e.ctx.state?(e.ctx.resume().then(function(){e.state=\"running\";for(var n=0;n<e._howls.length;n++)e._howls[n]._emit(\"resume\")}),e._suspendTimer&&(clearTimeout(e._suspendTimer),e._suspendTimer=null)):\"suspending\"===e.state&&(e._resumeAfterSuspend=!0),e}};var n=new e,o=function(e){var n=this;if(!e.src||0===e.src.length)return void console.error(\"An array of source files must be passed with any new Howl.\");n.init(e)};o.prototype={init:function(e){var o=this;return n.ctx||_(),o._autoplay=e.autoplay||!1,o._format=\"string\"!=typeof e.format?e.format:[e.format],o._html5=e.html5||!1,o._muted=e.mute||!1,o._loop=e.loop||!1,o._pool=e.pool||5,o._preload=\"boolean\"!=typeof e.preload&&\"metadata\"!==e.preload||e.preload,o._rate=e.rate||1,o._sprite=e.sprite||{},o._src=\"string\"!=typeof e.src?e.src:[e.src],o._volume=void 0!==e.volume?e.volume:1,o._xhr={method:e.xhr&&e.xhr.method?e.xhr.method:\"GET\",headers:e.xhr&&e.xhr.headers?e.xhr.headers:null,withCredentials:!(!e.xhr||!e.xhr.withCredentials)&&e.xhr.withCredentials},o._duration=0,o._state=\"unloaded\",o._sounds=[],o._endTimers={},o._queue=[],o._playLock=!1,o._onend=e.onend?[{fn:e.onend}]:[],o._onfade=e.onfade?[{fn:e.onfade}]:[],o._onload=e.onload?[{fn:e.onload}]:[],o._onloaderror=e.onloaderror?[{fn:e.onloaderror}]:[],o._onplayerror=e.onplayerror?[{fn:e.onplayerror}]:[],o._onpause=e.onpause?[{fn:e.onpause}]:[],o._onplay=e.onplay?[{fn:e.onplay}]:[],o._onstop=e.onstop?[{fn:e.onstop}]:[],o._onmute=e.onmute?[{fn:e.onmute}]:[],o._onvolume=e.onvolume?[{fn:e.onvolume}]:[],o._onrate=e.onrate?[{fn:e.onrate}]:[],o._onseek=e.onseek?[{fn:e.onseek}]:[],o._onunlock=e.onunlock?[{fn:e.onunlock}]:[],o._onresume=[],o._webAudio=n.usingWebAudio&&!o._html5,void 0!==n.ctx&&n.ctx&&n.autoUnlock&&n._unlockAudio(),n._howls.push(o),o._autoplay&&o._queue.push({event:\"play\",action:function(){o.play()}}),o._preload&&\"none\"!==o._preload&&o.load(),o},load:function(){var e=this,o=null;if(n.noAudio)return void e._emit(\"loaderror\",null,\"No audio support.\");\"string\"==typeof e._src&&(e._src=[e._src]);for(var r=0;r<e._src.length;r++){var u,d;if(e._format&&e._format[r])u=e._format[r];else{if(\"string\"!=typeof(d=e._src[r])){e._emit(\"loaderror\",null,\"Non-string found in selected audio sources - ignoring.\");continue}u=/^data:audio\\/([^;,]+);/i.exec(d),u||(u=/\\.([^.]+)$/.exec(d.split(\"?\",1)[0])),u&&(u=u[1].toLowerCase())}if(u||console.warn('No file extension was found. Consider using the \"format\" property or specify an extension.'),u&&n.codecs(u)){o=e._src[r];break}}return o?(e._src=o,e._state=\"loading\",\"https:\"===window.location.protocol&&\"http:\"===o.slice(0,5)&&(e._html5=!0,e._webAudio=!1),new t(e),e._webAudio&&a(e),e):void e._emit(\"loaderror\",null,\"No codec support for selected audio sources.\")},play:function(e,o){var t=this,r=null;if(\"number\"==typeof e)r=e,e=null;else{if(\"string\"==typeof e&&\"loaded\"===t._state&&!t._sprite[e])return null;if(void 0===e&&(e=\"__default\",!t._playLock)){for(var a=0,u=0;u<t._sounds.length;u++)t._sounds[u]._paused&&!t._sounds[u]._ended&&(a++,r=t._sounds[u]._id);1===a?e=null:r=null}}var d=r?t._soundById(r):t._inactiveSound();if(!d)return null;if(r&&!e&&(e=d._sprite||\"__default\"),\"loaded\"!==t._state){d._sprite=e,d._ended=!1;var i=d._id;return t._queue.push({event:\"play\",action:function(){t.play(i)}}),i}if(r&&!d._paused)return o||t._loadQueue(\"play\"),d._id;t._webAudio&&n._autoResume();var _=Math.max(0,d._seek>0?d._seek:t._sprite[e][0]/1e3),s=Math.max(0,(t._sprite[e][0]+t._sprite[e][1])/1e3-_),l=1e3*s/Math.abs(d._rate),c=t._sprite[e][0]/1e3,f=(t._sprite[e][0]+t._sprite[e][1])/1e3;d._sprite=e,d._ended=!1;var p=function(){d._paused=!1,d._seek=_,d._start=c,d._stop=f,d._loop=!(!d._loop&&!t._sprite[e][2])};if(_>=f)return void t._ended(d);var m=d._node;if(t._webAudio){var v=function(){t._playLock=!1,p(),t._refreshBuffer(d);var e=d._muted||t._muted?0:d._volume;m.gain.setValueAtTime(e,n.ctx.currentTime),d._playStart=n.ctx.currentTime,void 0===m.bufferSource.start?d._loop?m.bufferSource.noteGrainOn(0,_,86400):m.bufferSource.noteGrainOn(0,_,s):d._loop?m.bufferSource.start(0,_,86400):m.bufferSource.start(0,_,s),l!==1/0&&(t._endTimers[d._id]=setTimeout(t._ended.bind(t,d),l)),o||setTimeout(function(){t._emit(\"play\",d._id),t._loadQueue()},0)};\"running\"===n.state&&\"interrupted\"!==n.ctx.state?v():(t._playLock=!0,t.once(\"resume\",v),t._clearTimer(d._id))}else{var h=function(){m.currentTime=_,m.muted=d._muted||t._muted||n._muted||m.muted,m.volume=d._volume*n.volume(),m.playbackRate=d._rate;try{var r=m.play();if(r&&\"undefined\"!=typeof Promise&&(r instanceof Promise||\"function\"==typeof r.then)?(t._playLock=!0,p(),r.then(function(){t._playLock=!1,m._unlocked=!0,o?t._loadQueue():t._emit(\"play\",d._id)}).catch(function(){t._playLock=!1,t._emit(\"playerror\",d._id,\"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.\"),d._ended=!0,d._paused=!0})):o||(t._playLock=!1,p(),t._emit(\"play\",d._id)),m.playbackRate=d._rate,m.paused)return void t._emit(\"playerror\",d._id,\"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.\");\"__default\"!==e||d._loop?t._endTimers[d._id]=setTimeout(t._ended.bind(t,d),l):(t._endTimers[d._id]=function(){t._ended(d),m.removeEventListener(\"ended\",t._endTimers[d._id],!1)},m.addEventListener(\"ended\",t._endTimers[d._id],!1))}catch(e){t._emit(\"playerror\",d._id,e)}};\"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA\"===m.src&&(m.src=t._src,m.load());var y=window&&window.ejecta||!m.readyState&&n._navigator.isCocoonJS;if(m.readyState>=3||y)h();else{t._playLock=!0,t._state=\"loading\";var g=function(){t._state=\"loaded\",h(),m.removeEventListener(n._canPlayEvent,g,!1)};m.addEventListener(n._canPlayEvent,g,!1),t._clearTimer(d._id)}}return d._id},pause:function(e){var n=this;if(\"loaded\"!==n._state||n._playLock)return n._queue.push({event:\"pause\",action:function(){n.pause(e)}}),n;for(var o=n._getSoundIds(e),t=0;t<o.length;t++){n._clearTimer(o[t]);var r=n._soundById(o[t]);if(r&&!r._paused&&(r._seek=n.seek(o[t]),r._rateSeek=0,r._paused=!0,n._stopFade(o[t]),r._node))if(n._webAudio){if(!r._node.bufferSource)continue;void 0===r._node.bufferSource.stop?r._node.bufferSource.noteOff(0):r._node.bufferSource.stop(0),n._cleanBuffer(r._node)}else isNaN(r._node.duration)&&r._node.duration!==1/0||r._node.pause();arguments[1]||n._emit(\"pause\",r?r._id:null)}return n},stop:function(e,n){var o=this;if(\"loaded\"!==o._state||o._playLock)return o._queue.push({event:\"stop\",action:function(){o.stop(e)}}),o;for(var t=o._getSoundIds(e),r=0;r<t.length;r++){o._clearTimer(t[r]);var a=o._soundById(t[r]);a&&(a._seek=a._start||0,a._rateSeek=0,a._paused=!0,a._ended=!0,o._stopFade(t[r]),a._node&&(o._webAudio?a._node.bufferSource&&(void 0===a._node.bufferSource.stop?a._node.bufferSource.noteOff(0):a._node.bufferSource.stop(0),o._cleanBuffer(a._node)):isNaN(a._node.duration)&&a._node.duration!==1/0||(a._node.currentTime=a._start||0,a._node.pause(),a._node.duration===1/0&&o._clearSound(a._node))),n||o._emit(\"stop\",a._id))}return o},mute:function(e,o){var t=this;if(\"loaded\"!==t._state||t._playLock)return t._queue.push({event:\"mute\",action:function(){t.mute(e,o)}}),t;if(void 0===o){if(\"boolean\"!=typeof e)return t._muted;t._muted=e}for(var r=t._getSoundIds(o),a=0;a<r.length;a++){var u=t._soundById(r[a]);u&&(u._muted=e,u._interval&&t._stopFade(u._id),t._webAudio&&u._node?u._node.gain.setValueAtTime(e?0:u._volume,n.ctx.currentTime):u._node&&(u._node.muted=!!n._muted||e),t._emit(\"mute\",u._id))}return t},volume:function(){var e,o,t=this,r=arguments;if(0===r.length)return t._volume;if(1===r.length||2===r.length&&void 0===r[1]){t._getSoundIds().indexOf(r[0])>=0?o=parseInt(r[0],10):e=parseFloat(r[0])}else r.length>=2&&(e=parseFloat(r[0]),o=parseInt(r[1],10));var a;if(!(void 0!==e&&e>=0&&e<=1))return a=o?t._soundById(o):t._sounds[0],a?a._volume:0;if(\"loaded\"!==t._state||t._playLock)return t._queue.push({event:\"volume\",action:function(){t.volume.apply(t,r)}}),t;void 0===o&&(t._volume=e),o=t._getSoundIds(o);for(var u=0;u<o.length;u++)(a=t._soundById(o[u]))&&(a._volume=e,r[2]||t._stopFade(o[u]),t._webAudio&&a._node&&!a._muted?a._node.gain.setValueAtTime(e,n.ctx.currentTime):a._node&&!a._muted&&(a._node.volume=e*n.volume()),t._emit(\"volume\",a._id));return t},fade:function(e,o,t,r){var a=this;if(\"loaded\"!==a._state||a._playLock)return a._queue.push({event:\"fade\",action:function(){a.fade(e,o,t,r)}}),a;e=Math.min(Math.max(0,parseFloat(e)),1),o=Math.min(Math.max(0,parseFloat(o)),1),t=parseFloat(t),a.volume(e,r);for(var u=a._getSoundIds(r),d=0;d<u.length;d++){var i=a._soundById(u[d]);if(i){if(r||a._stopFade(u[d]),a._webAudio&&!i._muted){var _=n.ctx.currentTime,s=_+t/1e3;i._volume=e,i._node.gain.setValueAtTime(e,_),i._node.gain.linearRampToValueAtTime(o,s)}a._startFadeInterval(i,e,o,t,u[d],void 0===r)}}return a},_startFadeInterval:function(e,n,o,t,r,a){var u=this,d=n,i=o-n,_=Math.abs(i/.01),s=Math.max(4,_>0?t/_:t),l=Date.now();e._fadeTo=o,e._interval=setInterval(function(){var r=(Date.now()-l)/t;l=Date.now(),d+=i*r,d=Math.round(100*d)/100,d=i<0?Math.max(o,d):Math.min(o,d),u._webAudio?e._volume=d:u.volume(d,e._id,!0),a&&(u._volume=d),(o<n&&d<=o||o>n&&d>=o)&&(clearInterval(e._interval),e._interval=null,e._fadeTo=null,u.volume(o,e._id),u._emit(\"fade\",e._id))},s)},_stopFade:function(e){var o=this,t=o._soundById(e);return t&&t._interval&&(o._webAudio&&t._node.gain.cancelScheduledValues(n.ctx.currentTime),clearInterval(t._interval),t._interval=null,o.volume(t._fadeTo,e),t._fadeTo=null,o._emit(\"fade\",e)),o},loop:function(){var e,n,o,t=this,r=arguments;if(0===r.length)return t._loop;if(1===r.length){if(\"boolean\"!=typeof r[0])return!!(o=t._soundById(parseInt(r[0],10)))&&o._loop;e=r[0],t._loop=e}else 2===r.length&&(e=r[0],n=parseInt(r[1],10));for(var a=t._getSoundIds(n),u=0;u<a.length;u++)(o=t._soundById(a[u]))&&(o._loop=e,t._webAudio&&o._node&&o._node.bufferSource&&(o._node.bufferSource.loop=e,e&&(o._node.bufferSource.loopStart=o._start||0,o._node.bufferSource.loopEnd=o._stop,t.playing(a[u])&&(t.pause(a[u],!0),t.play(a[u],!0)))));return t},rate:function(){var e,o,t=this,r=arguments;if(0===r.length)o=t._sounds[0]._id;else if(1===r.length){var a=t._getSoundIds(),u=a.indexOf(r[0]);u>=0?o=parseInt(r[0],10):e=parseFloat(r[0])}else 2===r.length&&(e=parseFloat(r[0]),o=parseInt(r[1],10));var d;if(\"number\"!=typeof e)return d=t._soundById(o),d?d._rate:t._rate;if(\"loaded\"!==t._state||t._playLock)return t._queue.push({event:\"rate\",action:function(){t.rate.apply(t,r)}}),t;void 0===o&&(t._rate=e),o=t._getSoundIds(o);for(var i=0;i<o.length;i++)if(d=t._soundById(o[i])){t.playing(o[i])&&(d._rateSeek=t.seek(o[i]),d._playStart=t._webAudio?n.ctx.currentTime:d._playStart),d._rate=e,t._webAudio&&d._node&&d._node.bufferSource?d._node.bufferSource.playbackRate.setValueAtTime(e,n.ctx.currentTime):d._node&&(d._node.playbackRate=e);var _=t.seek(o[i]),s=(t._sprite[d._sprite][0]+t._sprite[d._sprite][1])/1e3-_,l=1e3*s/Math.abs(d._rate);!t._endTimers[o[i]]&&d._paused||(t._clearTimer(o[i]),t._endTimers[o[i]]=setTimeout(t._ended.bind(t,d),l)),t._emit(\"rate\",d._id)}return t},seek:function(){var e,o,t=this,r=arguments;if(0===r.length)t._sounds.length&&(o=t._sounds[0]._id);else if(1===r.length){var a=t._getSoundIds(),u=a.indexOf(r[0]);u>=0?o=parseInt(r[0],10):t._sounds.length&&(o=t._sounds[0]._id,e=parseFloat(r[0]))}else 2===r.length&&(e=parseFloat(r[0]),o=parseInt(r[1],10));if(void 0===o)return 0;if(\"number\"==typeof e&&(\"loaded\"!==t._state||t._playLock))return t._queue.push({event:\"seek\",action:function(){t.seek.apply(t,r)}}),t;var d=t._soundById(o);if(d){if(!(\"number\"==typeof e&&e>=0)){if(t._webAudio){var i=t.playing(o)?n.ctx.currentTime-d._playStart:0,_=d._rateSeek?d._rateSeek-d._seek:0;return d._seek+(_+i*Math.abs(d._rate))}return d._node.currentTime}var s=t.playing(o);s&&t.pause(o,!0),d._seek=e,d._ended=!1,t._clearTimer(o),t._webAudio||!d._node||isNaN(d._node.duration)||(d._node.currentTime=e);var l=function(){s&&t.play(o,!0),t._emit(\"seek\",o)};if(s&&!t._webAudio){var c=function(){t._playLock?setTimeout(c,0):l()};setTimeout(c,0)}else l()}return t},playing:function(e){var n=this;if(\"number\"==typeof e){var o=n._soundById(e);return!!o&&!o._paused}for(var t=0;t<n._sounds.length;t++)if(!n._sounds[t]._paused)return!0;return!1},duration:function(e){var n=this,o=n._duration,t=n._soundById(e);return t&&(o=n._sprite[t._sprite][1]/1e3),o},state:function(){return this._state},unload:function(){for(var e=this,o=e._sounds,t=0;t<o.length;t++)o[t]._paused||e.stop(o[t]._id),e._webAudio||(e._clearSound(o[t]._node),o[t]._node.removeEventListener(\"error\",o[t]._errorFn,!1),o[t]._node.removeEventListener(n._canPlayEvent,o[t]._loadFn,!1),o[t]._node.removeEventListener(\"ended\",o[t]._endFn,!1),n._releaseHtml5Audio(o[t]._node)),delete o[t]._node,e._clearTimer(o[t]._id);var a=n._howls.indexOf(e);a>=0&&n._howls.splice(a,1);var u=!0;for(t=0;t<n._howls.length;t++)if(n._howls[t]._src===e._src||e._src.indexOf(n._howls[t]._src)>=0){u=!1;break}return r&&u&&delete r[e._src],n.noAudio=!1,e._state=\"unloaded\",e._sounds=[],e=null,null},on:function(e,n,o,t){var r=this,a=r[\"_on\"+e];return\"function\"==typeof n&&a.push(t?{id:o,fn:n,once:t}:{id:o,fn:n}),r},off:function(e,n,o){var t=this,r=t[\"_on\"+e],a=0;if(\"number\"==typeof n&&(o=n,n=null),n||o)for(a=0;a<r.length;a++){var u=o===r[a].id;if(n===r[a].fn&&u||!n&&u){r.splice(a,1);break}}else if(e)t[\"_on\"+e]=[];else{var d=Object.keys(t);for(a=0;a<d.length;a++)0===d[a].indexOf(\"_on\")&&Array.isArray(t[d[a]])&&(t[d[a]]=[])}return t},once:function(e,n,o){var t=this;return t.on(e,n,o,1),t},_emit:function(e,n,o){for(var t=this,r=t[\"_on\"+e],a=r.length-1;a>=0;a--)r[a].id&&r[a].id!==n&&\"load\"!==e||(setTimeout(function(e){e.call(this,n,o)}.bind(t,r[a].fn),0),r[a].once&&t.off(e,r[a].fn,r[a].id));return t._loadQueue(e),t},_loadQueue:function(e){var n=this;if(n._queue.length>0){var o=n._queue[0];o.event===e&&(n._queue.shift(),n._loadQueue()),e||o.action()}return n},_ended:function(e){var o=this,t=e._sprite;if(!o._webAudio&&e._node&&!e._node.paused&&!e._node.ended&&e._node.currentTime<e._stop)return setTimeout(o._ended.bind(o,e),100),o;var r=!(!e._loop&&!o._sprite[t][2]);if(o._emit(\"end\",e._id),!o._webAudio&&r&&o.stop(e._id,!0).play(e._id),o._webAudio&&r){o._emit(\"play\",e._id),e._seek=e._start||0,e._rateSeek=0,e._playStart=n.ctx.currentTime;var a=1e3*(e._stop-e._start)/Math.abs(e._rate);o._endTimers[e._id]=setTimeout(o._ended.bind(o,e),a)}return o._webAudio&&!r&&(e._paused=!0,e._ended=!0,e._seek=e._start||0,e._rateSeek=0,o._clearTimer(e._id),o._cleanBuffer(e._node),n._autoSuspend()),o._webAudio||r||o.stop(e._id,!0),o},_clearTimer:function(e){var n=this;if(n._endTimers[e]){if(\"function\"!=typeof n._endTimers[e])clearTimeout(n._endTimers[e]);else{var o=n._soundById(e);o&&o._node&&o._node.removeEventListener(\"ended\",n._endTimers[e],!1)}delete n._endTimers[e]}return n},_soundById:function(e){for(var n=this,o=0;o<n._sounds.length;o++)if(e===n._sounds[o]._id)return n._sounds[o];return null},_inactiveSound:function(){var e=this;e._drain();for(var n=0;n<e._sounds.length;n++)if(e._sounds[n]._ended)return e._sounds[n].reset();return new t(e)},_drain:function(){var e=this,n=e._pool,o=0,t=0;if(!(e._sounds.length<n)){for(t=0;t<e._sounds.length;t++)e._sounds[t]._ended&&o++;for(t=e._sounds.length-1;t>=0;t--){if(o<=n)return;e._sounds[t]._ended&&(e._webAudio&&e._sounds[t]._node&&e._sounds[t]._node.disconnect(0),e._sounds.splice(t,1),o--)}}},_getSoundIds:function(e){var n=this;if(void 0===e){for(var o=[],t=0;t<n._sounds.length;t++)o.push(n._sounds[t]._id);return o}return[e]},_refreshBuffer:function(e){var o=this;return e._node.bufferSource=n.ctx.createBufferSource(),e._node.bufferSource.buffer=r[o._src],e._panner?e._node.bufferSource.connect(e._panner):e._node.bufferSource.connect(e._node),e._node.bufferSource.loop=e._loop,e._loop&&(e._node.bufferSource.loopStart=e._start||0,e._node.bufferSource.loopEnd=e._stop||0),e._node.bufferSource.playbackRate.setValueAtTime(e._rate,n.ctx.currentTime),o},_cleanBuffer:function(e){var o=this,t=n._navigator&&n._navigator.vendor.indexOf(\"Apple\")>=0;if(n._scratchBuffer&&e.bufferSource&&(e.bufferSource.onended=null,e.bufferSource.disconnect(0),t))try{e.bufferSource.buffer=n._scratchBuffer}catch(e){}return e.bufferSource=null,o},_clearSound:function(e){/MSIE |Trident\\//.test(n._navigator&&n._navigator.userAgent)||(e.src=\"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA\")}};var t=function(e){this._parent=e,this.init()};t.prototype={init:function(){var e=this,o=e._parent;return e._muted=o._muted,e._loop=o._loop,e._volume=o._volume,e._rate=o._rate,e._seek=0,e._paused=!0,e._ended=!0,e._sprite=\"__default\",e._id=++n._counter,o._sounds.push(e),e.create(),e},create:function(){var e=this,o=e._parent,t=n._muted||e._muted||e._parent._muted?0:e._volume;return o._webAudio?(e._node=void 0===n.ctx.createGain?n.ctx.createGainNode():n.ctx.createGain(),e._node.gain.setValueAtTime(t,n.ctx.currentTime),e._node.paused=!0,e._node.connect(n.masterGain)):n.noAudio||(e._node=n._obtainHtml5Audio(),e._errorFn=e._errorListener.bind(e),e._node.addEventListener(\"error\",e._errorFn,!1),e._loadFn=e._loadListener.bind(e),e._node.addEventListener(n._canPlayEvent,e._loadFn,!1),e._endFn=e._endListener.bind(e),e._node.addEventListener(\"ended\",e._endFn,!1),e._node.src=o._src,e._node.preload=!0===o._preload?\"auto\":o._preload,e._node.volume=t*n.volume(),e._node.load()),e},reset:function(){var e=this,o=e._parent;return e._muted=o._muted,e._loop=o._loop,e._volume=o._volume,e._rate=o._rate,e._seek=0,e._rateSeek=0,e._paused=!0,e._ended=!0,e._sprite=\"__default\",e._id=++n._counter,e},_errorListener:function(){var e=this;e._parent._emit(\"loaderror\",e._id,e._node.error?e._node.error.code:0),e._node.removeEventListener(\"error\",e._errorFn,!1)},_loadListener:function(){var e=this,o=e._parent;o._duration=Math.ceil(10*e._node.duration)/10,0===Object.keys(o._sprite).length&&(o._sprite={__default:[0,1e3*o._duration]}),\"loaded\"!==o._state&&(o._state=\"loaded\",o._emit(\"load\"),o._loadQueue()),e._node.removeEventListener(n._canPlayEvent,e._loadFn,!1)},_endListener:function(){var e=this,n=e._parent;n._duration===1/0&&(n._duration=Math.ceil(10*e._node.duration)/10,n._sprite.__default[1]===1/0&&(n._sprite.__default[1]=1e3*n._duration),n._ended(e)),e._node.removeEventListener(\"ended\",e._endFn,!1)}};var r={},a=function(e){var n=e._src;if(r[n])return e._duration=r[n].duration,void i(e);if(/^data:[^;]+;base64,/.test(n)){for(var o=atob(n.split(\",\")[1]),t=new Uint8Array(o.length),a=0;a<o.length;++a)t[a]=o.charCodeAt(a);d(t.buffer,e)}else{var _=new XMLHttpRequest;_.open(e._xhr.method,n,!0),_.withCredentials=e._xhr.withCredentials,_.responseType=\"arraybuffer\",e._xhr.headers&&Object.keys(e._xhr.headers).forEach(function(n){_.setRequestHeader(n,e._xhr.headers[n])}),_.onload=function(){var n=(_.status+\"\")[0];if(\"0\"!==n&&\"2\"!==n&&\"3\"!==n)return void e._emit(\"loaderror\",null,\"Failed loading audio file with status: \"+_.status+\".\");d(_.response,e)},_.onerror=function(){e._webAudio&&(e._html5=!0,e._webAudio=!1,e._sounds=[],delete r[n],e.load())},u(_)}},u=function(e){try{e.send()}catch(n){e.onerror()}},d=function(e,o){var t=function(){o._emit(\"loaderror\",null,\"Decoding audio data failed.\")},a=function(e){e&&o._sounds.length>0?(r[o._src]=e,i(o,e)):t()};\"undefined\"!=typeof Promise&&1===n.ctx.decodeAudioData.length?n.ctx.decodeAudioData(e).then(a).catch(t):n.ctx.decodeAudioData(e,a,t)},i=function(e,n){n&&!e._duration&&(e._duration=n.duration),0===Object.keys(e._sprite).length&&(e._sprite={__default:[0,1e3*e._duration]}),\"loaded\"!==e._state&&(e._state=\"loaded\",e._emit(\"load\"),e._loadQueue())},_=function(){if(n.usingWebAudio){try{\"undefined\"!=typeof AudioContext?n.ctx=new AudioContext:\"undefined\"!=typeof webkitAudioContext?n.ctx=new webkitAudioContext:n.usingWebAudio=!1}catch(e){n.usingWebAudio=!1}n.ctx||(n.usingWebAudio=!1);var e=/iP(hone|od|ad)/.test(n._navigator&&n._navigator.platform),o=n._navigator&&n._navigator.appVersion.match(/OS (\\d+)_(\\d+)_?(\\d+)?/),t=o?parseInt(o[1],10):null;if(e&&t&&t<9){var r=/safari/.test(n._navigator&&n._navigator.userAgent.toLowerCase());n._navigator&&!r&&(n.usingWebAudio=!1)}n.usingWebAudio&&(n.masterGain=void 0===n.ctx.createGain?n.ctx.createGainNode():n.ctx.createGain(),n.masterGain.gain.setValueAtTime(n._muted?0:n._volume,n.ctx.currentTime),n.masterGain.connect(n.ctx.destination)),n._setup()}};\"function\"==typeof define&&define.amd&&define([],function(){return{Howler:n,Howl:o}}),\"undefined\"!=typeof exports&&(exports.Howler=n,exports.Howl=o),\"undefined\"!=typeof global?(global.HowlerGlobal=e,global.Howler=n,global.Howl=o,global.Sound=t):\"undefined\"!=typeof window&&(window.HowlerGlobal=e,window.Howler=n,window.Howl=o,window.Sound=t)}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../node_modules/@splinetool/runtime/build/howler.js\n"));

/***/ })

}]);
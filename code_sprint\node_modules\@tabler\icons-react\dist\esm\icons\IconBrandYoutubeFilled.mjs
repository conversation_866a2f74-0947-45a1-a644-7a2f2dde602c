/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandYoutubeFilled = createReactComponent("filled", "brand-youtube-filled", "IconBrandYoutubeFilled", [["path", { "d": "M18 3a5 5 0 0 1 5 5v8a5 5 0 0 1 -5 5h-12a5 5 0 0 1 -5 -5v-8a5 5 0 0 1 5 -5zm-9 6v6a1 1 0 0 0 1.514 .857l5 -3a1 1 0 0 0 0 -1.714l-5 -3a1 1 0 0 0 -1.514 .857z", "key": "svg-0" }]]);

export { IconBrandYoutubeFilled as default };
//# sourceMappingURL=IconBrandYoutubeFilled.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBuildingMonument = createReactComponent("outline", "building-monument", "IconBuildingMonument", [["path", { "d": "M8 18l2 -13l2 -2l2 2l2 13", "key": "svg-0" }], ["path", { "d": "M5 21v-3h14v3", "key": "svg-1" }], ["path", { "d": "M3 21l18 0", "key": "svg-2" }]]);

export { IconBuildingMonument as default };
//# sourceMappingURL=IconBuildingMonument.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandKbin = createReactComponent("outline", "brand-kbin", "IconBrandKbin", [["g", { "key": "svg-0", "strokeWidth": "1.838" }]]);

export { IconBrandKbin as default };
//# sourceMappingURL=IconBrandKbin.mjs.map

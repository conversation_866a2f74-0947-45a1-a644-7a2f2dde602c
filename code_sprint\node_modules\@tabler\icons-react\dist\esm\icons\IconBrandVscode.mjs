/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandVscode = createReactComponent("outline", "brand-vscode", "IconBrandVscode", [["path", { "d": "M16 3v18l4 -2.5v-13z", "key": "svg-0" }], ["path", { "d": "M9.165 13.903l-4.165 3.597l-2 -1l4.333 -4.5m1.735 -1.802l6.932 -7.198v5l-4.795 4.141", "key": "svg-1" }], ["path", { "d": "M16 16.5l-11 -10l-2 1l13 13.5", "key": "svg-2" }]]);

export { IconBrandVscode as default };
//# sourceMappingURL=IconBrandVscode.mjs.map

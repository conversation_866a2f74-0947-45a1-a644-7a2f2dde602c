/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandYandex = createReactComponent("outline", "brand-yandex", "IconBrandYandex", [["path", { "d": "M15 20v-16h-2a4 4 0 0 0 -4 4v1a4 4 0 0 0 4 4h2", "key": "svg-0" }], ["path", { "d": "M9 20l3 -7", "key": "svg-1" }]]);

export { IconBrandYandex as default };
//# sourceMappingURL=IconBrandYandex.mjs.map

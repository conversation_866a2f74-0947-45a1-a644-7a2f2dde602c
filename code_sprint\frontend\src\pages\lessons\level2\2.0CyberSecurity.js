import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "../level-4/section1/animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const Sportsgames = () => {
  const typedText = useTypingAnimation("Cybersecurity", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          <strong>What is Cybersecurity?</strong>
        </p>
        <img
          src="/path-to-cybersecurity-image.png"
          alt="Cybersecurity"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          Cybersecurity is like a superhero for computers, networks, and digital
          devices. Its job is to protect them from bad guys called cyber
          attackers who try to steal important information, mess with systems,
          or even demand money to give back control of your computer.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          <strong>Why Do We Need Cybersecurity?</strong>
        </p>
        <img
          src="/path-to-need-for-cybersecurity-image.png"
          alt="Need for Cybersecurity"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          In today’s world, we all use the internet for lots of things—playing
          games, chatting with friends, doing schoolwork, and more. But with so
          many devices and online activities, we need to make sure our
          information stays safe from hackers (the bad guys who want to break
          into our systems). Cybersecurity helps keep everything secure, just
          like how you lock your doors to keep your home safe.
        </p>
        <h3 className="text-lg text-gray-700 mb-4">
          How Does Cybersecurity Work?
        </h3>
        <img
          src="/path-to-cybersecurity-work-image.png"
          alt="How Cybersecurity Works"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          Cybersecurity uses a mix of <strong>people</strong>,{" "}
          <strong>processes</strong>, and <strong>technology</strong> to build
          strong defenses:
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>People:</strong> Everyone needs to learn good habits to stay
            safe online, like using strong passwords, being careful with emails
            from strangers, and backing up important files.
          </li>
          <li>
            <strong>Processes:</strong> Organizations (like schools, businesses,
            and hospitals) follow rules and plans to protect against and respond
            to cyberattacks. This helps them stay safe even when hackers try to
            cause trouble.
          </li>
          <li>
            <strong>Technology:</strong> Special tools like antivirus software,
            firewalls, and security systems protect our devices, networks, and
            data from being attacked.
          </li>
        </ul>
        <h3 className="text-lg text-gray-700 mb-4">
          Types of Cybersecurity Threats
        </h3>
        <img
          src="/path-to-cybersecurity-threats-image.png"
          alt="Types of Cybersecurity Threats"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          Here are some common threats that cybersecurity protects us from:
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Malware:</strong> This is harmful software that can damage
            your computer or steal your data.
          </li>
          <li>
            <strong>Phishing:</strong> This happens when hackers try to trick
            you into giving them personal information by pretending to be
            someone you trust, like a bank or a friend.
          </li>
          <li>
            <strong>Ransomware:</strong> This type of attack locks up your files
            and demands money to unlock them. But even if you pay, there’s no
            guarantee you’ll get your files back.
          </li>
          <li>
            <strong>Social Engineering:</strong> This is when attackers trick
            you into giving them access to your sensitive information by
            pretending to be someone else or manipulating your emotions.
          </li>
        </ul>
        <h3 className="text-lg text-gray-700 mb-4">
          How Can You Stay Safe Online?
        </h3>
        <img
          src="/path-to-stay-safe-online-image.png"
          alt="Stay Safe Online"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          Here are some simple tips to protect yourself and your devices:
        </p>
        <ul className="text-lg text-gray-700 mb-4">
          <li>
            <strong>Keep Your Software Updated:</strong> Make sure your apps and
            operating system are always up to date. Updates often include fixes
            that protect against new threats.
          </li>
          <li>
            <strong>Use Strong Passwords:</strong> Create strong and unique
            passwords for your accounts. Avoid using the same password for
            everything!
          </li>
          <li>
            <strong>Enable Multi-Factor Authentication (MFA):</strong> This adds
            an extra layer of security by requiring two or more ways to verify
            your identity before you can log in to an account.
          </li>
        </ul>
        <h3 className="text-lg text-gray-700 mb-4">
          Why is Cybersecurity Important for Everyone?
        </h3>
        <img
          src="/path-to-cybersecurity-importance-image.png"
          alt="Importance of Cybersecurity"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          Cybersecurity is important because it keeps our personal information
          safe and prevents hackers from causing harm. Imagine if a hacker got
          into your school’s computer system—classes could be disrupted, and
          private information could be stolen! By staying informed and following
          good cybersecurity practices, we can all help protect our digital
          world.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Cybersecurity might sound complex, but it's all about keeping you and
          your digital life safe from the bad guys online. By learning how to
          protect yourself, you become a superhero in your own right!
        </p>
      </div>
    </div>
  );
};

export default Sportsgames;

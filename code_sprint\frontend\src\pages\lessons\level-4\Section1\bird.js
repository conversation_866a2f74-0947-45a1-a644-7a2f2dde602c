import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import { Player } from "@lottiefiles/react-lottie-player";

const WeakPassword = () => {
  const typedText = useTypingAnimation(" FINISH LINE!", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        // background: "linear-gradient(to bottom, #ff9a8b, #fecfef)",
        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >

          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {/* Confetti Animation */}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        <div
          style={{
            display: "flex",
            justifyContent: "center", // Centers horizontally
            alignItems: "center", // Optional: Centers vertically
            height: "40px", // Optional: Takes full viewport height
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px", // Spacing between text and animation
            }}
          >
            {typedText}
            <Player
              autoplay
              loop
              src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f3c1/lottie.json"
              style={{ height: "40px", width: "40px" }}
            />
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          Through goes the python learner!!! Congratulations on being the first
          ever to cross the finish line with your exceptional Python skills!
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Well done! You&apos;ve made it this far, and now it&apos;s time to put your
          newfound skills to the test with a hands-on project.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Grab your favorite code editor and dive into this fun challenge.
          Whether you&apos;re using Python Code Editor, Visual Studio Code, or
          something else, it&apos;s time to get coding!
        </p>
        <hr className="my-6 border-t border-gray-300" />
        <h2 className="text-2xl font-bold text-gray-800 mt-6">
          DON&apos;T EAT MY BRAINS, THANKS
        </h2>
        <p className="text-lg text-gray-700 mb-4">
          Create a{" "}
          <code className="font-mono text-blue-500">zombie_survival.py</code>{" "}
          game where players must survive a zombie apocalypse. The game will let
          them make decisions on scavenging for supplies, building defenses, and
          fending off zombies.
        </p>
        <h2 className="text-lg font-bold text-[#3B4555] mt-6">
          Challenge Outline:
        </h2>
        <div className="bg-gray-100 p-4 rounded-lg text-left mt-4">
          <pre className="text-sm text-[#3B4555]">{`- Begin with a scenario of a zombie outbreak.
- Use choices to manage supplies, build defenses, and survive encounters.
- Implement a health system and random zombie attacks.
- Have lots of fun with it!
- Get creative and go wild!
- And did I mention? Have fun!

`}</pre>
        </div>
      </div>
    </div>
  );
};

export default WeakPassword;

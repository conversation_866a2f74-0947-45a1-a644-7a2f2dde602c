import React from 'react';

export default function ExploringDataTypes() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-blue-400 via-purple-500 to-pink-600 text-white p-10">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .card {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    margin: 20px 0;
                    max-width: 600px;
                    text-align: left;
                }

                .image-placeholder {
                    width: 200px;
                    height: 150px;
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px dashed #fff;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 1rem;
                    margin: 16px;
                    flex-shrink: 0; /* Ensures image placeholder doesn't shrink */
                }

                .content-row {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 20px; /* Adds space between cards and placeholders */
                    flex-wrap: wrap; /* Ensures responsiveness on smaller screens */
                }
                `}
            </style>

            <h1 className="text-5xl font-bold mb-8">Exploring Data Types</h1>
            <p className="text-lg text-center max-w-3xl mb-12">
                Data comes in many types, like <span className="highlight">numbers</span>, <span className="highlight">text</span>, and <span className="highlight">dates</span>. Knowing how to handle each type is key to unlocking the power of Data Science.
            </p>

            {/* Example 1: Numbers */}
            <div className="content-row">
                <img
                    src="/data_numbers.jpg" /* Adjusted to your actual image path */
                    alt="Numbers Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">🔢 Numbers</h2>
                    <p className="text-lg">
                        Numbers are the building blocks of data. For example:
                        <br />
                        - A store tracks daily sales in dollars.<br />
                        - Scientists record temperatures in degrees Celsius.
                    </p>
                </div>
            </div>

            {/* Example 2: Text */}
            <div className="content-row">
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📜 Text</h2>
                    <p className="text-lg">
                        Text data holds information like names, emails, and descriptions. For instance:
                        <br />
                        - A library catalog stores book titles and authors.<br />
                        - An app collects feedback from user reviews.
                    </p>
                </div>
                <img
                    src="/data_text.jpg" /* Adjusted to your actual image path */
                    alt="Text Example"
                    className="image-placeholder"
                />
            </div>

            {/* Example 3: Dates */}
            <div className="content-row">
                <img
                    src="/data_dates.jpg" /* Adjusted to your actual image path */
                    alt="Dates Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📅 Dates</h2>
                    <p className="text-lg">
                        Dates help track events over time. For example:
                        <br />
                        - A hospital records patient admission dates.<br />
                        - A fitness app logs exercise history by day.
                    </p>
                </div>
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-xl font-semibold mt-16 max-w-2xl">
                By understanding data types, you can organize and analyze data to reveal amazing insights!
            </p>
        </div>
    );
}

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandWikipedia = createReactComponent("outline", "brand-wikipedia", "IconBrandWikipedia", [["path", { "d": "M3 4.984h2", "key": "svg-0" }], ["path", { "d": "M8 4.984h2.5", "key": "svg-1" }], ["path", { "d": "M14.5 4.984h2.5", "key": "svg-2" }], ["path", { "d": "M22 4.984h-2", "key": "svg-3" }], ["path", { "d": "M4 4.984l5.455 14.516l6.545 -14.516", "key": "svg-4" }], ["path", { "d": "M9 4.984l6 14.516l6 -14.516", "key": "svg-5" }]]);

export { IconBrandWikipedia as default };
//# sourceMappingURL=IconBrandWikipedia.mjs.map

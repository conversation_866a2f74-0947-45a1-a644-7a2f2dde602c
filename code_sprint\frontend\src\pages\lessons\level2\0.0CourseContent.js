'use client';
import React, { useState } from 'react';

// Import lesson components
import MetaverseAndAugmentedReality from "./01.0MetaverseAndAugmentedReality";
import CoolTechnologyBehindMetaverse from "./01.1CoolTechnologyBehindMetaverse";
import AugmentedReality from "./02.0AugmentedReality";
import MarkerBasedAR from "./02.1MarkerBasedAR";
import MarkerlessAR from "./02.2MarkerlessAR";
import UsesOfAugmentedReality from "./02.3UsesOfAugmentedReality";
import VirtualReality from "./03.0VirtualReality";
import ApplicationsOfVirtualReality from "./03.2ApplicationsOfVirtualReality";
import UnderstandingDNA from "./04.1UnderstandingDNA";
import ApplicationsInMedicine from "./04.3ApplicationsInMedicine";
import FunFactsGenomicAI from "./04.4FunFactsGenomicAI";
import CyberSecurity from "./2.0CyberSecurity";
import WhatIsCybersecurity from "./2.1WhatIsCybersecurity";
import ThreatsAndAttacks from "./2.2ThreatsAndAttacks";
import ProtectingYourselfOnline from "./2.3ProtectingYourselfOnline";
import FutureOfCybersecurity from "./2.4FutureOfCybersecurity";
import QuantumComputing from "./3.0QuantumComputing";
import WhatIsQuantumComputing from "./3.1WhatIsQuantumComputing";
import QuantumBitsAndEntanglement from "./3.2QuantumBitsAndEntanglement";
import { WhatIsAugmentedReality } from './02.01WhatIsAugmentedReality';

// Modal Component
const Modal = ({ isOpen, onClose, children }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50">
            {/* Background overlay */}
            <div className="fixed inset-0 bg-black opacity-50" onClick={onClose}></div>
            {/* Modal content */}
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 relative z-10 shadow-lg">
                <button onClick={onClose} className="absolute top-2 right-2 text-gray-600 hover:text-gray-800">
                    ✖️
                </button>
                {children}
            </div>
        </div>
    );
};

// IndexOfCourse Component
const IndexOfCourse = () => {
    return (
        <div className="p-4">
            <div className="mt-4">
                <h3 className="text-lg font-semibold">CONTENTS</h3>
                <ul className="list-disc list-inside">
                    <li>
                        <strong>01. METAVERSE AND AUGMENTED REALITY</strong>
                        <ul className="ml-4">
                            <li>What is the Metaverse?</li>
                            <li>Cool Technology Behind the Metaverse</li>
                        </ul>
                    </li>
                    <li>
                        <strong>02. AUGMENTED REALITY</strong>
                        <ul className="ml-4">
                            <li>Augmented Reality</li>
                            <li>WhatIsAugmentedReality</li>
                            <li>Marker-Based AR</li>
                            <li>Markerless AR</li>
                            <li>Uses of Augmented Reality</li>
                        </ul>
                    </li>
                    <li>
                        <strong>03. VIRTUAL REALITY</strong>
                        <ul className="ml-4">
                            <li>Introduction to Virtual Reality</li>
                            <li>Applications of Virtual Reality</li>
                        </ul>
                    </li>
                    <li>
                        <strong>04. GENOMIC AI</strong>
                        <ul className="ml-4">
                            <li>Understanding DNA</li>
                            <li>AI in Genomics</li>
                            <li>Applications in Medicine</li>
                            <li>Fun Facts about Genomic AI</li>
                        </ul>
                    </li>
                    <li>
                        <strong>05. CYBERSECURITY</strong>
                        <ul className="ml-4">
                            <li>What is Cybersecurity?</li>
                            <li>Threats and Attacks</li>
                            <li>Protecting Yourself Online</li>
                            <li>Future of Cybersecurity</li>
                        </ul>
                    </li>
                    <li>
                        <strong>06. QUANTUM COMPUTING</strong>
                        <ul className="ml-4">
                            <li>What is Quantum Computing?</li>
                            <li>Introduction to Quantum Computing</li>
                            <li>Quantum Bits and Entanglement</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    );
};

// Main Component
export default function CourseContentModal() {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const handleOpenModal = () => setIsModalOpen(true);
    const handleCloseModal = () => setIsModalOpen(false);

    return (
        <div className="max-w-3xl mx-auto p-6">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">Level 2 Course Section</h1>
            
            <button
                onClick={handleOpenModal}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
            >
                Index of Course
            </button>

            {/* Modal to show IndexOfCourse component */}
            <Modal isOpen={isModalOpen} onClose={handleCloseModal}>
                <IndexOfCourse />
            </Modal>
        </div>
    );
}

// Export level2Data as a named export
export const level2Data = {
    majorTopics: [
        {
            title: "METAVERSE AND AUGMENTED REALITY",
            minorTopics: [
                { title: "What is the Metaverse?", component: <MetaverseAndAugmentedReality /> },
                { title: "Cool Technology Behind the Metaverse", component: <CoolTechnologyBehindMetaverse /> },
            ],
        },
        {
            title: "AUGMENTED REALITY",
            minorTopics: [
                { title: "What is Augmented Reality?", component: <WhatIsAugmentedReality /> },
                { title: "Augmented Reality", component: <AugmentedReality /> },
                { title: "Marker-Based AR", component: <MarkerBasedAR /> },
                { title: "Markerless AR", component: <MarkerlessAR /> },
                { title: "Uses of Augmented Reality", component: <UsesOfAugmentedReality /> },
            ],
        },
        {
            title: "VIRTUAL REALITY",
            minorTopics: [
                { title: "Introduction to Virtual Reality", component: <VirtualReality /> },
                { title: "Applications of Virtual Reality", component: <ApplicationsOfVirtualReality /> },
            ],
        },
        {
            title: "GENOMIC AI",
            minorTopics: [
                { title: "Understanding DNA", component: <UnderstandingDNA /> },
            ],
        },
        {
            title: "CYBERSECURITY",
            minorTopics: [
                { title: "Cybersecurity", component: <CyberSecurity /> },
            ],
        },
        {
            title: "QUANTUM COMPUTING",
            minorTopics: [
                { title: "Quantum Computing", component: <QuantumComputing /> },
            ],
        }
    ],
};

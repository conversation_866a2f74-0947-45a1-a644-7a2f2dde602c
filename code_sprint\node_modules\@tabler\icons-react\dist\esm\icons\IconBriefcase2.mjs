/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBriefcase2 = createReactComponent("outline", "briefcase-2", "IconBriefcase2", [["path", { "d": "M3 9a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v9a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-9z", "key": "svg-0" }], ["path", { "d": "M8 7v-2a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v2", "key": "svg-1" }]]);

export { IconBriefcase2 as default };
//# sourceMappingURL=IconBriefcase2.mjs.map

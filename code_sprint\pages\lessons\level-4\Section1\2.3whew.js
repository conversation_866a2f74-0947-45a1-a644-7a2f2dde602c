import React, { useState, useRef } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const DataTypesAndOperators = () => {
  const [code, setCode] = useState(`# Python Data Types and Operators
# Declare a string
name = "Alice"

# Declare an integer
age = 25

# Perform addition
result = age + 5

print("Name:", name)
print("Age in 5 years:", result)
`);

const animatedCode = useTypingAnimation(code);

const typedText = useTypingAnimation("Fun with Python!", 100);

  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);

  const outputRef = useRef(null);

  const runCode = () => {
    try {
      const name = "Alice";
      const age = 25;
      const result = age + 5;
      setOutput(`Name: ${name}\nAge in 5 years: ${result}`);
      setShowConfetti(true);
      setShowOutput(true);
      setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);

      setTimeout(() => {
        document.querySelector(`.${styles.confetti}`)?.classList.add(styles.hidden);
        setTimeout(() => setShowConfetti(false), 500);
      }, 1000);

    } catch (error) {
      console.error(error);
      setOutput("Error: There was a problem running your code.");
      setShowOutput(true);
    }
  };
    
  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom right, #FFFBF0, #E0F7FA)",
        padding: "20px",
      }}
    >
      <div
        style={{
          maxWidth: "800px",
          textAlign: "center",
          backgroundColor: "#fff",
          padding: "20px",
          borderRadius: "15px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.1)",
          position: "relative",
        }}
      >
        {showConfetti && (
          <div className={styles.confetti}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            <div
              style={{
                position: "absolute",
                width: "10px",
                height: "10px",
                backgroundColor: "#FFD700",
                animation: "fall 1.5s infinite linear",
              }}
            />
            <div
              style={{
                position: "absolute",
                width: "10px",
                height: "10px",
                backgroundColor: "#FF4081",
                animation: "fall 1.5s infinite linear",
              }}
            />
            <div
              style={{
                position: "absolute",
                width: "10px",
                height: "10px",
                backgroundColor: "#7C4DFF",
                animation: "fall 1.5s infinite linear",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #FF4081, #448AFF)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
            animation: "bounce 1.5s infinite",
          }}
        >
          {typedText}
        </h1>{" "}
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            textAlign: "center",
            fontSize: "1.2rem",
            color: "#555",
          }}
        >
          <p>
          Learn <strong>data types</strong> and <strong>operators</strong> with
          a smile!{" "}
          </p>
          <Player
            src="https://fonts.gstatic.com/s/e/notoemoji/latest/263a_fe0f/lottie.json"
            style={{ width: "24px", height: "24px", marginLeft: "8px" }}
            autoplay
            loop
          />
        </div>{" "}

        <p style={{ fontSize: "1rem", color: "#666", marginTop: "10px" }}>
          Data types define what kind of data you are working with, like words,
          numbers, or magic spells 🪄.
        </p>

        <hr className="my-6 border-t border-gray-300" />
        <p className="text-lg font-bold text-black mt-6">INSTRUCTIONS</p>
        <p style={{ fontSize: "0.9rem", color: "#666", marginTop: "10px" }}>
          Create a{" "}
          <code style={{ fontFamily: "monospace", color: "#448AFF" }}>
            data_types.py
          </code>{" "}
          program and try the code below:
        </p>

        <pre
          style={{
            backgroundColor: "#F0F8FF",
            padding: "15px",
            borderRadius: "10px",
            textAlign: "left",
            color: "#444",
            marginTop: "10px",
            border: "1px solid #B3E5FC",
          }}
        >
          # Declare a string{"\n"}name = &quot;Alice&quot;{"\n\n"}# Declare an integer
          {"\n"}age = 25{"\n\n"}# Perform addition{"\n"}result = age + 5{"\n\n"}
          print(&quot;Name:&quot;, name){"\n"}print(&quot;Age in 5 years:&quot;, result)
        </pre>

        {}
        <div
          style={{
            backgroundColor: "#1a2327",
            padding: "15px",
            borderRadius: "10px",
            marginTop: "20px",
            textAlign: "left",
            color: "#fff",
          }}
        >
          <Editor
            value={animatedCode}
            onValueChange={(newCode) => setCode(newCode)}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative", zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              borderRadius: "10px",
              backgroundColor: "#263238",
              color: "#fff",
              minHeight: "300px",
            }}
          />
        </div>

        <div>
          {}
          {!showOutput && (
            <button
              onClick={runCode}
              className={styles.fadeInUp}
              style={{
                position: "relative", zIndex: 10,
                marginTop: "20px",
                backgroundColor: "#cb997e",
                color: "#797d62",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
              }}
            >
              Run Code 🎉
            </button>
          )}

          {}
          <div className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "82%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#cb997e",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #FFE082",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#797d62",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#797d62", marginTop: "10px" }}
              >
                {output}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DataTypesAndOperators;

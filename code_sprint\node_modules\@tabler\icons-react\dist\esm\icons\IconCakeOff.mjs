/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCakeOff = createReactComponent("outline", "cake-off", "IconCakeOff", [["path", { "d": "M21 17v-5a3 3 0 0 0 -3 -3h-5m-4 0h-3a3 3 0 0 0 -3 3v8h17", "key": "svg-0" }], ["path", { "d": "M3 14.803c.312 .135 .654 .204 1 .197a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1m4 0a2.4 2.4 0 0 0 2 1c.35 .007 .692 -.062 1 -.197", "key": "svg-1" }], ["path", { "d": "M10.172 6.188c.07 -.158 .163 -.31 .278 -.451l1.55 -1.737l1.465 1.638a2 2 0 0 1 -.65 3.19", "key": "svg-2" }], ["path", { "d": "M3 3l18 18", "key": "svg-3" }]]);

export { IconCakeOff as default };
//# sourceMappingURL=IconCakeOff.mjs.map

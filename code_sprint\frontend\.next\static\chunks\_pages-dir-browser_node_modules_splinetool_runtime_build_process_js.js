"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_process_js"],{

/***/ "(pages-dir-browser)/../node_modules/@splinetool/runtime/build/process.js":
/*!************************************************************!*\
  !*** ../node_modules/@splinetool/runtime/build/process.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\nvar Module = (() => {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(moduleArg = {}) {\n\nvar Module=moduleArg;var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram=\"./this.program\";var quit_=(status,toThrow)=>{throw toThrow};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=\"undefined\"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=url=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];var noExitRuntime=Module[\"noExitRuntime\"]||true;if(typeof WebAssembly!=\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(text)}}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module[\"HEAP8\"]=HEAP8=new Int8Array(b);Module[\"HEAP16\"]=HEAP16=new Int16Array(b);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(b);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(b);Module[\"HEAP32\"]=HEAP32=new Int32Array(b);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(b);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(b);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(b)}var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;if(!Module[\"noFSInit\"]&&!FS.init.initialized)FS.init();FS.ignorePermissions=false;TTY.init();callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function getUniqueRunDependency(id){return id}function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}what=\"Aborted(\"+what+\")\";err(what);ABORT=true;EXITSTATUS=1;what+=\". Build with -sASSERTIONS for more info.\";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix=\"data:application/octet-stream;base64,\";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile=\"process.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw\"both async and sync fetching of the wasm failed\"}function getBinaryPromise(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==\"function\"){return fetch(binaryFile,{credentials:\"same-origin\"}).then(response=>{if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+binaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(()=>getBinarySync(binaryFile))}}return Promise.resolve().then(()=>getBinarySync(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>WebAssembly.instantiate(binary,imports)).then(instance=>instance).then(receiver,reason=>{err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming==\"function\"&&!isDataURI(binaryFile)&&typeof fetch==\"function\"){return fetch(binaryFile,{credentials:\"same-origin\"}).then(response=>{var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err(`wasm streaming compile failed: ${reason}`);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(binaryFile,imports,callback)})})}return instantiateArrayBuffer(binaryFile,imports,callback)}function createWasm(){var info={\"a\":wasmImports};function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports[\"K\"];updateMemoryViews();wasmTable=wasmExports[\"O\"];addOnInit(wasmExports[\"L\"]);removeRunDependency(\"wasm-instantiate\");return wasmExports}addRunDependency(\"wasm-instantiate\");function receiveInstantiationResult(result){receiveInstance(result[\"instance\"])}if(Module[\"instantiateWasm\"]){try{return Module[\"instantiateWasm\"](info,receiveInstance)}catch(e){err(`Module.instantiateWasm callback failed with error: ${e}`);readyPromiseReject(e)}}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult).catch(readyPromiseReject);return{}}var tempDouble;var tempI64;var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24;this.set_type=function(type){HEAPU32[this.ptr+4>>2]=type};this.get_type=function(){return HEAPU32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAPU32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAPU32[this.ptr+8>>2]};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)};this.set_adjusted_ptr=function(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr};this.get_adjusted_ptr=function(){return HEAPU32[this.ptr+16>>2]};this.get_exception_ptr=function(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer){return HEAPU32[this.excPtr>>2]}var adjusted=this.get_adjusted_ptr();if(adjusted!==0)return adjusted;return this.excPtr}}var exceptionLast=0;var uncaughtExceptionCount=0;var ___cxa_throw=(ptr,type,destructor)=>{var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast};var tupleRegistrations={};var runDestructors=destructors=>{while(destructors.length){var ptr=destructors.pop();var del=destructors.pop();del(ptr)}};function simpleReadValueFromPointer(pointer){return this[\"fromWireType\"](HEAP32[pointer>>2])}var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var InternalError;var throwInternalError=message=>{throw new InternalError(message)};var whenDependentTypesAreResolved=(myTypes,dependentTypes,getTypeConverters)=>{myTypes.forEach(function(type){typeDependencies[type]=dependentTypes});function onComplete(typeConverters){var myTypeConverters=getTypeConverters(typeConverters);if(myTypeConverters.length!==myTypes.length){throwInternalError(\"Mismatched type converter count\")}for(var i=0;i<myTypes.length;++i){registerType(myTypes[i],myTypeConverters[i])}}var typeConverters=new Array(dependentTypes.length);var unregisteredTypes=[];var registered=0;dependentTypes.forEach((dt,i)=>{if(registeredTypes.hasOwnProperty(dt)){typeConverters[i]=registeredTypes[dt]}else{unregisteredTypes.push(dt);if(!awaitingDependencies.hasOwnProperty(dt)){awaitingDependencies[dt]=[]}awaitingDependencies[dt].push(()=>{typeConverters[i]=registeredTypes[dt];++registered;if(registered===unregisteredTypes.length){onComplete(typeConverters)}})}});if(0===unregisteredTypes.length){onComplete(typeConverters)}};var __embind_finalize_value_array=rawTupleType=>{var reg=tupleRegistrations[rawTupleType];delete tupleRegistrations[rawTupleType];var elements=reg.elements;var elementsLength=elements.length;var elementTypes=elements.map(elt=>elt.getterReturnType).concat(elements.map(elt=>elt.setterArgumentType));var rawConstructor=reg.rawConstructor;var rawDestructor=reg.rawDestructor;whenDependentTypesAreResolved([rawTupleType],elementTypes,function(elementTypes){elements.forEach((elt,i)=>{var getterReturnType=elementTypes[i];var getter=elt.getter;var getterContext=elt.getterContext;var setterArgumentType=elementTypes[i+elementsLength];var setter=elt.setter;var setterContext=elt.setterContext;elt.read=ptr=>getterReturnType[\"fromWireType\"](getter(getterContext,ptr));elt.write=(ptr,o)=>{var destructors=[];setter(setterContext,ptr,setterArgumentType[\"toWireType\"](destructors,o));runDestructors(destructors)}});return[{name:reg.name,\"fromWireType\":ptr=>{var rv=new Array(elementsLength);for(var i=0;i<elementsLength;++i){rv[i]=elements[i].read(ptr)}rawDestructor(ptr);return rv},\"toWireType\":(destructors,o)=>{if(elementsLength!==o.length){throw new TypeError(`Incorrect number of tuple elements for ${reg.name}: expected=${elementsLength}, actual=${o.length}`)}var ptr=rawConstructor();for(var i=0;i<elementsLength;++i){elements[i].write(ptr,o[i])}if(destructors!==null){destructors.push(rawDestructor,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:rawDestructor}]})};var structRegistrations={};var __embind_finalize_value_object=structType=>{var reg=structRegistrations[structType];delete structRegistrations[structType];var rawConstructor=reg.rawConstructor;var rawDestructor=reg.rawDestructor;var fieldRecords=reg.fields;var fieldTypes=fieldRecords.map(field=>field.getterReturnType).concat(fieldRecords.map(field=>field.setterArgumentType));whenDependentTypesAreResolved([structType],fieldTypes,fieldTypes=>{var fields={};fieldRecords.forEach((field,i)=>{var fieldName=field.fieldName;var getterReturnType=fieldTypes[i];var getter=field.getter;var getterContext=field.getterContext;var setterArgumentType=fieldTypes[i+fieldRecords.length];var setter=field.setter;var setterContext=field.setterContext;fields[fieldName]={read:ptr=>getterReturnType[\"fromWireType\"](getter(getterContext,ptr)),write:(ptr,o)=>{var destructors=[];setter(setterContext,ptr,setterArgumentType[\"toWireType\"](destructors,o));runDestructors(destructors)}}});return[{name:reg.name,\"fromWireType\":ptr=>{var rv={};for(var i in fields){rv[i]=fields[i].read(ptr)}rawDestructor(ptr);return rv},\"toWireType\":(destructors,o)=>{for(var fieldName in fields){if(!(fieldName in o)){throw new TypeError(`Missing field: \"${fieldName}\"`)}}var ptr=rawConstructor();for(fieldName in fields){fields[fieldName].write(ptr,o[fieldName])}if(destructors!==null){destructors.push(rawDestructor,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:rawDestructor}]})};var __embind_register_bigint=(primitiveType,name,size,minRange,maxRange)=>{};var embind_init_charCodes=()=>{var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes};var embind_charCodes;var readLatin1String=ptr=>{var ret=\"\";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]]}return ret};var BindingError;var throwBindingError=message=>{throw new BindingError(message)};function sharedRegisterType(rawType,registeredInstance,options={}){var name=registeredInstance.name;if(!rawType){throwBindingError(`type \"${name}\" must have a positive integer typeid pointer`)}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError(`Cannot register type '${name}' twice`)}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(cb=>cb())}}function registerType(rawType,registeredInstance,options={}){if(!(\"argPackAdvance\"in registeredInstance)){throw new TypeError(\"registerType registeredInstance requires argPackAdvance\")}return sharedRegisterType(rawType,registeredInstance,options)}var GenericWireTypeSize=8;var __embind_register_bool=(rawType,name,trueValue,falseValue)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":function(wt){return!!wt},\"toWireType\":function(destructors,o){return o?trueValue:falseValue},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":function(pointer){return this[\"fromWireType\"](HEAPU8[pointer])},destructorFunction:null})};var shallowCopyInternalPointer=o=>({count:o.count,deleteScheduled:o.deleteScheduled,preservePointerOnDelete:o.preservePointerOnDelete,ptr:o.ptr,ptrType:o.ptrType,smartPtr:o.smartPtr,smartPtrType:o.smartPtrType});var throwInstanceAlreadyDeleted=obj=>{function getInstanceTypeName(handle){return handle.$$.ptrType.registeredClass.name}throwBindingError(getInstanceTypeName(obj)+\" instance already deleted\")};var finalizationRegistry=false;var detachFinalizer=handle=>{};var runDestructor=$$=>{if($$.smartPtr){$$.smartPtrType.rawDestructor($$.smartPtr)}else{$$.ptrType.registeredClass.rawDestructor($$.ptr)}};var releaseClassHandle=$$=>{$$.count.value-=1;var toDelete=0===$$.count.value;if(toDelete){runDestructor($$)}};var downcastPointer=(ptr,ptrClass,desiredClass)=>{if(ptrClass===desiredClass){return ptr}if(undefined===desiredClass.baseClass){return null}var rv=downcastPointer(ptr,ptrClass,desiredClass.baseClass);if(rv===null){return null}return desiredClass.downcast(rv)};var registeredPointers={};var getInheritedInstanceCount=()=>Object.keys(registeredInstances).length;var getLiveInheritedInstances=()=>{var rv=[];for(var k in registeredInstances){if(registeredInstances.hasOwnProperty(k)){rv.push(registeredInstances[k])}}return rv};var deletionQueue=[];var flushPendingDeletes=()=>{while(deletionQueue.length){var obj=deletionQueue.pop();obj.$$.deleteScheduled=false;obj[\"delete\"]()}};var delayFunction;var setDelayFunction=fn=>{delayFunction=fn;if(deletionQueue.length&&delayFunction){delayFunction(flushPendingDeletes)}};var init_embind=()=>{Module[\"getInheritedInstanceCount\"]=getInheritedInstanceCount;Module[\"getLiveInheritedInstances\"]=getLiveInheritedInstances;Module[\"flushPendingDeletes\"]=flushPendingDeletes;Module[\"setDelayFunction\"]=setDelayFunction};var registeredInstances={};var getBasestPointer=(class_,ptr)=>{if(ptr===undefined){throwBindingError(\"ptr should not be undefined\")}while(class_.baseClass){ptr=class_.upcast(ptr);class_=class_.baseClass}return ptr};var getInheritedInstance=(class_,ptr)=>{ptr=getBasestPointer(class_,ptr);return registeredInstances[ptr]};var makeClassHandle=(prototype,record)=>{if(!record.ptrType||!record.ptr){throwInternalError(\"makeClassHandle requires ptr and ptrType\")}var hasSmartPtrType=!!record.smartPtrType;var hasSmartPtr=!!record.smartPtr;if(hasSmartPtrType!==hasSmartPtr){throwInternalError(\"Both smartPtrType and smartPtr must be specified\")}record.count={value:1};return attachFinalizer(Object.create(prototype,{$$:{value:record}}))};function RegisteredPointer_fromWireType(ptr){var rawPointer=this.getPointee(ptr);if(!rawPointer){this.destructor(ptr);return null}var registeredInstance=getInheritedInstance(this.registeredClass,rawPointer);if(undefined!==registeredInstance){if(0===registeredInstance.$$.count.value){registeredInstance.$$.ptr=rawPointer;registeredInstance.$$.smartPtr=ptr;return registeredInstance[\"clone\"]()}else{var rv=registeredInstance[\"clone\"]();this.destructor(ptr);return rv}}function makeDefaultHandle(){if(this.isSmartPointer){return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:rawPointer,smartPtrType:this,smartPtr:ptr})}else{return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this,ptr:ptr})}}var actualType=this.registeredClass.getActualType(rawPointer);var registeredPointerRecord=registeredPointers[actualType];if(!registeredPointerRecord){return makeDefaultHandle.call(this)}var toType;if(this.isConst){toType=registeredPointerRecord.constPointerType}else{toType=registeredPointerRecord.pointerType}var dp=downcastPointer(rawPointer,this.registeredClass,toType.registeredClass);if(dp===null){return makeDefaultHandle.call(this)}if(this.isSmartPointer){return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp,smartPtrType:this,smartPtr:ptr})}else{return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp})}}var attachFinalizer=handle=>{if(\"undefined\"===typeof FinalizationRegistry){attachFinalizer=handle=>handle;return handle}finalizationRegistry=new FinalizationRegistry(info=>{releaseClassHandle(info.$$)});attachFinalizer=handle=>{var $$=handle.$$;var hasSmartPtr=!!$$.smartPtr;if(hasSmartPtr){var info={$$:$$};finalizationRegistry.register(handle,info,handle)}return handle};detachFinalizer=handle=>finalizationRegistry.unregister(handle);return attachFinalizer(handle)};var init_ClassHandle=()=>{Object.assign(ClassHandle.prototype,{\"isAliasOf\"(other){if(!(this instanceof ClassHandle)){return false}if(!(other instanceof ClassHandle)){return false}var leftClass=this.$$.ptrType.registeredClass;var left=this.$$.ptr;other.$$=other.$$;var rightClass=other.$$.ptrType.registeredClass;var right=other.$$.ptr;while(leftClass.baseClass){left=leftClass.upcast(left);leftClass=leftClass.baseClass}while(rightClass.baseClass){right=rightClass.upcast(right);rightClass=rightClass.baseClass}return leftClass===rightClass&&left===right},\"clone\"(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.preservePointerOnDelete){this.$$.count.value+=1;return this}else{var clone=attachFinalizer(Object.create(Object.getPrototypeOf(this),{$$:{value:shallowCopyInternalPointer(this.$$)}}));clone.$$.count.value+=1;clone.$$.deleteScheduled=false;return clone}},\"delete\"(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError(\"Object already scheduled for deletion\")}detachFinalizer(this);releaseClassHandle(this.$$);if(!this.$$.preservePointerOnDelete){this.$$.smartPtr=undefined;this.$$.ptr=undefined}},\"isDeleted\"(){return!this.$$.ptr},\"deleteLater\"(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError(\"Object already scheduled for deletion\")}deletionQueue.push(this);if(deletionQueue.length===1&&delayFunction){delayFunction(flushPendingDeletes)}this.$$.deleteScheduled=true;return this}})};function ClassHandle(){}var char_0=48;var char_9=57;var makeLegalFunctionName=name=>{if(undefined===name){return\"_unknown\"}name=name.replace(/[^a-zA-Z0-9_]/g,\"$\");var f=name.charCodeAt(0);if(f>=char_0&&f<=char_9){return`_${name}`}return name};function createNamedFunction(name,body){name=makeLegalFunctionName(name);return{[name]:function(){return body.apply(this,arguments)}}[name]}var ensureOverloadTable=(proto,methodName,humanName)=>{if(undefined===proto[methodName].overloadTable){var prevFunc=proto[methodName];proto[methodName]=function(){if(!proto[methodName].overloadTable.hasOwnProperty(arguments.length)){throwBindingError(`Function '${humanName}' called with an invalid number of arguments (${arguments.length}) - expects one of (${proto[methodName].overloadTable})!`)}return proto[methodName].overloadTable[arguments.length].apply(this,arguments)};proto[methodName].overloadTable=[];proto[methodName].overloadTable[prevFunc.argCount]=prevFunc}};var exposePublicSymbol=(name,value,numArguments)=>{if(Module.hasOwnProperty(name)){if(undefined===numArguments||undefined!==Module[name].overloadTable&&undefined!==Module[name].overloadTable[numArguments]){throwBindingError(`Cannot register public name '${name}' twice`)}ensureOverloadTable(Module,name,name);if(Module.hasOwnProperty(numArguments)){throwBindingError(`Cannot register multiple overloads of a function with the same number of arguments (${numArguments})!`)}Module[name].overloadTable[numArguments]=value}else{Module[name]=value;if(undefined!==numArguments){Module[name].numArguments=numArguments}}};function RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast){this.name=name;this.constructor=constructor;this.instancePrototype=instancePrototype;this.rawDestructor=rawDestructor;this.baseClass=baseClass;this.getActualType=getActualType;this.upcast=upcast;this.downcast=downcast;this.pureVirtualFunctions=[]}var upcastPointer=(ptr,ptrClass,desiredClass)=>{while(ptrClass!==desiredClass){if(!ptrClass.upcast){throwBindingError(`Expected null or instance of ${desiredClass.name}, got an instance of ${ptrClass.name}`)}ptr=ptrClass.upcast(ptr);ptrClass=ptrClass.baseClass}return ptr};function constNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`)}return 0}if(!handle.$$){throwBindingError(`Cannot pass \"${embindRepr(handle)}\" as a ${this.name}`)}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`)}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function genericPointerToWireType(destructors,handle){var ptr;if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`)}if(this.isSmartPointer){ptr=this.rawConstructor();if(destructors!==null){destructors.push(this.rawDestructor,ptr)}return ptr}else{return 0}}if(!handle.$$){throwBindingError(`Cannot pass \"${embindRepr(handle)}\" as a ${this.name}`)}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`)}if(!this.isConst&&handle.$$.ptrType.isConst){throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name} to parameter type ${this.name}`)}var handleClass=handle.$$.ptrType.registeredClass;ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);if(this.isSmartPointer){if(undefined===handle.$$.smartPtr){throwBindingError(\"Passing raw pointer to smart pointer is illegal\")}switch(this.sharingPolicy){case 0:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr}else{throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name} to parameter type ${this.name}`)}break;case 1:ptr=handle.$$.smartPtr;break;case 2:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr}else{var clonedHandle=handle[\"clone\"]();ptr=this.rawShare(ptr,Emval.toHandle(()=>clonedHandle[\"delete\"]()));if(destructors!==null){destructors.push(this.rawDestructor,ptr)}}break;default:throwBindingError(\"Unsupporting sharing policy\")}}return ptr}function nonConstNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`)}return 0}if(!handle.$$){throwBindingError(`Cannot pass \"${embindRepr(handle)}\" as a ${this.name}`)}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`)}if(handle.$$.ptrType.isConst){throwBindingError(`Cannot convert argument of type ${handle.$$.ptrType.name} to parameter type ${this.name}`)}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function readPointer(pointer){return this[\"fromWireType\"](HEAPU32[pointer>>2])}var init_RegisteredPointer=()=>{Object.assign(RegisteredPointer.prototype,{getPointee(ptr){if(this.rawGetPointee){ptr=this.rawGetPointee(ptr)}return ptr},destructor(ptr){if(this.rawDestructor){this.rawDestructor(ptr)}},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":readPointer,\"deleteObject\"(handle){if(handle!==null){handle[\"delete\"]()}},\"fromWireType\":RegisteredPointer_fromWireType})};function RegisteredPointer(name,registeredClass,isReference,isConst,isSmartPointer,pointeeType,sharingPolicy,rawGetPointee,rawConstructor,rawShare,rawDestructor){this.name=name;this.registeredClass=registeredClass;this.isReference=isReference;this.isConst=isConst;this.isSmartPointer=isSmartPointer;this.pointeeType=pointeeType;this.sharingPolicy=sharingPolicy;this.rawGetPointee=rawGetPointee;this.rawConstructor=rawConstructor;this.rawShare=rawShare;this.rawDestructor=rawDestructor;if(!isSmartPointer&&registeredClass.baseClass===undefined){if(isConst){this[\"toWireType\"]=constNoSmartPtrRawPointerToWireType;this.destructorFunction=null}else{this[\"toWireType\"]=nonConstNoSmartPtrRawPointerToWireType;this.destructorFunction=null}}else{this[\"toWireType\"]=genericPointerToWireType}}var replacePublicSymbol=(name,value,numArguments)=>{if(!Module.hasOwnProperty(name)){throwInternalError(\"Replacing nonexistant public symbol\")}if(undefined!==Module[name].overloadTable&&undefined!==numArguments){Module[name].overloadTable[numArguments]=value}else{Module[name]=value;Module[name].argCount=numArguments}};var dynCallLegacy=(sig,ptr,args)=>{var f=Module[\"dynCall_\"+sig];return args&&args.length?f.apply(null,[ptr].concat(args)):f.call(null,ptr)};var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};var dynCall=(sig,ptr,args)=>{if(sig.includes(\"j\")){return dynCallLegacy(sig,ptr,args)}var rtn=getWasmTableEntry(ptr).apply(null,args);return rtn};var getDynCaller=(sig,ptr)=>{var argCache=[];return function(){argCache.length=0;Object.assign(argCache,arguments);return dynCall(sig,ptr,argCache)}};var embind__requireFunction=(signature,rawFunction)=>{signature=readLatin1String(signature);function makeDynCaller(){if(signature.includes(\"j\")){return getDynCaller(signature,rawFunction)}return getWasmTableEntry(rawFunction)}var fp=makeDynCaller();if(typeof fp!=\"function\"){throwBindingError(`unknown function pointer with signature ${signature}: ${rawFunction}`)}return fp};var extendError=(baseErrorType,errorName)=>{var errorClass=createNamedFunction(errorName,function(message){this.name=errorName;this.message=message;var stack=new Error(message).stack;if(stack!==undefined){this.stack=this.toString()+\"\\n\"+stack.replace(/^Error(:[^\\n]*)?\\n/,\"\")}});errorClass.prototype=Object.create(baseErrorType.prototype);errorClass.prototype.constructor=errorClass;errorClass.prototype.toString=function(){if(this.message===undefined){return this.name}else{return`${this.name}: ${this.message}`}};return errorClass};var UnboundTypeError;var getTypeName=type=>{var ptr=___getTypeName(type);var rv=readLatin1String(ptr);_free(ptr);return rv};var throwUnboundTypeError=(message,types)=>{var unboundTypes=[];var seen={};function visit(type){if(seen[type]){return}if(registeredTypes[type]){return}if(typeDependencies[type]){typeDependencies[type].forEach(visit);return}unboundTypes.push(type);seen[type]=true}types.forEach(visit);throw new UnboundTypeError(`${message}: `+unboundTypes.map(getTypeName).join([\", \"]))};var __embind_register_class=(rawType,rawPointerType,rawConstPointerType,baseClassRawType,getActualTypeSignature,getActualType,upcastSignature,upcast,downcastSignature,downcast,name,destructorSignature,rawDestructor)=>{name=readLatin1String(name);getActualType=embind__requireFunction(getActualTypeSignature,getActualType);if(upcast){upcast=embind__requireFunction(upcastSignature,upcast)}if(downcast){downcast=embind__requireFunction(downcastSignature,downcast)}rawDestructor=embind__requireFunction(destructorSignature,rawDestructor);var legalFunctionName=makeLegalFunctionName(name);exposePublicSymbol(legalFunctionName,function(){throwUnboundTypeError(`Cannot construct ${name} due to unbound types`,[baseClassRawType])});whenDependentTypesAreResolved([rawType,rawPointerType,rawConstPointerType],baseClassRawType?[baseClassRawType]:[],function(base){base=base[0];var baseClass;var basePrototype;if(baseClassRawType){baseClass=base.registeredClass;basePrototype=baseClass.instancePrototype}else{basePrototype=ClassHandle.prototype}var constructor=createNamedFunction(legalFunctionName,function(){if(Object.getPrototypeOf(this)!==instancePrototype){throw new BindingError(\"Use 'new' to construct \"+name)}if(undefined===registeredClass.constructor_body){throw new BindingError(name+\" has no accessible constructor\")}var body=registeredClass.constructor_body[arguments.length];if(undefined===body){throw new BindingError(`Tried to invoke ctor of ${name} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(registeredClass.constructor_body).toString()}) parameters instead!`)}return body.apply(this,arguments)});var instancePrototype=Object.create(basePrototype,{constructor:{value:constructor}});constructor.prototype=instancePrototype;var registeredClass=new RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast);if(registeredClass.baseClass){if(registeredClass.baseClass.__derivedClasses===undefined){registeredClass.baseClass.__derivedClasses=[]}registeredClass.baseClass.__derivedClasses.push(registeredClass)}var referenceConverter=new RegisteredPointer(name,registeredClass,true,false,false);var pointerConverter=new RegisteredPointer(name+\"*\",registeredClass,false,false,false);var constPointerConverter=new RegisteredPointer(name+\" const*\",registeredClass,false,true,false);registeredPointers[rawType]={pointerType:pointerConverter,constPointerType:constPointerConverter};replacePublicSymbol(legalFunctionName,constructor);return[referenceConverter,pointerConverter,constPointerConverter]})};var heap32VectorToArray=(count,firstElement)=>{var array=[];for(var i=0;i<count;i++){array.push(HEAPU32[firstElement+i*4>>2])}return array};function newFunc(constructor,argumentList){if(!(constructor instanceof Function)){throw new TypeError(`new_ called with constructor type ${typeof constructor} which is not a function`)}var dummy=createNamedFunction(constructor.name||\"unknownFunctionName\",function(){});dummy.prototype=constructor.prototype;var obj=new dummy;var r=constructor.apply(obj,argumentList);return r instanceof Object?r:obj}function craftInvokerFunction(humanName,argTypes,classType,cppInvokerFunc,cppTargetFunc,isAsync){var argCount=argTypes.length;if(argCount<2){throwBindingError(\"argTypes array size mismatch! Must at least get return value and 'this' types!\")}var isClassMethodFunc=argTypes[1]!==null&&classType!==null;var needsDestructorStack=false;for(var i=1;i<argTypes.length;++i){if(argTypes[i]!==null&&argTypes[i].destructorFunction===undefined){needsDestructorStack=true;break}}var returns=argTypes[0].name!==\"void\";var argsList=\"\";var argsListWired=\"\";for(var i=0;i<argCount-2;++i){argsList+=(i!==0?\", \":\"\")+\"arg\"+i;argsListWired+=(i!==0?\", \":\"\")+\"arg\"+i+\"Wired\"}var invokerFnBody=`\\n        return function ${makeLegalFunctionName(humanName)}(${argsList}) {\\n        if (arguments.length !== ${argCount-2}) {\\n          throwBindingError('function ${humanName} called with ' + arguments.length + ' arguments, expected ${argCount-2}');\\n        }`;if(needsDestructorStack){invokerFnBody+=\"var destructors = [];\\n\"}var dtorStack=needsDestructorStack?\"destructors\":\"null\";var args1=[\"throwBindingError\",\"invoker\",\"fn\",\"runDestructors\",\"retType\",\"classParam\"];var args2=[throwBindingError,cppInvokerFunc,cppTargetFunc,runDestructors,argTypes[0],argTypes[1]];if(isClassMethodFunc){invokerFnBody+=\"var thisWired = classParam.toWireType(\"+dtorStack+\", this);\\n\"}for(var i=0;i<argCount-2;++i){invokerFnBody+=\"var arg\"+i+\"Wired = argType\"+i+\".toWireType(\"+dtorStack+\", arg\"+i+\"); // \"+argTypes[i+2].name+\"\\n\";args1.push(\"argType\"+i);args2.push(argTypes[i+2])}if(isClassMethodFunc){argsListWired=\"thisWired\"+(argsListWired.length>0?\", \":\"\")+argsListWired}invokerFnBody+=(returns||isAsync?\"var rv = \":\"\")+\"invoker(fn\"+(argsListWired.length>0?\", \":\"\")+argsListWired+\");\\n\";if(needsDestructorStack){invokerFnBody+=\"runDestructors(destructors);\\n\"}else{for(var i=isClassMethodFunc?1:2;i<argTypes.length;++i){var paramName=i===1?\"thisWired\":\"arg\"+(i-2)+\"Wired\";if(argTypes[i].destructorFunction!==null){invokerFnBody+=paramName+\"_dtor(\"+paramName+\"); // \"+argTypes[i].name+\"\\n\";args1.push(paramName+\"_dtor\");args2.push(argTypes[i].destructorFunction)}}}if(returns){invokerFnBody+=\"var ret = retType.fromWireType(rv);\\n\"+\"return ret;\\n\"}else{}invokerFnBody+=\"}\\n\";args1.push(invokerFnBody);return newFunc(Function,args1).apply(null,args2)}var __embind_register_class_constructor=(rawClassType,argCount,rawArgTypesAddr,invokerSignature,invoker,rawConstructor)=>{var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);invoker=embind__requireFunction(invokerSignature,invoker);whenDependentTypesAreResolved([],[rawClassType],function(classType){classType=classType[0];var humanName=`constructor ${classType.name}`;if(undefined===classType.registeredClass.constructor_body){classType.registeredClass.constructor_body=[]}if(undefined!==classType.registeredClass.constructor_body[argCount-1]){throw new BindingError(`Cannot register multiple constructors with identical number of parameters (${argCount-1}) for class '${classType.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`)}classType.registeredClass.constructor_body[argCount-1]=()=>{throwUnboundTypeError(`Cannot construct ${classType.name} due to unbound types`,rawArgTypes)};whenDependentTypesAreResolved([],rawArgTypes,argTypes=>{argTypes.splice(1,0,null);classType.registeredClass.constructor_body[argCount-1]=craftInvokerFunction(humanName,argTypes,null,invoker,rawConstructor);return[]});return[]})};var __embind_register_class_function=(rawClassType,methodName,argCount,rawArgTypesAddr,invokerSignature,rawInvoker,context,isPureVirtual,isAsync)=>{var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);methodName=readLatin1String(methodName);rawInvoker=embind__requireFunction(invokerSignature,rawInvoker);whenDependentTypesAreResolved([],[rawClassType],function(classType){classType=classType[0];var humanName=`${classType.name}.${methodName}`;if(methodName.startsWith(\"@@\")){methodName=Symbol[methodName.substring(2)]}if(isPureVirtual){classType.registeredClass.pureVirtualFunctions.push(methodName)}function unboundTypesHandler(){throwUnboundTypeError(`Cannot call ${humanName} due to unbound types`,rawArgTypes)}var proto=classType.registeredClass.instancePrototype;var method=proto[methodName];if(undefined===method||undefined===method.overloadTable&&method.className!==classType.name&&method.argCount===argCount-2){unboundTypesHandler.argCount=argCount-2;unboundTypesHandler.className=classType.name;proto[methodName]=unboundTypesHandler}else{ensureOverloadTable(proto,methodName,humanName);proto[methodName].overloadTable[argCount-2]=unboundTypesHandler}whenDependentTypesAreResolved([],rawArgTypes,function(argTypes){var memberFunction=craftInvokerFunction(humanName,argTypes,classType,rawInvoker,context,isAsync);if(undefined===proto[methodName].overloadTable){memberFunction.argCount=argCount-2;proto[methodName]=memberFunction}else{proto[methodName].overloadTable[argCount-2]=memberFunction}return[]});return[]})};var validateThis=(this_,classType,humanName)=>{if(!(this_ instanceof Object)){throwBindingError(`${humanName} with invalid \"this\": ${this_}`)}if(!(this_ instanceof classType.registeredClass.constructor)){throwBindingError(`${humanName} incompatible with \"this\" of type ${this_.constructor.name}`)}if(!this_.$$.ptr){throwBindingError(`cannot call emscripten binding method ${humanName} on deleted object`)}return upcastPointer(this_.$$.ptr,this_.$$.ptrType.registeredClass,classType.registeredClass)};var __embind_register_class_property=(classType,fieldName,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext)=>{fieldName=readLatin1String(fieldName);getter=embind__requireFunction(getterSignature,getter);whenDependentTypesAreResolved([],[classType],function(classType){classType=classType[0];var humanName=`${classType.name}.${fieldName}`;var desc={get(){throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`,[getterReturnType,setterArgumentType])},enumerable:true,configurable:true};if(setter){desc.set=()=>throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`,[getterReturnType,setterArgumentType])}else{desc.set=v=>throwBindingError(humanName+\" is a read-only property\")}Object.defineProperty(classType.registeredClass.instancePrototype,fieldName,desc);whenDependentTypesAreResolved([],setter?[getterReturnType,setterArgumentType]:[getterReturnType],function(types){var getterReturnType=types[0];var desc={get(){var ptr=validateThis(this,classType,humanName+\" getter\");return getterReturnType[\"fromWireType\"](getter(getterContext,ptr))},enumerable:true};if(setter){setter=embind__requireFunction(setterSignature,setter);var setterArgumentType=types[1];desc.set=function(v){var ptr=validateThis(this,classType,humanName+\" setter\");var destructors=[];setter(setterContext,ptr,setterArgumentType[\"toWireType\"](destructors,v));runDestructors(destructors)}}Object.defineProperty(classType.registeredClass.instancePrototype,fieldName,desc);return[]});return[]})};function handleAllocatorInit(){Object.assign(HandleAllocator.prototype,{get(id){return this.allocated[id]},has(id){return this.allocated[id]!==undefined},allocate(handle){var id=this.freelist.pop()||this.allocated.length;this.allocated[id]=handle;return id},free(id){this.allocated[id]=undefined;this.freelist.push(id)}})}function HandleAllocator(){this.allocated=[undefined];this.freelist=[]}var emval_handles=new HandleAllocator;var __emval_decref=handle=>{if(handle>=emval_handles.reserved&&0===--emval_handles.get(handle).refcount){emval_handles.free(handle)}};var count_emval_handles=()=>{var count=0;for(var i=emval_handles.reserved;i<emval_handles.allocated.length;++i){if(emval_handles.allocated[i]!==undefined){++count}}return count};var init_emval=()=>{emval_handles.allocated.push({value:undefined},{value:null},{value:true},{value:false});emval_handles.reserved=emval_handles.allocated.length;Module[\"count_emval_handles\"]=count_emval_handles};var Emval={toValue:handle=>{if(!handle){throwBindingError(\"Cannot use deleted val. handle = \"+handle)}return emval_handles.get(handle).value},toHandle:value=>{switch(value){case undefined:return 1;case null:return 2;case true:return 3;case false:return 4;default:{return emval_handles.allocate({refcount:1,value:value})}}}};var __embind_register_emval=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":handle=>{var rv=Emval.toValue(handle);__emval_decref(handle);return rv},\"toWireType\":(destructors,value)=>Emval.toHandle(value),\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:null})};var enumReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?function(pointer){return this[\"fromWireType\"](HEAP8[pointer>>0])}:function(pointer){return this[\"fromWireType\"](HEAPU8[pointer>>0])};case 2:return signed?function(pointer){return this[\"fromWireType\"](HEAP16[pointer>>1])}:function(pointer){return this[\"fromWireType\"](HEAPU16[pointer>>1])};case 4:return signed?function(pointer){return this[\"fromWireType\"](HEAP32[pointer>>2])}:function(pointer){return this[\"fromWireType\"](HEAPU32[pointer>>2])};default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_enum=(rawType,name,size,isSigned)=>{name=readLatin1String(name);function ctor(){}ctor.values={};registerType(rawType,{name:name,constructor:ctor,\"fromWireType\":function(c){return this.constructor.values[c]},\"toWireType\":(destructors,c)=>c.value,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":enumReadValueFromPointer(name,size,isSigned),destructorFunction:null});exposePublicSymbol(name,ctor)};var requireRegisteredType=(rawType,humanName)=>{var impl=registeredTypes[rawType];if(undefined===impl){throwBindingError(humanName+\" has unknown type \"+getTypeName(rawType))}return impl};var __embind_register_enum_value=(rawEnumType,name,enumValue)=>{var enumType=requireRegisteredType(rawEnumType,\"enum\");name=readLatin1String(name);var Enum=enumType.constructor;var Value=Object.create(enumType.constructor.prototype,{value:{value:enumValue},constructor:{value:createNamedFunction(`${enumType.name}_${name}`,function(){})}});Enum.values[enumValue]=Value;Enum[name]=Value};var embindRepr=v=>{if(v===null){return\"null\"}var t=typeof v;if(t===\"object\"||t===\"array\"||t===\"function\"){return v.toString()}else{return\"\"+v}};var floatReadValueFromPointer=(name,width)=>{switch(width){case 4:return function(pointer){return this[\"fromWireType\"](HEAPF32[pointer>>2])};case 8:return function(pointer){return this[\"fromWireType\"](HEAPF64[pointer>>3])};default:throw new TypeError(`invalid float width (${width}): ${name}`)}};var __embind_register_float=(rawType,name,size)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":value=>value,\"toWireType\":(destructors,value)=>value,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":floatReadValueFromPointer(name,size),destructorFunction:null})};var __embind_register_function=(name,argCount,rawArgTypesAddr,signature,rawInvoker,fn,isAsync)=>{var argTypes=heap32VectorToArray(argCount,rawArgTypesAddr);name=readLatin1String(name);rawInvoker=embind__requireFunction(signature,rawInvoker);exposePublicSymbol(name,function(){throwUnboundTypeError(`Cannot call ${name} due to unbound types`,argTypes)},argCount-1);whenDependentTypesAreResolved([],argTypes,function(argTypes){var invokerArgsArray=[argTypes[0],null].concat(argTypes.slice(1));replacePublicSymbol(name,craftInvokerFunction(name,invokerArgsArray,null,rawInvoker,fn,isAsync),argCount-1);return[]})};var integerReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?pointer=>HEAP8[pointer>>0]:pointer=>HEAPU8[pointer>>0];case 2:return signed?pointer=>HEAP16[pointer>>1]:pointer=>HEAPU16[pointer>>1];case 4:return signed?pointer=>HEAP32[pointer>>2]:pointer=>HEAPU32[pointer>>2];default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_integer=(primitiveType,name,size,minRange,maxRange)=>{name=readLatin1String(name);if(maxRange===-1){maxRange=4294967295}var fromWireType=value=>value;if(minRange===0){var bitshift=32-8*size;fromWireType=value=>value<<bitshift>>>bitshift}var isUnsignedType=name.includes(\"unsigned\");var checkAssertions=(value,toTypeName)=>{};var toWireType;if(isUnsignedType){toWireType=function(destructors,value){checkAssertions(value,this.name);return value>>>0}}else{toWireType=function(destructors,value){checkAssertions(value,this.name);return value}}registerType(primitiveType,{name:name,\"fromWireType\":fromWireType,\"toWireType\":toWireType,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":integerReadValueFromPointer(name,size,minRange!==0),destructorFunction:null})};var __embind_register_memory_view=(rawType,dataTypeIndex,name)=>{var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){var size=HEAPU32[handle>>2];var data=HEAPU32[handle+4>>2];return new TA(HEAP8.buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":decodeMemoryView,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":decodeMemoryView},{ignoreDuplicateRegistrations:true})};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var UTF8Decoder=typeof TextDecoder!=\"undefined\"?new TextDecoder(\"utf8\"):undefined;var UTF8ArrayToString=(heapOrArray,idx,maxBytesToRead)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str=\"\";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\";var __embind_register_std_string=(rawType,name)=>{name=readLatin1String(name);var stdStringIsUTF8=name===\"std::string\";registerType(rawType,{name:name,\"fromWireType\"(value){var length=HEAPU32[value>>2];var payload=value+4;var str;if(stdStringIsUTF8){var decodeStartPtr=payload;for(var i=0;i<=length;++i){var currentBytePtr=payload+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[payload+i])}str=a.join(\"\")}_free(value);return str},\"toWireType\"(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var length;var valueIsOfTypeString=typeof value==\"string\";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError(\"Cannot pass non-string to std::string\")}if(stdStringIsUTF8&&valueIsOfTypeString){length=lengthBytesUTF8(value)}else{length=value.length}var base=_malloc(4+length+1);var ptr=base+4;HEAPU32[base>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr,length+1)}else{if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError(\"String has UTF-16 code units that do not fit in 8 bits\")}HEAPU8[ptr+i]=charCode}}else{for(var i=0;i<length;++i){HEAPU8[ptr+i]=value[i]}}}if(destructors!==null){destructors.push(_free,base)}return base},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":readPointer,destructorFunction(ptr){_free(ptr)}})};var UTF16Decoder=typeof TextDecoder!=\"undefined\"?new TextDecoder(\"utf-16le\"):undefined;var UTF16ToString=(ptr,maxBytesToRead)=>{var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&HEAPU16[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder)return UTF16Decoder.decode(HEAPU8.subarray(ptr,endPtr));var str=\"\";for(var i=0;!(i>=maxBytesToRead/2);++i){var codeUnit=HEAP16[ptr+i*2>>1];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit)}return str};var stringToUTF16=(str,outPtr,maxBytesToWrite)=>{if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2}HEAP16[outPtr>>1]=0;return outPtr-startPtr};var lengthBytesUTF16=str=>str.length*2;var UTF32ToString=(ptr,maxBytesToRead)=>{var i=0;var str=\"\";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str};var stringToUTF32=(str,outPtr,maxBytesToWrite)=>{if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr};var lengthBytesUTF32=str=>{var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len};var __embind_register_std_wstring=(rawType,charSize,name)=>{name=readLatin1String(name);var decodeString,encodeString,getHeap,lengthBytesUTF,shift;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;getHeap=()=>HEAPU16;shift=1}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;getHeap=()=>HEAPU32;shift=2}registerType(rawType,{name:name,\"fromWireType\":value=>{var length=HEAPU32[value>>2];var HEAP=getHeap();var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||HEAP[currentBytePtr>>shift]==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},\"toWireType\":(destructors,value)=>{if(!(typeof value==\"string\")){throwBindingError(`Cannot pass non-string to C++ string type ${name}`)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length>>shift;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction(ptr){_free(ptr)}})};var __embind_register_value_array=(rawType,name,constructorSignature,rawConstructor,destructorSignature,rawDestructor)=>{tupleRegistrations[rawType]={name:readLatin1String(name),rawConstructor:embind__requireFunction(constructorSignature,rawConstructor),rawDestructor:embind__requireFunction(destructorSignature,rawDestructor),elements:[]}};var __embind_register_value_array_element=(rawTupleType,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext)=>{tupleRegistrations[rawTupleType].elements.push({getterReturnType:getterReturnType,getter:embind__requireFunction(getterSignature,getter),getterContext:getterContext,setterArgumentType:setterArgumentType,setter:embind__requireFunction(setterSignature,setter),setterContext:setterContext})};var __embind_register_value_object=(rawType,name,constructorSignature,rawConstructor,destructorSignature,rawDestructor)=>{structRegistrations[rawType]={name:readLatin1String(name),rawConstructor:embind__requireFunction(constructorSignature,rawConstructor),rawDestructor:embind__requireFunction(destructorSignature,rawDestructor),fields:[]}};var __embind_register_value_object_field=(structType,fieldName,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext)=>{structRegistrations[structType].fields.push({fieldName:readLatin1String(fieldName),getterReturnType:getterReturnType,getter:embind__requireFunction(getterSignature,getter),getterContext:getterContext,setterArgumentType:setterArgumentType,setter:embind__requireFunction(setterSignature,setter),setterContext:setterContext})};var __embind_register_void=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,\"argPackAdvance\":0,\"fromWireType\":()=>undefined,\"toWireType\":(destructors,o)=>undefined})};var __emval_incref=handle=>{if(handle>4){emval_handles.get(handle).refcount+=1}};var __emval_take_value=(type,arg)=>{type=requireRegisteredType(type,\"_emval_take_value\");var v=type[\"readValueFromPointer\"](arg);return Emval.toHandle(v)};var _abort=()=>{abort(\"\")};var _emscripten_memcpy_js=(dest,src,num)=>HEAPU8.copyWithin(dest,src,src+num);var getHeapMax=()=>2147483648;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false};var ENV={};var getExecutableName=()=>thisProgram||\"./this.program\";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator==\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\";var env={\"USER\":\"web_user\",\"LOGNAME\":\"web_user\",\"PATH\":\"/\",\"PWD\":\"/\",\"HOME\":\"/home/<USER>",\"LANG\":lang,\"_\":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}HEAP8[buffer>>0]=0};var PATH={isAbs:path=>path.charAt(0)===\"/\",splitPath:filename=>{var splitPathRe=/^(\\/?|)([\\s\\S]*?)((?:\\.{1,2}|[^\\/]+?|)(\\.[^.\\/]*|))(?:[\\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last===\".\"){parts.splice(i,1)}else if(last===\"..\"){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift(\"..\")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.substr(-1)===\"/\";path=PATH.normalizeArray(path.split(\"/\").filter(p=>!!p),!isAbsolute).join(\"/\");if(!path&&!isAbsolute){path=\".\"}if(path&&trailingSlash){path+=\"/\"}return(isAbsolute?\"/\":\"\")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return\".\"}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir},basename:path=>{if(path===\"/\")return\"/\";path=PATH.normalize(path);path=path.replace(/\\/$/,\"\");var lastSlash=path.lastIndexOf(\"/\");if(lastSlash===-1)return path;return path.substr(lastSlash+1)},join:function(){var paths=Array.prototype.slice.call(arguments);return PATH.normalize(paths.join(\"/\"))},join2:(l,r)=>PATH.normalize(l+\"/\"+r)};var initRandomFill=()=>{if(typeof crypto==\"object\"&&typeof crypto[\"getRandomValues\"]==\"function\"){return view=>crypto.getRandomValues(view)}else abort(\"initRandomDevice\")};var randomFill=view=>(randomFill=initRandomFill())(view);var PATH_FS={resolve:function(){var resolvedPath=\"\",resolvedAbsolute=false;for(var i=arguments.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?arguments[i]:FS.cwd();if(typeof path!=\"string\"){throw new TypeError(\"Arguments to path.resolve must be strings\")}else if(!path){return\"\"}resolvedPath=path+\"/\"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split(\"/\").filter(p=>!!p),!resolvedAbsolute).join(\"/\");return(resolvedAbsolute?\"/\":\"\")+resolvedPath||\".\"},relative:(from,to)=>{from=PATH_FS.resolve(from).substr(1);to=PATH_FS.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!==\"\")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!==\"\")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split(\"/\"));var toParts=trim(to.split(\"/\"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push(\"..\")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join(\"/\")}};var FS_stdin_getChar_buffer=[];function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var FS_stdin_getChar=()=>{if(!FS_stdin_getChar_buffer.length){var result=null;if(typeof window!=\"undefined\"&&typeof window.prompt==\"function\"){result=window.prompt(\"Input: \");if(result!==null){result+=\"\\n\"}}else if(typeof readline==\"function\"){result=readline();if(result!==null){result+=\"\\n\"}}if(!result){return null}FS_stdin_getChar_buffer=intArrayFromString(result,true)}return FS_stdin_getChar_buffer.shift()};var TTY={ttys:[],init(){},shutdown(){},register(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close(stream){stream.tty.ops.fsync(stream.tty)},fsync(stream){stream.tty.ops.fsync(stream.tty)},read(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.timestamp=Date.now()}return i}},default_tty_ops:{get_char(tty){return FS_stdin_getChar()},put_char(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}},ioctl_tcgets(tty){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets(tty,optional_actions,data){return 0},ioctl_tiocgwinsz(tty){return[24,80]}},default_tty1_ops:{put_char(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}}}};var mmapAlloc=size=>{abort()};var MEMFS={ops_table:null,mount(mount){return MEMFS.createNode(null,\"/\",16384|511,0)},createNode(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}if(!MEMFS.ops_table){MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}}}var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node;parent.timestamp=node.timestamp}return node},getFileDataAsTypedArray(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup(parent,name){throw FS.genericErrors[44]},mknod(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}}delete old_node.parent.contents[old_node.name];old_node.parent.timestamp=Date.now();old_node.name=new_name;new_dir.contents[new_name]=old_node;new_dir.timestamp=old_node.parent.timestamp;old_node.parent=new_dir},unlink(parent,name){delete parent.contents[name];parent.timestamp=Date.now()},rmdir(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.timestamp=Date.now()},readdir(node){var entries=[\".\",\"..\"];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries},symlink(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write(stream,buffer,offset,length,position,canOwn){if(buffer.buffer===HEAP8.buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},allocate(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)},mmap(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents.buffer===HEAP8.buffer){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}HEAP8.set(contents,ptr)}return{ptr:ptr,allocated:allocated}},msync(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};var asyncLoad=(url,onload,onerror,noRunDep)=>{var dep=!noRunDep?getUniqueRunDependency(`al ${url}`):\"\";readAsync(url,arrayBuffer=>{assert(arrayBuffer,`Loading data file \"${url}\" failed (no arrayBuffer).`);onload(new Uint8Array(arrayBuffer));if(dep)removeRunDependency(dep)},event=>{if(onerror){onerror()}else{throw`Loading data file \"${url}\" failed.`}});if(dep)addRunDependency(dep)};var FS_createDataFile=(parent,name,fileData,canRead,canWrite,canOwn)=>FS.createDataFile(parent,name,fileData,canRead,canWrite,canOwn);var preloadPlugins=Module[\"preloadPlugins\"]||[];var FS_handledByPreloadPlugin=(byteArray,fullname,finish,onerror)=>{if(typeof Browser!=\"undefined\")Browser.init();var handled=false;preloadPlugins.forEach(plugin=>{if(handled)return;if(plugin[\"canHandle\"](fullname)){plugin[\"handle\"](byteArray,fullname,finish,onerror);handled=true}});return handled};var FS_createPreloadedFile=(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish)=>{var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){if(preFinish)preFinish();if(!dontCreateFile){FS_createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}if(onload)onload();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{if(onerror)onerror();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url==\"string\"){asyncLoad(url,byteArray=>processData(byteArray),onerror)}else{processData(url)}};var FS_modeStringToFlags=str=>{var flagModes={\"r\":0,\"r+\":2,\"w\":512|64|1,\"w+\":512|64|2,\"a\":1024|64|1,\"a+\":1024|64|2};var flags=flagModes[str];if(typeof flags==\"undefined\"){throw new Error(`Unknown file open mode: ${str}`)}return flags};var FS_getMode=(canRead,canWrite)=>{var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:\"/\",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath(path,opts={}){path=PATH_FS.resolve(path);if(!path)return{path:\"\",node:null};var defaults={follow_mount:true,recurse_count:0};opts=Object.assign(defaults,opts);if(opts.recurse_count>8){throw new FS.ErrnoError(32)}var parts=path.split(\"/\").filter(p=>!!p);var current=FS.root;var current_path=\"/\";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH_FS.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count+1});current=lookup.node;if(count++>40){throw new FS.ErrnoError(32)}}}}return{path:current_path,node:current}},getPath(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!==\"/\"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode(parent,name){var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode,parent)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode(parent,name,mode,rdev){var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode(node){FS.hashRemoveNode(node)},isRoot(node){return node===node.parent},isMountpoint(node){return!!node.mounted},isFile(mode){return(mode&61440)===32768},isDir(mode){return(mode&61440)===16384},isLink(mode){return(mode&61440)===40960},isChrdev(mode){return(mode&61440)===8192},isBlkdev(mode){return(mode&61440)===24576},isFIFO(mode){return(mode&61440)===4096},isSocket(mode){return(mode&49152)===49152},flagsToPermissionString(flag){var perms=[\"r\",\"w\",\"rw\"][flag&3];if(flag&512){perms+=\"w\"}return perms},nodePermissions(node,perms){if(FS.ignorePermissions){return 0}if(perms.includes(\"r\")&&!(node.mode&292)){return 2}else if(perms.includes(\"w\")&&!(node.mode&146)){return 2}else if(perms.includes(\"x\")&&!(node.mode&73)){return 2}return 0},mayLookup(dir){var errCode=FS.nodePermissions(dir,\"x\");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate(dir,name){try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,\"wx\")},mayDelete(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,\"wx\");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen(node,flags){if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!==\"r\"||flags&512){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},MAX_OPEN_FDS:4096,nextfd(){for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStreamChecked(fd){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}return stream},getStream:fd=>FS.streams[fd],createStream(stream,fd=-1){if(!FS.FSStream){FS.FSStream=function(){this.shared={}};FS.FSStream.prototype={};Object.defineProperties(FS.FSStream.prototype,{object:{get(){return this.node},set(val){this.node=val}},isRead:{get(){return(this.flags&2097155)!==1}},isWrite:{get(){return(this.flags&2097155)!==0}},isAppend:{get(){return this.flags&1024}},flags:{get(){return this.shared.flags},set(val){this.shared.flags=val}},position:{get(){return this.shared.position},set(val){this.shared.position=val}}})}stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream(fd){FS.streams[fd]=null},chrdev_stream_ops:{open(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;if(stream.stream_ops.open){stream.stream_ops.open(stream)}},llseek(){throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice(dev,ops){FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push.apply(check,m.mounts)}return mounts},syncfs(populate,callback){if(typeof populate==\"function\"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount(type,opts,mountpoint){var root=mountpoint===\"/\";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);node.mount.mounts.splice(idx,1)},lookup(parent,name){return parent.node_ops.lookup(parent,name)},mknod(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name===\".\"||name===\"..\"){throw new FS.ErrnoError(28)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},create(path,mode){mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir(path,mode){mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree(path,mode){var dirs=path.split(\"/\");var d=\"\";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+=\"/\"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev(path,mode,dev){if(typeof dev==\"undefined\"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink(oldpath,newpath){if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!==\".\"){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!==\".\"){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,\"w\");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(54)}return node.node_ops.readdir(node)},unlink(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return PATH_FS.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))},stat(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(44)}if(!node.node_ops.getattr){throw new FS.ErrnoError(63)}return node.node_ops.getattr(node)},lstat(path){return FS.stat(path,true)},chmod(path,mode,dontFollow){var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})},lchmod(path,mode){FS.chmod(path,mode,true)},fchmod(fd,mode){var stream=FS.getStreamChecked(fd);FS.chmod(stream.node,mode)},chown(path,uid,gid,dontFollow){var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{timestamp:Date.now()})},lchown(path,uid,gid){FS.chown(path,uid,gid,true)},fchown(fd,uid,gid){var stream=FS.getStreamChecked(fd);FS.chown(stream.node,uid,gid)},truncate(path,len){if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,\"w\");if(errCode){throw new FS.ErrnoError(errCode)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})},ftruncate(fd,len){var stream=FS.getStreamChecked(fd);if((stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.truncate(stream.node,len)},utime(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})},open(path,flags,mode){if(path===\"\"){throw new FS.ErrnoError(44)}flags=typeof flags==\"string\"?FS_modeStringToFlags(flags):flags;mode=typeof mode==\"undefined\"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path==\"object\"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module[\"logReadFiles\"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed(stream){return stream.fd===null},llseek(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read(stream,buffer,offset,length,position){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!=\"undefined\";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write(stream,buffer,offset,length,position,canOwn){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!=\"undefined\";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},allocate(stream,offset,length){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(offset<0||length<=0){throw new FS.ErrnoError(28)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(43)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(138)}stream.stream_ops.allocate(stream,offset,length)},mmap(stream,length,position,prot,flags){if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync(stream,buffer,offset,length,mmapFlags){if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},munmap:stream=>0,ioctl(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile(path,opts={}){opts.flags=opts.flags||0;opts.encoding=opts.encoding||\"binary\";if(opts.encoding!==\"utf8\"&&opts.encoding!==\"binary\"){throw new Error(`Invalid encoding type \"${opts.encoding}\"`)}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding===\"utf8\"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding===\"binary\"){ret=buf}FS.close(stream);return ret},writeFile(path,data,opts={}){opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data==\"string\"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error(\"Unsupported data type\")}FS.close(stream)},cwd:()=>FS.currentPath,chdir(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,\"x\");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories(){FS.mkdir(\"/tmp\");FS.mkdir(\"/home\");FS.mkdir(\"/home/<USER>")},createDefaultDevices(){FS.mkdir(\"/dev\");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length});FS.mkdev(\"/dev/null\",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev(\"/dev/tty\",FS.makedev(5,0));FS.mkdev(\"/dev/tty1\",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomLeft=randomFill(randomBuffer).byteLength}return randomBuffer[--randomLeft]};FS.createDevice(\"/dev\",\"random\",randomByte);FS.createDevice(\"/dev\",\"urandom\",randomByte);FS.mkdir(\"/dev/shm\");FS.mkdir(\"/dev/shm/tmp\")},createSpecialDirectories(){FS.mkdir(\"/proc\");var proc_self=FS.mkdir(\"/proc/self\");FS.mkdir(\"/proc/self/fd\");FS.mount({mount(){var node=FS.createNode(proc_self,\"fd\",16384|511,73);node.node_ops={lookup(parent,name){var fd=+name;var stream=FS.getStreamChecked(fd);var ret={parent:null,mount:{mountpoint:\"fake\"},node_ops:{readlink:()=>stream.path}};ret.parent=ret;return ret}};return node}},{},\"/proc/self/fd\")},createStandardStreams(){if(Module[\"stdin\"]){FS.createDevice(\"/dev\",\"stdin\",Module[\"stdin\"])}else{FS.symlink(\"/dev/tty\",\"/dev/stdin\")}if(Module[\"stdout\"]){FS.createDevice(\"/dev\",\"stdout\",null,Module[\"stdout\"])}else{FS.symlink(\"/dev/tty\",\"/dev/stdout\")}if(Module[\"stderr\"]){FS.createDevice(\"/dev\",\"stderr\",null,Module[\"stderr\"])}else{FS.symlink(\"/dev/tty1\",\"/dev/stderr\")}var stdin=FS.open(\"/dev/stdin\",0);var stdout=FS.open(\"/dev/stdout\",1);var stderr=FS.open(\"/dev/stderr\",1)},ensureErrnoError(){if(FS.ErrnoError)return;FS.ErrnoError=function ErrnoError(errno,node){this.name=\"ErrnoError\";this.node=node;this.setErrno=function(errno){this.errno=errno};this.setErrno(errno);this.message=\"FS error\"};FS.ErrnoError.prototype=new Error;FS.ErrnoError.prototype.constructor=FS.ErrnoError;[44].forEach(code=>{FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack=\"<generic error, no stack>\"})},staticInit(){FS.ensureErrnoError();FS.nameTable=new Array(4096);FS.mount(MEMFS,{},\"/\");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={\"MEMFS\":MEMFS}},init(input,output,error){FS.init.initialized=true;FS.ensureErrnoError();Module[\"stdin\"]=input||Module[\"stdin\"];Module[\"stdout\"]=output||Module[\"stdout\"];Module[\"stderr\"]=error||Module[\"stderr\"];FS.createStandardStreams()},quit(){FS.init.initialized=false;for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}},findObject(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path===\"/\"}catch(e){ret.error=e.errno}return ret},createPath(parent,path,canRead,canWrite){parent=typeof parent==\"string\"?parent:FS.getPath(parent);var parts=path.split(\"/\").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current},createFile(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent==\"string\"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile(parent,name,data,canRead,canWrite,canOwn){var path=name;if(parent){parent=typeof parent==\"string\"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data==\"string\"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}return node},createDevice(parent,name,input,output){var path=PATH.join2(typeof parent==\"string\"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open(stream){stream.seekable=false},close(stream){if(output&&output.buffer&&output.buffer.length){output(10)}},read(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.timestamp=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!=\"undefined\"){throw new Error(\"Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.\")}else if(read_){try{obj.contents=intArrayFromString(read_(obj.url),true);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}else{throw new Error(\"Cannot load without read() or XMLHttpRequest.\")}},createLazyFile(parent,name,url,canRead,canWrite){function LazyUint8Array(){this.lengthKnown=false;this.chunks=[]}LazyUint8Array.prototype.get=function LazyUint8Array_get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]};LazyUint8Array.prototype.setDataGetter=function LazyUint8Array_setDataGetter(getter){this.getter=getter};LazyUint8Array.prototype.cacheLength=function LazyUint8Array_cacheLength(){var xhr=new XMLHttpRequest;xhr.open(\"HEAD\",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error(\"Couldn't load \"+url+\". Status: \"+xhr.status);var datalength=Number(xhr.getResponseHeader(\"Content-length\"));var header;var hasByteServing=(header=xhr.getResponseHeader(\"Accept-Ranges\"))&&header===\"bytes\";var usesGzip=(header=xhr.getResponseHeader(\"Content-Encoding\"))&&header===\"gzip\";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error(\"invalid range (\"+from+\", \"+to+\") or no bytes requested!\");if(to>datalength-1)throw new Error(\"only \"+datalength+\" bytes available! programmer error!\");var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);if(datalength!==chunkSize)xhr.setRequestHeader(\"Range\",\"bytes=\"+from+\"-\"+to);xhr.responseType=\"arraybuffer\";if(xhr.overrideMimeType){xhr.overrideMimeType(\"text/plain; charset=x-user-defined\")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error(\"Couldn't load \"+url+\". Status: \"+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||\"\",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]==\"undefined\"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]==\"undefined\")throw new Error(\"doXHR failed!\");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out(\"LazyFiles on gzip forces download of the whole file when length is accessed\")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true};if(typeof XMLHttpRequest!=\"undefined\"){if(!ENVIRONMENT_IS_WORKER)throw\"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc\";var lazyArray=new LazyUint8Array;Object.defineProperties(lazyArray,{length:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._length}},chunkSize:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}});var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=function forceLoadLazyFile(){FS.forceLoadFile(node);return fn.apply(null,arguments)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,HEAP8,ptr,length,position);return{ptr:ptr,allocated:true}};node.stream_ops=stream_ops;return node}};var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return PATH.join2(dir,path)},doStat(func,path,buf){try{var stat=func(path)}catch(e){if(e&&e.node&&PATH.normalize(path)!==PATH.normalize(FS.getPath(e.node))){return-54}throw e}HEAP32[buf>>2]=stat.dev;HEAP32[buf+4>>2]=stat.mode;HEAPU32[buf+8>>2]=stat.nlink;HEAP32[buf+12>>2]=stat.uid;HEAP32[buf+16>>2]=stat.gid;HEAP32[buf+20>>2]=stat.rdev;tempI64=[stat.size>>>0,(tempDouble=stat.size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+24>>2]=tempI64[0],HEAP32[buf+28>>2]=tempI64[1];HEAP32[buf+32>>2]=4096;HEAP32[buf+36>>2]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();tempI64=[Math.floor(atime/1e3)>>>0,(tempDouble=Math.floor(atime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+40>>2]=tempI64[0],HEAP32[buf+44>>2]=tempI64[1];HEAPU32[buf+48>>2]=atime%1e3*1e3;tempI64=[Math.floor(mtime/1e3)>>>0,(tempDouble=Math.floor(mtime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+56>>2]=tempI64[0],HEAP32[buf+60>>2]=tempI64[1];HEAPU32[buf+64>>2]=mtime%1e3*1e3;tempI64=[Math.floor(ctime/1e3)>>>0,(tempDouble=Math.floor(ctime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+72>>2]=tempI64[0],HEAP32[buf+76>>2]=tempI64[1];HEAPU32[buf+80>>2]=ctime%1e3*1e3;tempI64=[stat.ino>>>0,(tempDouble=stat.ino,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+88>>2]=tempI64[0],HEAP32[buf+92>>2]=tempI64[1];return 0},doMsync(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=HEAPU8.slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},varargs:undefined,get(){var ret=HEAP32[+SYSCALLS.varargs>>2];SYSCALLS.varargs+=4;return ret},getp(){return SYSCALLS.get()},getStr(ptr){var ret=UTF8ToString(ptr);return ret},getStreamFromFD(fd){var stream=FS.getStreamChecked(fd);return stream}};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;getEnvStrings().forEach((string,i)=>{var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(string=>bufSize+=string.length+1);HEAPU32[penviron_buf_size>>2]=bufSize;return 0};function _fd_close(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var doReadv=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.read(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break;if(typeof offset!==\"undefined\"){offset+=curr}}return ret};function _fd_read(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var convertI32PairToI53Checked=(lo,hi)=>hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN;function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);try{if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);tempI64=[stream.position>>>0,(tempDouble=stream.position,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[newOffset>>2]=tempI64[0],HEAP32[newOffset+4>>2]=tempI64[1];if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var doWritev=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(typeof offset!==\"undefined\"){offset+=curr}}return ret};function _fd_write(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var isLeapYear=year=>year%4===0&&(year%100!==0||year%400===0);var arraySum=(array,index)=>{var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum};var MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];var addDays=(date,days)=>{var newDate=new Date(date.getTime());while(days>0){var leap=isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate};var writeArrayToMemory=(array,buffer)=>{HEAP8.set(array,buffer)};var _strftime=(s,maxsize,format,tm)=>{var tm_zone=HEAPU32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):\"\"};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_1[rule])}var WEEKDAYS=[\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"];var MONTHS=[\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];function leadingSomething(value,digits,character){var str=typeof value==\"number\"?value.toString():value||\"\";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,\"0\")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}return thisDate.getFullYear()}return thisDate.getFullYear()-1}var EXPANSION_RULES_2={\"%a\":date=>WEEKDAYS[date.tm_wday].substring(0,3),\"%A\":date=>WEEKDAYS[date.tm_wday],\"%b\":date=>MONTHS[date.tm_mon].substring(0,3),\"%B\":date=>MONTHS[date.tm_mon],\"%C\":date=>{var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},\"%d\":date=>leadingNulls(date.tm_mday,2),\"%e\":date=>leadingSomething(date.tm_mday,2,\" \"),\"%g\":date=>getWeekBasedYear(date).toString().substring(2),\"%G\":date=>getWeekBasedYear(date),\"%H\":date=>leadingNulls(date.tm_hour,2),\"%I\":date=>{var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},\"%j\":date=>leadingNulls(date.tm_mday+arraySum(isLeapYear(date.tm_year+1900)?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR,date.tm_mon-1),3),\"%m\":date=>leadingNulls(date.tm_mon+1,2),\"%M\":date=>leadingNulls(date.tm_min,2),\"%n\":()=>\"\\n\",\"%p\":date=>{if(date.tm_hour>=0&&date.tm_hour<12){return\"AM\"}return\"PM\"},\"%S\":date=>leadingNulls(date.tm_sec,2),\"%t\":()=>\"\\t\",\"%u\":date=>date.tm_wday||7,\"%U\":date=>{var days=date.tm_yday+7-date.tm_wday;return leadingNulls(Math.floor(days/7),2)},\"%V\":date=>{var val=Math.floor((date.tm_yday+7-(date.tm_wday+6)%7)/7);if((date.tm_wday+371-date.tm_yday-2)%7<=2){val++}if(!val){val=52;var dec31=(date.tm_wday+7-date.tm_yday-1)%7;if(dec31==4||dec31==5&&isLeapYear(date.tm_year%400-1)){val++}}else if(val==53){var jan1=(date.tm_wday+371-date.tm_yday)%7;if(jan1!=4&&(jan1!=3||!isLeapYear(date.tm_year)))val=1}return leadingNulls(val,2)},\"%w\":date=>date.tm_wday,\"%W\":date=>{var days=date.tm_yday+7-(date.tm_wday+6)%7;return leadingNulls(Math.floor(days/7),2)},\"%y\":date=>(date.tm_year+1900).toString().substring(2),\"%Y\":date=>date.tm_year+1900,\"%z\":date=>{var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?\"+\":\"-\")+String(\"0000\"+off).slice(-4)},\"%Z\":date=>date.tm_zone,\"%%\":()=>\"%\"};pattern=pattern.replace(/%%/g,\"\\0\\0\");for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_2[rule](date))}}pattern=pattern.replace(/\\0\\0/g,\"%\");var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1};var _strftime_l=(s,maxsize,format,tm,loc)=>_strftime(s,maxsize,format,tm);var getCFunc=ident=>{var func=Module[\"_\"+ident];return func};var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var ccall=(ident,returnType,argTypes,args,opts)=>{var toC={\"string\":str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},\"array\":arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType===\"string\"){return UTF8ToString(ret)}if(returnType===\"boolean\")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret};InternalError=Module[\"InternalError\"]=class InternalError extends Error{constructor(message){super(message);this.name=\"InternalError\"}};embind_init_charCodes();BindingError=Module[\"BindingError\"]=class BindingError extends Error{constructor(message){super(message);this.name=\"BindingError\"}};init_ClassHandle();init_embind();init_RegisteredPointer();UnboundTypeError=Module[\"UnboundTypeError\"]=extendError(Error,\"UnboundTypeError\");handleAllocatorInit();init_emval();var FSNode=function(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev};var readMode=292|73;var writeMode=146;Object.defineProperties(FSNode.prototype,{read:{get:function(){return(this.mode&readMode)===readMode},set:function(val){val?this.mode|=readMode:this.mode&=~readMode}},write:{get:function(){return(this.mode&writeMode)===writeMode},set:function(val){val?this.mode|=writeMode:this.mode&=~writeMode}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}});FS.FSNode=FSNode;FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();var wasmImports={d:___cxa_throw,n:__embind_finalize_value_array,l:__embind_finalize_value_object,w:__embind_register_bigint,G:__embind_register_bool,h:__embind_register_class,g:__embind_register_class_constructor,c:__embind_register_class_function,q:__embind_register_class_property,F:__embind_register_emval,p:__embind_register_enum,i:__embind_register_enum_value,t:__embind_register_float,a:__embind_register_function,j:__embind_register_integer,e:__embind_register_memory_view,u:__embind_register_std_string,r:__embind_register_std_wstring,o:__embind_register_value_array,b:__embind_register_value_array_element,m:__embind_register_value_object,f:__embind_register_value_object_field,H:__embind_register_void,I:__emval_decref,J:__emval_incref,k:__emval_take_value,s:_abort,E:_emscripten_memcpy_js,y:_emscripten_resize_heap,z:_environ_get,A:_environ_sizes_get,B:_fd_close,D:_fd_read,v:_fd_seek,C:_fd_write,x:_strftime_l};var wasmExports=createWasm();var ___wasm_call_ctors=()=>(___wasm_call_ctors=wasmExports[\"L\"])();var _malloc=Module[\"_malloc\"]=a0=>(_malloc=Module[\"_malloc\"]=wasmExports[\"M\"])(a0);var _free=Module[\"_free\"]=a0=>(_free=Module[\"_free\"]=wasmExports[\"N\"])(a0);var ___getTypeName=a0=>(___getTypeName=wasmExports[\"P\"])(a0);var __embind_initialize_bindings=Module[\"__embind_initialize_bindings\"]=()=>(__embind_initialize_bindings=Module[\"__embind_initialize_bindings\"]=wasmExports[\"Q\"])();var ___errno_location=()=>(___errno_location=wasmExports[\"__errno_location\"])();var stackSave=()=>(stackSave=wasmExports[\"R\"])();var stackRestore=a0=>(stackRestore=wasmExports[\"S\"])(a0);var stackAlloc=a0=>(stackAlloc=wasmExports[\"T\"])(a0);var ___cxa_increment_exception_refcount=a0=>(___cxa_increment_exception_refcount=wasmExports[\"__cxa_increment_exception_refcount\"])(a0);var ___cxa_is_pointer_type=a0=>(___cxa_is_pointer_type=wasmExports[\"U\"])(a0);var dynCall_jiji=Module[\"dynCall_jiji\"]=(a0,a1,a2,a3,a4)=>(dynCall_jiji=Module[\"dynCall_jiji\"]=wasmExports[\"V\"])(a0,a1,a2,a3,a4);var dynCall_viijii=Module[\"dynCall_viijii\"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_viijii=Module[\"dynCall_viijii\"]=wasmExports[\"W\"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_iiiiij=Module[\"dynCall_iiiiij\"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_iiiiij=Module[\"dynCall_iiiiij\"]=wasmExports[\"X\"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_iiiiijj=Module[\"dynCall_iiiiijj\"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8)=>(dynCall_iiiiijj=Module[\"dynCall_iiiiijj\"]=wasmExports[\"Y\"])(a0,a1,a2,a3,a4,a5,a6,a7,a8);var dynCall_iiiiiijj=Module[\"dynCall_iiiiiijj\"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(dynCall_iiiiiijj=Module[\"dynCall_iiiiiijj\"]=wasmExports[\"Z\"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);Module[\"ccall\"]=ccall;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(){if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}run();\n\n\n  return moduleArg.ready\n}\n\n);\n})();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Module);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../node_modules/@splinetool/runtime/build/process.js\n"));

/***/ })

}]);
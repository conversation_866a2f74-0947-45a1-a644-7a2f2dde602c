import React from "react";
import { Player } from "@lottiefiles/react-lottie-player";

const glitchAnimationUrl =
  "https://assets6.lottiefiles.com/packages/lf20_rbtawnzi.json"; // Glitch animation
const mascotAnimationUrl =
  "https://assets2.lottiefiles.com/packages/lf20_ydo1amjm.json"; // Mascot animation saying "Hi there!"

export default function GlitchSaysHi() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-6">
      <style>
        {`
          .glitch-heading {
            font-size: 3rem;
            color: #fff;
            position: relative;
            text-shadow: 2px 2px #000, -2px -2px #444;
            animation: glitchEffect 1.5s infinite;
          }

          @keyframes glitchEffect {
            0% {
              transform: translate(0);
            }
            20% {
              transform: translate(-2px, 2px);
            }
            40% {
              transform: translate(2px, -2px);
            }
            60% {
              transform: translate(0);
            }
            80% {
              transform: translate(2px, 2px);
            }
            100% {
              transform: translate(0);
            }
          }
        `}
      </style>

      {/* Animated Heading */}
      <h1 className="glitch-heading mb-8">Glitch Says Hi!</h1>

      {/* Mascot Section */}
      <div className="flex items-center justify-center mb-8">
        {/* Mascot Animation */}
        <Player
          autoplay
          loop
          src={mascotAnimationUrl}
          style={{
            width: "150px",
            height: "150px",
          }}
        />
        {/* Speech Bubble */}
        <div className="ml-4 bg-white bg-opacity-80 text-gray-800 font-bold text-lg p-4 rounded-lg shadow-lg">
          Hi there! Welcome to Data Science!
        </div>
      </div>

      {/* Description */}
      <p className="text-center text-lg text-white max-w-3xl mb-6">
        Glitch is here to welcome you to the world of Data Science! Just like a
        small glitch in a program that can reveal hidden bugs, we’ll explore
        data to uncover powerful insights. Data Science is about asking
        questions and finding answers in the form of patterns, predictions, and
        visualizations.
      </p>

      {/* Interactive Animation */}
      <div className="w-full max-w-lg mb-6">
        <Player
          autoplay
          loop
          src={glitchAnimationUrl}
          style={{
            width: "100%",
            height: "100%",
          }}
        />
      </div>

      {/* Key Concepts */}
      <div className="w-full max-w-4xl bg-white bg-opacity-20 rounded-lg p-6 text-white shadow-lg">
        <h2 className="text-2xl font-bold mb-4 text-center">
          Key Concepts Introduced:
        </h2>
        <ul className="list-disc list-inside text-lg space-y-3">
          <li>
            <strong>What is Data?</strong> Data is raw information collected
            from the world around us, from numbers and text to images and
            videos.
          </li>
          <li>
            <strong>Patterns in Data:</strong> Finding patterns in data is like
            solving a mystery. Patterns can help us understand the past and
            predict the future.
          </li>
          <li>
            <strong>The Role of Data Science:</strong> Data Science transforms
            data into meaningful stories through analysis, visualization, and
            machine learning.
          </li>
        </ul>
      </div>

      {/* Encouragement Section */}
      <p className="text-center text-white text-xl font-semibold mt-6">
        Let&apos;s start this exciting journey where small glitches lead to big
        discoveries. Say hi to Glitch and the world of Data Science!
      </p>
    </div>
  );
}

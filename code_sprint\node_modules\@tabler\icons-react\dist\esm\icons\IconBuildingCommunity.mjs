/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBuildingCommunity = createReactComponent("outline", "building-community", "IconBuildingCommunity", [["path", { "d": "M8 9l5 5v7h-5v-4m0 4h-5v-7l5 -5m1 1v-6a1 1 0 0 1 1 -1h10a1 1 0 0 1 1 1v17h-8", "key": "svg-0" }], ["path", { "d": "M13 7l0 .01", "key": "svg-1" }], ["path", { "d": "M17 7l0 .01", "key": "svg-2" }], ["path", { "d": "M17 11l0 .01", "key": "svg-3" }], ["path", { "d": "M17 15l0 .01", "key": "svg-4" }]]);

export { IconBuildingCommunity as default };
//# sourceMappingURL=IconBuildingCommunity.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandSublimeText = createReactComponent("outline", "brand-sublime-text", "IconBrandSublimeText", [["path", { "d": "M19 8l-14 4.5v-5.5l14 -4.5z", "key": "svg-0" }], ["path", { "d": "M19 17l-14 4.5v-5.5l14 -4.5z", "key": "svg-1" }], ["path", { "d": "M19 11.5l-14 -4.5", "key": "svg-2" }], ["path", { "d": "M5 12.5l14 4.5", "key": "svg-3" }]]);

export { IconBrandSublimeText as default };
//# sourceMappingURL=IconBrandSublimeText.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandPicsart = createReactComponent("outline", "brand-picsart", "IconBrandPicsart", [["path", { "d": "M12 9m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0", "key": "svg-0" }], ["path", { "d": "M12 9m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-1" }], ["path", { "d": "M5 9v11a2 2 0 1 0 4 0v-4.5", "key": "svg-2" }]]);

export { IconBrandPicsart as default };
//# sourceMappingURL=IconBrandPicsart.mjs.map

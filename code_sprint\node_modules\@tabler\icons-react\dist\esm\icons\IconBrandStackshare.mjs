/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandStackshare = createReactComponent("outline", "brand-stackshare", "IconBrandStackshare", [["path", { "d": "M19 6m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M19 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M5 12m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-2" }], ["path", { "d": "M7 12h3l3.5 6h3.5", "key": "svg-3" }], ["path", { "d": "M17 6h-3.5l-3.5 6", "key": "svg-4" }]]);

export { IconBrandStackshare as default };
//# sourceMappingURL=IconBrandStackshare.mjs.map

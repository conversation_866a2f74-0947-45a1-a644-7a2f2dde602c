/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandVite = createReactComponent("outline", "brand-vite", "IconBrandVite", [["path", { "d": "M10 4.5l6 -1.5l-2 6.5l2 -.5l-4 7v-5l-3 1z", "key": "svg-0" }], ["path", { "d": "M15 6.5l7 -1.5l-10 17l-10 -17l7.741 1.5", "key": "svg-1" }]]);

export { IconBrandVite as default };
//# sourceMappingURL=IconBrandVite.mjs.map

import React, { useState, useRef, useEffect } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "./useTypingAnimation";
import styles from "./animations.module.css";
import { Player } from "@lottiefiles/react-lottie-player";

const DontMakeMeGuess = () => {
  const [code] = useState(
    `Welcome to the Guessing Game!\nGuess a number between 1 and 10:\n`
  );
  const [userInput, setUserInput] = useState("");
  const [output, setOutput] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [isAnimationCompleted, setIsAnimationCompleted] = useState(false);
  const outputRef = useRef(null);
  const [buttonClicked, setButtonClicked] = useState(false);
  const typedText = useTypingAnimation("Don't Make Me Guess", 100);

  const [animatedCode, setAnimatedCode] = useState("");

  useEffect(() => {
    if (isAnimationCompleted) return;

    let typingInterval;
    let currentIndex = 0;

    const typeCode = () => {
      typingInterval = setInterval(() => {
        if (currentIndex < code.length) {
          const nextChar = code[currentIndex];
          setAnimatedCode((prev) => prev + nextChar);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsAnimationCompleted(true);
        }
      }, 100);
    };

    typeCode();

    return () => clearInterval(typingInterval);
  }, [code, isAnimationCompleted]);


  const runCode = () => {
    try {
      const simulatedInput = userInput?.trim().toLowerCase();

      if (!simulatedInput) {
        setOutput("Error: Please provide an input.");
        setShowOutput(true);
        scrollToOutput();
        return;
      }

      let output = "";
      let confetti = false;


      const secretNumber = 7;
      const guess = parseInt(simulatedInput);

      if (guess === secretNumber) {
        output = "Congratulations! You guessed the correct number.";
        confetti = true;
      } else {
        output = "Wrong guess. Try again.";
        confetti = false;
      }

      setOutput(output);
      setShowConfetti(confetti);
      setShowOutput(true);
      scrollToOutput();


      if (confetti) {
        setTimeout(() => setShowConfetti(false), 1000);
      }
    } catch {
      setOutput("Error: An unexpected error occurred.");
      setShowOutput(true);
    }
  };


  const scrollToOutput = () => {
    setTimeout(() => {
      if (outputRef.current) {
        outputRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  };

  const handleTyping = (newCode) => {
    if (isAnimationCompleted) {

      const lines = newCode.split("\n");
      const prompt = "Guess a number between 1 and 10: ";
      console.log(prompt);
      const inputWithoutPrompt = lines.slice(2).join("\n");
      setUserInput(inputWithoutPrompt);
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "linear-gradient(to bottom, #3f4238, #b98b73)",
        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        {showConfetti && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            {}
            <div
              style={{
                position: "absolute",
                width: "15px",
                height: "15px",
                backgroundColor: "#FF4081",
                borderRadius: "50%",
                animation: "fall 1.5s infinite ease-in-out",
              }}
            />
          </div>
        )}
        <h1
          style={{
            fontSize: "2.5rem",
            fontWeight: "bold",
            backgroundImage: "linear-gradient(to right, #3f4238, #b98b73)",
            WebkitBackgroundClip: "text",
            color: "transparent",
            marginBottom: "20px",
          }}
        >
          {typedText}
        </h1>{" "}
      <div className="max-w-2xl text-center">
        <p className="text-lg text-gray-700">
          You might have heard the term <strong>“iterate”</strong> a lot in the programming world. Iterate just means &quot;to repeat.&quot;
        </p>
        <p className="text-lg text-gray-700 mt-2">
          In programming, loops are used to repeat a block of code until a certain condition is met. This is a powerful tool that&apos;s used frequently!
        </p>
        <p className="text-lg text-gray-700 mt-2">
          The first type of loop we&apos;re going to learn is the <code className="font-mono text-blue-500">while</code> loop. You can think of a while loop like a traffic circle. Each time you go around the circle, it&apos;s one iteration! A car will keep looping around until it’s no longer able to continue.
        </p>
        <hr className="my-6 border-t border-gray-300" />
        <p className="text-lg font-bold text-black mt-6">INSTRUCTIONS</p>
        <p className="text-sm text-gray-700 mt-2">
          Before we dive deep into the <code className="font-mono text-blue-500">while</code> loop, let&apos;s see an example:
        </p>
        <p className="text-sm text-gray-700 mt-2">
          Create a <code className="font-mono text-blue-500">guess.py</code> program and type in the following:
        </p>
        <pre className="bg-gray-100 p-4 rounded-lg text-left text-gray-800">
          secret_number = 7{"\n"}
          guess = int(input(&quot;Guess a number between 1 and 10: &quot;)){"\n"}
          {"\n"}
          while guess != secret_number:{"\n"}
          {"    "}guess = int(input(&quot;Wrong guess. Try again: &quot;)){"\n"}
          {"\n"}
          print(&quot;Congratulations! You guessed the correct number.&quot;)
        </pre>
        </div>
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#9E5647",
            padding: "20px",
            borderRadius: "10px",
            textAlign: "left",
          }}
        >
          <Editor
            value={isAnimationCompleted ? code + userInput : animatedCode}
            onValueChange={handleTyping}
            highlight={(code) =>
              Prism.highlight(code, Prism.languages.python, "python")
            }
            padding={10}
            style={{
              position: "relative",
              zIndex: 10,
              fontFamily: '"Fira Code", monospace',
              fontSize: "14px",
              backgroundColor: "#C5705D",
              color: "#EEEEEE",
              minHeight: "150px",
              borderRadius: "10px",
            }}
          />
        </div>{" "}
        <div>
          {}
          <div
            className={`${styles.confetti} ${styles.visible}`}
            style={{
              position: "absolute",
              top: "0",
              left: "0",
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              zIndex: "9999",
              backgroundColor: "transparent",
            }}
          >
            {}
            {showConfetti && (
              <Player
                autoplay
                loop
                src="https://fonts.gstatic.com/s/e/notoemoji/latest/1f389/lottie.json"
                style={{
                  width: "100px",
                  height: "100px",
                  position: "absolute",
                  top: "77%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              />
            )}
          </div>

          {}
          {showOutput && (
            <div
              ref={outputRef}
              className={styles.fadeInUp}
              style={{
                backgroundColor: "#6a9c89",
                padding: "15px",
                borderRadius: "10px",
                textAlign: "left",
                marginTop: "20px",
                border: "1px solid #F3E5F5",
              }}
            >
              <h2
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  color: "#654520",
                }}
              >
                Output:
              </h2>
              <pre
                style={{ fontSize: "1rem", color: "#654520", marginTop: "10px" }}
              >
                {output}
              </pre>
            </div>
          )}
          <div>
            <button
              onClick={() => {
                if (buttonClicked) {

                  setButtonClicked(false);
                  setUserInput("");
                  setOutput("");
                  setShowOutput(false);
                  setShowConfetti(false);
                  setAnimatedCode("");
                  setIsAnimationCompleted(true);
                } else {

                  runCode();
                  setButtonClicked(true);
                }
              }}
              className={styles.fadeInUp}
              style={{
                position: "relative",
                zIndex: 10,
                marginTop: "20px",
                backgroundColor: buttonClicked ? "#654520" : "#6A9C89",
                color: buttonClicked ? "#6A9C89" : "#654520",
                padding: "10px 20px",
                borderRadius: "30px",
                fontWeight: "bold",
                fontSize: "1rem",
                cursor: "pointer",
                boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)",
                transition: "all 0.3s ease-in-out",
              }}
            >
              {buttonClicked ? "Retry 🔄" : "Run Code 🎉"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DontMakeMeGuess;

import React from "react";

export default function MarkerlessAR() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-purple-400 to-pink-500 text-white p-10">
      <div className="max-w-3xl text-center">
        <h1 className="text-5xl font-bold mb-6">Markerless AR</h1>
        <p className="text-lg mb-6">
          Markerless Augmented Reality (AR) is super cool because it doesn’t need physical markers like QR codes. Instead, it uses your device’s sensors and cameras to understand the world around you. It’s like adding magic to your surroundings!
        </p>

        {/* How Markerless AR Works */}
        <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg mb-8">
          <h2 className="text-3xl font-bold mb-4 text-yellow-300">How It Works</h2>
          <p className="text-lg text-gray-800">
            Markerless AR detects flat surfaces or objects in your environment using your device's camera and sensors. Once the environment is mapped, it overlays digital objects like 3D models or animations onto the real world.
          </p>
          <img 
            src="path-to-image/markerless-ar-how-it-works.jpg" 
            alt="How Markerless AR Works" 
            className="w-full rounded-lg mt-4"
          />
        </div>

        {/* Examples of Markerless AR */}
        <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg mb-8">
          <h2 className="text-3xl font-bold mb-4 text-yellow-300">Fun Examples</h2>
          <ul className="list-disc ml-6 space-y-2 text-lg text-gray-800">
            <li>
              Use apps like IKEA Place to see how a virtual couch would look in your living room.
            </li>
            <li>
              Try social media filters that turn you into a cartoon or add virtual accessories like hats and glasses.
            </li>
            <li>
              Play AR games like Pokémon GO, where you can catch creatures that appear in real-world locations.
            </li>
          </ul>
          <img 
            src="path-to-image/markerless-ar-examples.jpg" 
            alt="Examples of Markerless AR" 
            className="w-full rounded-lg mt-4"
          />
        </div>

        {/* Why Markerless AR is Awesome */}
        <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg">
          <h2 className="text-3xl font-bold mb-4 text-yellow-300">Why It’s Awesome</h2>
          <p className="text-lg text-gray-800">
            Markerless AR lets you explore and interact with the digital world in any space. It’s perfect for learning, gaming, and creative fun. Imagine designing your dream room or catching magical creatures wherever you go!
          </p>
          <img 
            src="path-to-image/markerless-ar-cool.jpg" 
            alt="Why Markerless AR is Awesome" 
            className="w-full rounded-lg mt-4"
          />
        </div>
      </div>
    </div>
  );
}

import React from "react";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-python";
import useTypingAnimation from "../Section1/useTypingAnimation";

const Actiongames = () => {
  const typedText = useTypingAnimation("Action Games", 100);

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        background: "linear-gradient(to bottom, #34ace0, #33d9b2)",

        padding: "20px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          maxWidth: "830px",
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          padding: "30px",
          borderRadius: "20px",
          boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        ></div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "40px",
            marginBottom: "20px",
          }}
        >
          <h1
            style={{
              fontSize: "2.5rem",
              fontWeight: "bold",
              backgroundImage: "linear-gradient(to right, #34ace0, #33d9b2)",
              WebkitBackgroundClip: "text",
              color: "transparent",
              marginBottom: "20px",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {typedText}
          </h1>
        </div>{" "}
        <p className="text-lg text-gray-700 mb-4">
          If you&apos;re all about fast-paced gameplay, action games are where you&apos;ll
          find your excitement. These games are full of intense battles, quick
          reflexes, and heart-pounding moments.
        </p>
        <img src="/valorant.png" alt="Valorant" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Valorant:</strong> Valorant combines shooting skills with
          strategic team play. In this game, two teams of players compete to
          complete objectives, such as planting a bomb or rescuing hostages.
          It&apos;s like a digital version of capture the flag, with lots of action
          and strategy mixed in. Quick thinking and teamwork are essential to
          victory.
        </p>
        <img src="/apex-legends.png" alt="Apex Legends" className="mb-4" />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Apex Legends:</strong> This game throws you into a battle
          royale where you and your team must outlast other squads to win. Each
          player selects a unique character, called a &quot;Legend,&quot; with special
          abilities. The action is fast, and every match is different, making
          Apex Legends a thrilling experience.
        </p>
        <img
          src="/super-mario-odyssey.png"
          alt="Super Mario Odyssey"
          className="mb-4"
        />
        <p className="text-lg text-gray-700 mb-4">
          <strong>Super Mario Odyssey:</strong> A favorite for younger players,
          this game takes Mario on a grand adventure through colorful worlds,
          battling enemies, solving puzzles, and collecting Power Moons. It&apos;s a
          great mix of action and exploration, with a friendly vibe.
        </p>
        <p className="text-lg text-gray-700 mb-4">
          Action games are ideal for players who enjoy fast, exciting gameplay
          with lots of challenges. These games often require quick reflexes,
          sharp aim, and the ability to make split-second decisions.
        </p>
      </div>
    </div>
  );
};

export default Actiongames;

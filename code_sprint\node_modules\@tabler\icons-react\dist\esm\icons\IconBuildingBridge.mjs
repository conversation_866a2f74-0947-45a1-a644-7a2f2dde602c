/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBuildingBridge = createReactComponent("outline", "building-bridge", "IconBuildingBridge", [["path", { "d": "M6 5l0 14", "key": "svg-0" }], ["path", { "d": "M18 5l0 14", "key": "svg-1" }], ["path", { "d": "M2 15l20 0", "key": "svg-2" }], ["path", { "d": "M3 8a7.5 7.5 0 0 0 3 -2a6.5 6.5 0 0 0 12 0a7.5 7.5 0 0 0 3 2", "key": "svg-3" }], ["path", { "d": "M12 10l0 5", "key": "svg-4" }]]);

export { IconBuildingBridge as default };
//# sourceMappingURL=IconBuildingBridge.mjs.map

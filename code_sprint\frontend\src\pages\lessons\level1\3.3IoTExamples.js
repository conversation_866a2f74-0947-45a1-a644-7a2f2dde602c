import React, { useState } from "react";

const SmartCityActivity = () => {
  const [cityZones, setCityZones] = useState({
    traffic: [],
    parking: [],
    environment: [],
  });

  const [feedback, setFeedback] = useState("");

  const devices = [
    { id: "Smart Traffic Light", label: "Smart Traffic Light 🚦", correctZone: "traffic" },
    { id: "Parking Sensor", label: "Parking Sensor 🅿️", correctZone: "parking" },
    { id: "Air Quality Monitor", label: "Air Quality Monitor 🌫️", correctZone: "environment" },
    { id: "Smart Waste Bin", label: "Smart Waste Bin 🗑️", correctZone: "environment" },
    { id: "Smart Streetlight", label: "Smart Streetlight 💡", correctZone: "traffic" },
  ];

  const handleDrop = (zone, device) => {
    if (device.correctZone === zone) {
      setCityZones((prev) => ({
        ...prev,
        [zone]: [...prev[zone], device],
      }));
      setFeedback(`✅ Correct! ${device.label} belongs in the ${zone} zone.`);
    } else {
      setFeedback(`❌ Oops! ${device.label} doesn’t belong in the ${zone} zone. Try again!`);
    }
  };

  return (
    <div className="flex flex-col items-center min-h-screen bg-gradient-to-b from-blue-50 via-green-50 to-yellow-50 p-6 text-gray-700">
      <h1 className="text-5xl font-bold text-blue-700 mb-6">Build Your Smart City</h1>

      {/* Explanation Section */}
      <div className="max-w-4xl text-center mb-8">
        <p className="text-lg">
          A smart city uses IoT devices to improve the quality of life by managing resources and services efficiently.
        </p>
        <p className="text-lg mt-2">
          Drag and drop IoT devices into the correct city zones below. You'll receive feedback for each action. Can you complete your smart city?
        </p>
      </div>

      {/* Activity Section */}
      <div className="flex flex-col lg:flex-row gap-8 items-start w-full max-w-6xl">
        {/* Drag Items */}
        <div className="w-full lg:w-1/3">
          <h2 className="text-2xl font-bold mb-4 text-green-600">IoT Devices</h2>
          <div className="space-y-4">
            {devices.map((device) => (
              <div
                key={device.id}
                draggable
                onDragStart={(e) => e.dataTransfer.setData("device", JSON.stringify(device))}
                className="p-4 bg-white border border-gray-300 rounded-lg shadow-lg hover:shadow-xl cursor-pointer transition"
              >
                {device.label}
              </div>
            ))}
          </div>
        </div>

        {/* City Zones */}
        <div className="w-full lg:w-2/3 grid grid-cols-1 lg:grid-cols-3 gap-4">
          <h2 className="text-2xl font-bold col-span-3 text-blue-700 mb-4 text-center">City Zones</h2>

          {/* Traffic Management Zone */}
          <div
            onDrop={(e) => {
              const device = JSON.parse(e.dataTransfer.getData("device"));
              handleDrop("traffic", device);
            }}
            onDragOver={(e) => e.preventDefault()}
            className="h-36 bg-blue-100 border-2 border-dashed border-blue-500 rounded-lg flex flex-col items-center justify-center transition hover:shadow-md"
          >
            <p className="font-semibold text-blue-700">Traffic Zone</p>
            {cityZones.traffic.length > 0 &&
              cityZones.traffic.map((device, index) => (
                <p key={index} className="text-sm mt-1">
                  {device.label}
                </p>
              ))}
          </div>

          {/* Parking Management Zone */}
          <div
            onDrop={(e) => {
              const device = JSON.parse(e.dataTransfer.getData("device"));
              handleDrop("parking", device);
            }}
            onDragOver={(e) => e.preventDefault()}
            className="h-36 bg-green-100 border-2 border-dashed border-green-500 rounded-lg flex flex-col items-center justify-center transition hover:shadow-md"
          >
            <p className="font-semibold text-green-700">Parking Zone</p>
            {cityZones.parking.length > 0 &&
              cityZones.parking.map((device, index) => (
                <p key={index} className="text-sm mt-1">
                  {device.label}
                </p>
              ))}
          </div>

          {/* Environmental Monitoring Zone */}
          <div
            onDrop={(e) => {
              const device = JSON.parse(e.dataTransfer.getData("device"));
              handleDrop("environment", device);
            }}
            onDragOver={(e) => e.preventDefault()}
            className="h-36 bg-yellow-100 border-2 border-dashed border-yellow-500 rounded-lg flex flex-col items-center justify-center transition hover:shadow-md"
          >
            <p className="font-semibold text-yellow-700">Environment Zone</p>
            {cityZones.environment.length > 0 &&
              cityZones.environment.map((device, index) => (
                <p key={index} className="text-sm mt-1">
                  {device.label}
                </p>
              ))}
          </div>
        </div>
      </div>

      {/* Feedback Section */}
      {feedback && (
        <div
          className={`mt-6 p-4 rounded-lg shadow-lg text-lg font-semibold transition ${
            feedback.startsWith("✅")
              ? "bg-green-100 text-green-700"
              : "bg-red-100 text-red-700"
          }`}
        >
          {feedback}
        </div>
      )}

      {/* Summary Section */}
      <div className="mt-8 w-full max-w-4xl bg-white p-6 border border-gray-300 rounded-lg shadow-lg">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">City Summary</h2>
        <ul className="list-disc pl-6 space-y-2 text-lg">
          <li>
            <strong>Traffic Zone:</strong>{" "}
            {cityZones.traffic.length > 0
              ? cityZones.traffic.map((device) => device.label).join(", ")
              : "No devices added yet."}
          </li>
          <li>
            <strong>Parking Zone:</strong>{" "}
            {cityZones.parking.length > 0
              ? cityZones.parking.map((device) => device.label).join(", ")
              : "No devices added yet."}
          </li>
          <li>
            <strong>Environment Zone:</strong>{" "}
            {cityZones.environment.length > 0
              ? cityZones.environment.map((device) => device.label).join(", ")
              : "No devices added yet."}
          </li>
        </ul>
      </div>
    </div>
  );
};

export default SmartCityActivity;

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandFlickr = createReactComponent("outline", "brand-flickr", "IconBrandFlickr", [["path", { "d": "M7 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M17 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-1" }]]);

export { IconBrandFlickr as default };
//# sourceMappingURL=IconBrandFlickr.mjs.map

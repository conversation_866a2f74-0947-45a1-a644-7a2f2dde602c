/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandXdeep = createReactComponent("outline", "brand-xdeep", "IconBrandXdeep", [["path", { "d": "M14.401 8.398l1.599 -2.398h5l-4 6l4 6h-5l-8 -12h-5l4 6l-4 6h5l1.596 -2.393", "key": "svg-0" }]]);

export { IconBrandXdeep as default };
//# sourceMappingURL=IconBrandXdeep.mjs.map

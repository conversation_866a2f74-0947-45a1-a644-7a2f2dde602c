/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandVercelFilled = createReactComponent("filled", "brand-vercel-filled", "IconBrandVercelFilled", [["path", { "d": "M11.143 3.486a1 1 0 0 1 1.714 0l9 15a1 1 0 0 1 -.857 1.514h-18a1 1 0 0 1 -.857 -1.514z", "key": "svg-0" }]]);

export { IconBrandVercelFilled as default };
//# sourceMappingURL=IconBrandVercelFilled.mjs.map

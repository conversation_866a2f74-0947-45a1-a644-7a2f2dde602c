import React from "react";
import Link from "next/link";

const StepThirteen = () => {
  return (
    <div className="text-gray-700 bg-white min-h-screen">
      {/* Header Section */}
      <header className="bg-purple-600 text-white py-8">
        <h1 className="text-4xl font-bold text-center">Step 13: Explore the Scratch Community</h1>
        <p className="text-center mt-2 text-lg">
          Discover amazing projects and connect with other Scratch creators!
        </p>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto mt-8 p-4">
        <div className="bg-white shadow-md p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4 text-purple-700">What Is the Scratch Community?</h2>
          <p className="text-lg mb-4">
            The Scratch community is a safe and friendly place where you can see projects from people all around the world. You can also
            share your own projects and get feedback!
          </p>

          {/* Features of the Scratch Community */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">What Can You Do in the Community?</h3>
          <ul className="list-disc pl-6 space-y-4">
            <li>
              <strong>Explore Projects:</strong> Find games, stories, and animations created by others.
            </li>
            <li>
              <strong>Get Inspired:</strong> See cool ideas that you can try in your own projects.
            </li>
            <li>
              <strong>Connect:</strong> Comment on projects or remix them to make your own version.
            </li>
          </ul>

          {/* Fun Illustration */}
          <div className="flex justify-center my-6">
            <img
              src="/lvl1_img/Screenshot 2024-12-25 153455.png"
              alt="Explore Scratch Community"
              className="rounded-lg shadow-md"
            />
          </div>

          {/* Step-by-Step Instructions */}
          <h3 className="text-xl font-semibold mb-4 text-purple-700">How to Explore the Community:</h3>
          <ol className="list-decimal pl-6 space-y-8">
            <li>
              <strong>Go to the Community:</strong>
              <p className="text-lg mt-2">
                Visit the Scratch homepage at{" "}
                <a
                  href="https://scratch.mit.edu"
                  target="_blank"
                  rel="noreferrer"
                  className="text-blue-600 underline"
                >
                  https://scratch.mit.edu
                </a>
                .
              </p>
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 153350.png"
                  alt="Placeholder: Visit Community"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            <li>
              <strong>Browse Projects:</strong>
              <p className="text-lg mt-2">
                Click on "Explore" or "Featured Projects" to see what others have created.
              </p>
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 153409.png"
                  alt="Placeholder: Browse Projects"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>

            <li>
              <strong>Try Remixing:</strong>
              <p className="text-lg mt-2">
                Remix a project by clicking "See Inside" and making changes. You can save it as your own!
              </p>
              <div className="flex justify-center mt-4">
                <img
                  src="/lvl1_img/Screenshot 2024-12-25 153524.png"
                  alt="Placeholder: Try Remixing"
                  className="rounded-lg shadow-md"
                />
              </div>
            </li>
          </ol>

          {/* Activity Section */}
          <div className="bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Activity: Find Inspiration!</h3>
            <p className="text-lg mb-4">
              Explore three projects in the Scratch community. Write down one idea from each project that you’d like to try in your own
              creation.
            </p>
          </div>
        </div>
      </main>

      {/* Navigation Buttons */}
      <div className="flex justify-between max-w-4xl mx-auto p-4">
        <Link href="/lessons/level1/step-12">
          <button className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Back: Step 12</button>
        </Link>
        <Link href="/lessons/level-1">
          <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Finish</button>
        </Link>
      </div>

      {/* Footer Section */}
      <footer className="bg-gray-100 mt-12 py-4 text-center">
        <p>© {new Date().getFullYear()} Getting Started with Scratch. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default StepThirteen;

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconHierarchy = createReactComponent("outline", "hierarchy", "IconHierarchy", [["path", { "d": "M12 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M5 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M19 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-2" }], ["path", { "d": "M6.5 17.5l5.5 -4.5l5.5 4.5", "key": "svg-3" }], ["path", { "d": "M12 7l0 6", "key": "svg-4" }]]);

export { IconHierarchy as default };
//# sourceMappingURL=IconHierarchy.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBubbleX = createReactComponent("outline", "bubble-x", "IconBubbleX", [["path", { "d": "M13.5 18.75c-.345 .09 -.727 .25 -1.1 .25a4.3 4.3 0 0 1 -1.57 -.298l-3.83 2.298v-3.134a2.668 2.668 0 0 1 -1.795 -3.773a4.8 4.8 0 0 1 2.908 -8.933a5.335 5.335 0 0 1 9.194 1.078a5.333 5.333 0 0 1 4.484 6.778", "key": "svg-0" }], ["path", { "d": "M22 22l-5 -5", "key": "svg-1" }], ["path", { "d": "M17 22l5 -5", "key": "svg-2" }]]);

export { IconBubbleX as default };
//# sourceMappingURL=IconBubbleX.mjs.map

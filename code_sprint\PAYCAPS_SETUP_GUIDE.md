# PayCaps Integration Setup Guide

## 🔐 PayCaps Sandbox Credentials

**Dashboard Access:**
- **URL**: https://sandbox.paycaps.com/pgui/jsp/index
- **Username**: <EMAIL>
- **Password**: Paycaps!@#25

## 💳 Test Card Details

**For Payment Testing:**
- **Card Number**: 2303779999000408
- **Expiry Date**: 06/28
- **CVV**: 123
- **3DS Password**: 123456

## ⚙️ Configuration Steps

### Step 1: Get PayCaps Credentials from Dashboard

1. **Login to PayCaps Dashboard**:
   - Go to: https://sandbox.paycaps.com/pgui/jsp/index
   - Username: <EMAIL>
   - Password: Paycaps!@#25

2. **Find Your Credentials**:
   - Look for **APP_ID** (Application ID)
   - Look for **SECRET KEY** (Merchant Secret)
   - Note down these values

### Step 2: Update Backend Environment Variables

Edit `backend/.env` file and replace:

```env
PAYCAPS_APP_ID=your_actual_app_id_from_dashboard
PAYCAPS_SECRET=your_actual_secret_from_dashboard
```

With your actual values from the PayCaps dashboard.

### Step 3: Test Payment Flow

1. **Start both servers**:
   ```bash
   # Terminal 1 - Backend
   cd backend && node server.js
   
   # Terminal 2 - Frontend
   cd frontend && npm run dev
   ```

2. **Test the payment**:
   - Go to: http://localhost:3000/go-premium
   - Select any premium level
   - Click "Pay Now"
   - Use the test card details provided above

## 🔄 Payment Flow

### Current Setup (Test Mode)
- **Status**: Working in test mode
- **Behavior**: Automatically upgrades users to premium
- **Use Case**: Development and testing

### PayCaps Integration (Production Ready)
- **Status**: Configured but needs real credentials
- **Behavior**: Redirects to PayCaps payment gateway
- **Use Case**: Real payments with test cards in sandbox

## 🧪 Testing Modes

### Mode 1: Quick Test Mode
```env
PAYCAPS_APP_ID=TEST_MODE
```
- Instantly upgrades users to premium
- No external payment gateway
- Perfect for development

### Mode 2: PayCaps Sandbox Mode
```env
PAYCAPS_APP_ID=your_actual_app_id
PAYCAPS_SECRET=your_actual_secret
PAYCAPS_MODE=sandbox
```
- Uses real PayCaps sandbox
- Test with provided card details
- Full payment flow testing

## 📋 API Endpoints

### Payment Processing
- **POST** `/api/process-payment`
- **POST** `/api/payment-callback`

### Authentication
- **POST** `/api/login`
- **POST** `/api/register`
- **GET** `/api/verify-token`

## 🔍 Troubleshooting

### Common Issues:

1. **"PayCaps credentials not configured"**
   - Update PAYCAPS_APP_ID and PAYCAPS_SECRET in .env
   - Restart backend server

2. **Payment fails immediately**
   - Check backend logs for errors
   - Verify PayCaps credentials are correct

3. **Payment callback not working**
   - Ensure callback URL is configured in PayCaps dashboard
   - Check network connectivity

### Debug Steps:

1. **Check backend logs** for payment processing
2. **Verify environment variables** are loaded
3. **Test with TEST_MODE** first
4. **Check PayCaps dashboard** for transaction logs

## 🚀 Production Deployment

### For Live Payments:

1. **Get Production Credentials**:
   - Switch from sandbox to production PayCaps account
   - Update credentials in environment variables

2. **Update Environment**:
   ```env
   NODE_ENV=production
   PAYCAPS_MODE=production
   PAYCAPS_APP_ID=production_app_id
   PAYCAPS_SECRET=production_secret
   ```

3. **Configure Webhooks**:
   - Set callback URL in PayCaps dashboard
   - Ensure HTTPS for production

## 📞 Support

- **PayCaps Documentation**: Check PayCaps developer docs
- **Technical Issues**: Check backend logs and console errors
- **Payment Testing**: Use provided test card details

---

**Note**: Always test thoroughly in sandbox mode before going live with real payments.

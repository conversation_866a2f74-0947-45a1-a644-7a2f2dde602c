/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandMailgun = createReactComponent("outline", "brand-mailgun", "IconBrandMailgun", [["path", { "d": "M17 12a2 2 0 1 0 4 0a9 9 0 1 0 -2.987 6.697", "key": "svg-0" }], ["path", { "d": "M12 12m-5 0a5 5 0 1 0 10 0a5 5 0 1 0 -10 0", "key": "svg-1" }], ["path", { "d": "M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-2" }], ["path", { "d": "M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-3" }]]);

export { IconBrandMailgun as default };
//# sourceMappingURL=IconBrandMailgun.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandStocktwits = createReactComponent("outline", "brand-stocktwits", "IconBrandStocktwits", [["path", { "d": "M16 3l-8 4.5l8 4.5", "key": "svg-0" }], ["path", { "d": "M8 12l8 4.5l-8 4.5", "key": "svg-1" }]]);

export { IconBrandStocktwits as default };
//# sourceMappingURL=IconBrandStocktwits.mjs.map

import globals from "globals";
import pluginJs from "@eslint/js";
import pluginReact from "eslint-plugin-react";

/** @type {import('eslint').Linter.Config[]} */
export default [
  {
    files: ["**/*.{js,mjs,cjs,jsx}"],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node, // Add Node.js globals like 'process'
      },
    },
    settings: {
      react: {
        version: "detect", // Automatically detect React version
      },
    },
  },
  pluginJs.configs.recommended,
  pluginReact.configs.flat.recommended,
  {
    rules: {
      "no-unused-vars": ["warn", { "args": "after-used", "ignoreRestSiblings": true }],
      "react/react-in-jsx-scope": "off", // Disable if using React 17+
      "react/no-unescaped-entities": "off", // Delete after build test
      "react/prop-types": "off", // Delete after build test
      "react/no-unknown-property": ["error", { ignore: ["jsx"] }], // Allow 'jsx' in <style jsx>
    },
  },
];

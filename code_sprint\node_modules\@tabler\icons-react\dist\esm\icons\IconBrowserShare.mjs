/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrowserShare = createReactComponent("outline", "browser-share", "IconBrowserShare", [["path", { "d": "M4 8h16", "key": "svg-0" }], ["path", { "d": "M12.5 20h-6.5a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v7", "key": "svg-1" }], ["path", { "d": "M8 4v4", "key": "svg-2" }], ["path", { "d": "M16 22l5 -5", "key": "svg-3" }], ["path", { "d": "M21 21.5v-4.5h-4.5", "key": "svg-4" }]]);

export { IconBrowserShare as default };
//# sourceMappingURL=IconBrowserShare.mjs.map

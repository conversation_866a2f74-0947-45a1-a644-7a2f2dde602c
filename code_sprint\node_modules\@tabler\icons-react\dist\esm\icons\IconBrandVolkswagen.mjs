/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandVolkswagen = createReactComponent("outline", "brand-volkswagen", "IconBrandVolkswagen", [["path", { "d": "M12 21a9 9 0 0 0 9 -9a9 9 0 0 0 -9 -9a9 9 0 0 0 -9 9a9 9 0 0 0 9 9z", "key": "svg-0" }], ["path", { "d": "M5 7l4.5 11l1.5 -5h2l1.5 5l4.5 -11", "key": "svg-1" }], ["path", { "d": "M9 4l2 6h2l2 -6", "key": "svg-2" }]]);

export { IconBrandVolkswagen as default };
//# sourceMappingURL=IconBrandVolkswagen.mjs.map

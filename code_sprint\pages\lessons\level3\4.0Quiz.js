import React, { useState } from "react";

const Questions = [
  {
    question: "What is Data Science?",
    options: [
      { text: "A field combining math, statistics, and computer science to analyze data", isCorrect: true },
      { text: "A method to build robots", isCorrect: false },
      { text: "A branch of biology", isCorrect: false },
      { text: "A way to collect surveys", isCorrect: false },
    ],
  },
  {
    question: "Which data type is used for whole numbers in Python?",
    options: [
      { text: "Float", isCorrect: false },
      { text: "Integer", isCorrect: true },
      { text: "String", isCorrect: false },
      { text: "Boolean", isCorrect: false },
    ],
  },
  {
    question: "What does the term 'mean' represent in data analysis?",
    options: [
      { text: "The middle value in a dataset", isCorrect: false },
      { text: "The average value of a dataset", isCorrect: true },
      { text: "The most frequent value in a dataset", isCorrect: false },
      { text: "The range of values in a dataset", isCorrect: false },
    ],
  },
  {
    question: "Which chart is best used to compare categories of data?",
    options: [
      { text: "Line chart", isCorrect: false },
      { text: "Pie chart", isCorrect: false },
      { text: "Bar graph", isCorrect: true },
      { text: "Scatter plot", isCorrect: false },
    ],
  },
  {
    question: "Why is cleaning data an essential step in data science?",
    options: [
      { text: "To remove incorrect or irrelevant data", isCorrect: true },
      { text: "To visualize data", isCorrect: false },
      { text: "To increase the size of the dataset", isCorrect: false },
      { text: "To make graphs", isCorrect: false },
    ],
  },
  {
    question: "What is the purpose of a scatter plot?",
    options: [
      { text: "To display relationships between two variables", isCorrect: true },
      { text: "To show trends over time", isCorrect: false },
      { text: "To compare parts of a whole", isCorrect: false },
      { text: "To highlight outliers", isCorrect: false },
    ],
  },
  {
    question: "What is a heatmap used for in data visualization?",
    options: [
      { text: "To show data density using colors", isCorrect: true },
      { text: "To display temperature trends", isCorrect: false },
      { text: "To track time series data", isCorrect: false },
      { text: "To organize rows and columns", isCorrect: false },
    ],
  },
  {
    question: "Which Python function is used to display data visually?",
    options: [
      { text: "matplotlib.pyplot.show()", isCorrect: true },
      { text: "print()", isCorrect: false },
      { text: "display()", isCorrect: false },
      { text: "visualize()", isCorrect: false },
    ],
  },
  {
    question: "Which of these is an advanced visualization technique?",
    options: [
      { text: "Bar graph", isCorrect: false },
      { text: "Scatter plot", isCorrect: false },
      { text: "Heatmap", isCorrect: true },
      { text: "Pie chart", isCorrect: false },
    ],
  },
  {
    question: "What is storytelling in data science?",
    options: [
      { text: "Presenting data in a way that is engaging and informative", isCorrect: true },
      { text: "Writing fiction based on datasets", isCorrect: false },
      { text: "Using only bar graphs to explain data", isCorrect: false },
      { text: "Analyzing the history of data collection", isCorrect: false },
    ],
  },
  {
    question: "What is Data Science?",
    options: [
      { text: "A field combining math, statistics, and computer science to analyze data", isCorrect: true },
      { text: "A method to build robots", isCorrect: false },
      { text: "A branch of biology", isCorrect: false },
      { text: "A way to collect surveys", isCorrect: false },
    ],
  },
  {
    question: "Which data type is used for whole numbers in Python?",
    options: [
      { text: "Float", isCorrect: false },
      { text: "Integer", isCorrect: true },
      { text: "String", isCorrect: false },
      { text: "Boolean", isCorrect: false },
    ],
  },
  {
    question: "What does the term 'mean' represent in data analysis?",
    options: [
      { text: "The middle value in a dataset", isCorrect: false },
      { text: "The average value of a dataset", isCorrect: true },
      { text: "The most frequent value in a dataset", isCorrect: false },
      { text: "The range of values in a dataset", isCorrect: false },
    ],
  },
  {
    question: "Which chart is best used to compare categories of data?",
    options: [
      { text: "Line chart", isCorrect: false },
      { text: "Pie chart", isCorrect: false },
      { text: "Bar graph", isCorrect: true },
      { text: "Scatter plot", isCorrect: false },
    ],
  },
  {
    question: "Why is cleaning data an essential step in data science?",
    options: [
      { text: "To remove incorrect or irrelevant data", isCorrect: true },
      { text: "To visualize data", isCorrect: false },
      { text: "To increase the size of the dataset", isCorrect: false },
      { text: "To make graphs", isCorrect: false },
    ],
  },
  {
    question: "What is the purpose of a scatter plot?",
    options: [
      { text: "To display relationships between two variables", isCorrect: true },
      { text: "To show trends over time", isCorrect: false },
      { text: "To compare parts of a whole", isCorrect: false },
      { text: "To highlight outliers", isCorrect: false },
    ],
  },
  {
    question: "What is a heatmap used for in data visualization?",
    options: [
      { text: "To show data density using colors", isCorrect: true },
      { text: "To display temperature trends", isCorrect: false },
      { text: "To track time series data", isCorrect: false },
      { text: "To organize rows and columns", isCorrect: false },
    ],
  },
  {
    question: "Which Python function is used to display data visually?",
    options: [
      { text: "matplotlib.pyplot.show()", isCorrect: true },
      { text: "print()", isCorrect: false },
      { text: "display()", isCorrect: false },
      { text: "visualize()", isCorrect: false },
    ],
  },
  {
    question: "Which of these is an advanced visualization technique?",
    options: [
      { text: "Bar graph", isCorrect: false },
      { text: "Scatter plot", isCorrect: false },
      { text: "Heatmap", isCorrect: true },
      { text: "Pie chart", isCorrect: false },
    ],
  },
  {
    question: "What is storytelling in data science?",
    options: [
      { text: "Presenting data in a way that is engaging and informative", isCorrect: true },
      { text: "Writing fiction based on datasets", isCorrect: false },
      { text: "Using only bar graphs to explain data", isCorrect: false },
      { text: "Analyzing the history of data collection", isCorrect: false },
    ],
  },
  // Additional Questions
  {
    question: "What is the median in a dataset?",
    options: [
      { text: "The average value", isCorrect: false },
      { text: "The middle value when data is sorted", isCorrect: true },
      { text: "The most frequent value", isCorrect: false },
      { text: "The smallest value", isCorrect: false },
    ],
  },
  {
    question: "Which programming language is widely used in data science?",
    options: [
      { text: "Python", isCorrect: true },
      { text: "C++", isCorrect: false },
      { text: "JavaScript", isCorrect: false },
      { text: "Ruby", isCorrect: false },
    ],
  },
  {
    question: "What does 'data cleaning' involve?",
    options: [
      { text: "Fixing or removing incorrect data", isCorrect: true },
      { text: "Creating new data", isCorrect: false },
      { text: "Deleting all old data", isCorrect: false },
      { text: "Organizing data alphabetically", isCorrect: false },
    ],
  },
  {
    question: "What is the purpose of a line chart?",
    options: [
      { text: "To show trends over time", isCorrect: true },
      { text: "To compare categories", isCorrect: false },
      { text: "To highlight relationships", isCorrect: false },
      { text: "To summarize numerical data", isCorrect: false },
    ],
  },
  {
    question: "Which library is commonly used in Python for data manipulation?",
    options: [
      { text: "Pandas", isCorrect: true },
      { text: "NumPy", isCorrect: false },
      { text: "Matplotlib", isCorrect: false },
      { text: "Scikit-learn", isCorrect: false },
    ],
  },
  {
    question: "What is a key advantage of using charts in data visualization?",
    options: [
      { text: "They look nice", isCorrect: false },
      { text: "They help simplify complex data", isCorrect: true },
      { text: "They add decoration to reports", isCorrect: false },
      { text: "They take up less space", isCorrect: false },
    ],
  },
  {
    question: "What is the mode of a dataset?",
    options: [
      { text: "The value that appears most frequently", isCorrect: true },
      { text: "The smallest value", isCorrect: false },
      { text: "The middle value", isCorrect: false },
      { text: "The largest value", isCorrect: false },
    ],
  },
  {
    question: "Which chart type is best for showing parts of a whole?",
    options: [
      { text: "Bar chart", isCorrect: false },
      { text: "Line chart", isCorrect: false },
      { text: "Pie chart", isCorrect: true },
      { text: "Scatter plot", isCorrect: false },
    ],
  },
  {
    question: "What is the role of statistics in data science?",
    options: [
      { text: "To build robots", isCorrect: false },
      { text: "To analyze and summarize data", isCorrect: true },
      { text: "To create animations", isCorrect: false },
      { text: "To store data", isCorrect: false },
    ],
  },
  {
    question: "Which tool is commonly used for creating dashboards?",
    options: [
      { text: "Tableau", isCorrect: true },
      { text: "Excel", isCorrect: false },
      { text: "Google Docs", isCorrect: false },
      { text: "Microsoft Word", isCorrect: false },
    ],
  },
  {
    question: "What is an outlier in a dataset?",
    options: [
      { text: "The smallest value", isCorrect: false },
      { text: "A value that is far from others", isCorrect: true },
      { text: "The most frequent value", isCorrect: false },
      { text: "The middle value", isCorrect: false },
    ],
  },
  {
    question: "What is the role of machine learning in data science?",
    options: [
      { text: "To clean data", isCorrect: false },
      { text: "To make predictions based on data", isCorrect: true },
      { text: "To sort data", isCorrect: false },
      { text: "To visualize data", isCorrect: false },
    ],
  },
  {
    question: "Which data type is used for true/false values?",
    options: [
      { text: "Boolean", isCorrect: true },
      { text: "Integer", isCorrect: false },
      { text: "Float", isCorrect: false },
      { text: "String", isCorrect: false },
    ],
  },
  {
    question: "Which visualization tool is known for its interactivity?",
    options: [
      { text: "D3.js", isCorrect: true },
      { text: "Excel", isCorrect: false },
      { text: "PowerPoint", isCorrect: false },
      { text: "Word", isCorrect: false },
    ],
  },
  {
    question: "What is predictive modeling?",
    options: [
      { text: "Predicting future data trends", isCorrect: true },
      { text: "Cleaning raw data", isCorrect: false },
      { text: "Summarizing data", isCorrect: false },
      { text: "Organizing data", isCorrect: false },
    ],
  },
  {
    question: "Which library in Python is used for statistical modeling?",
    options: [
        { text: "Statsmodels", isCorrect: true },
        { text: "Matplotlib", isCorrect: false },
        { text: "Seaborn", isCorrect: false },
        { text: "Requests", isCorrect: false },
      ],
    },
    {
      question: "What is data wrangling?",
      options: [
        { text: "Transforming and preparing raw data for analysis", isCorrect: true },
        { text: "Cleaning the environment for coding", isCorrect: false },
        { text: "Building charts", isCorrect: false },
        { text: "Finding outliers in data", isCorrect: false },
      ],
    },
    {
      question: "What does 'big data' refer to?",
      options: [
        { text: "Extremely large datasets that are difficult to process", isCorrect: true },
        { text: "Data collected from tall buildings", isCorrect: false },
        { text: "Data that is more than 1GB in size", isCorrect: false },
        { text: "Data stored in files", isCorrect: false },
      ],
    },
    {
      question: "What is a dashboard in data science?",
      options: [
        { text: "A tool for visualizing key metrics and trends", isCorrect: true },
        { text: "A place to store data", isCorrect: false },
        { text: "A report in Excel", isCorrect: false },
        { text: "A way to clean data", isCorrect: false },
      ],
    },
    {
      question: "What does CSV stand for?",
      options: [
        { text: "Comma Separated Values", isCorrect: true },
        { text: "Computer Storage Values", isCorrect: false },
        { text: "Charting Statistical Values", isCorrect: false },
        { text: "Complex Stored Variables", isCorrect: false },
      ],
    },
    {
      question: "Which tool is commonly used for processing big data?",
      options: [
        { text: "Hadoop", isCorrect: true },
        { text: "Excel", isCorrect: false },
        { text: "PowerPoint", isCorrect: false },
        { text: "Word", isCorrect: false },
      ],
    },
    {
      question: "What is a key feature of structured data?",
      options: [
        { text: "It is organized in rows and columns", isCorrect: true },
        { text: "It has no defined format", isCorrect: false },
        { text: "It is always text data", isCorrect: false },
        { text: "It cannot be analyzed", isCorrect: false },
      ],
    },
    {
      question: "What is unstructured data?",
      options: [
        { text: "Data that doesn’t have a predefined format", isCorrect: true },
        { text: "Data in rows and columns", isCorrect: false },
        { text: "Data that cannot be stored", isCorrect: false },
        { text: "Data only in numbers", isCorrect: false },
      ],
    },  
];

export default function QuizApp() {
    const [currentQuestion, setCurrentQuestion] = useState(0);
    const [score, setScore] = useState(0);
    const [selectedOption, setSelectedOption] = useState(null);
    const [showFeedback, setShowFeedback] = useState(false);
  
    const handleOptionClick = (option) => {
      if (showFeedback) return;
  
      setSelectedOption(option);
      setShowFeedback(true);
  
      if (option.isCorrect) {
        setScore(score + 1);
      }
  
      setTimeout(() => {
        setShowFeedback(false);
        setSelectedOption(null);
        setCurrentQuestion((prev) => prev + 1);
      }, 2000);
    };
  
    const current = Questions[currentQuestion];
  
    if (!current) {
      return (
        <div className="text-center p-10 bg-gradient-to-b from-green-400 to-blue-500 text-white min-h-screen">
          <h1 className="text-4xl font-bold mb-4">Quiz Completed!</h1>
          <p className="text-xl">Your final score: {score}/{Questions.length}</p>
        </div>
      );
    }
  
    return (
      <div className="flex flex-col items-center justify-center bg-gradient-to-b from-green-400 to-blue-500 text-white min-h-screen p-6">
        <div className="bg-white text-black rounded-lg shadow-lg p-8 max-w-2xl w-full">
          <h1 className="text-2xl font-bold mb-6">
            Question {currentQuestion + 1}/{Questions.length}
          </h1>
          <p className="mb-6">{current.question}</p>
          <ul className="list-none space-y-4">
            {current.options.map((option, index) => {
              const bgColor = showFeedback
                ? option.isCorrect
                  ? "bg-green-500"
                  : selectedOption === option
                  ? "bg-red-500"
                  : "bg-gray-200"
                : "bg-gray-200";
              return (
                <li
                  key={index}
                  onClick={() => handleOptionClick(option)}
                  className={`${bgColor} text-black p-4 rounded-lg cursor-pointer`}
                >
                  {option.text}
                </li>
              );
            })}
          </ul>
          <p className="mt-4">
            <strong>Score:</strong> {score}
          </p>
        </div>
      </div>
    );
  }
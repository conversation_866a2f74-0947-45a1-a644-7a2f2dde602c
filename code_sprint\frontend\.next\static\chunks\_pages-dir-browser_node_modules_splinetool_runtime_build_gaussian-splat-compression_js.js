"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_gaussian-splat-compression_js"],{

/***/ "(pages-dir-browser)/../node_modules/@splinetool/runtime/build/gaussian-splat-compression.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/@splinetool/runtime/build/gaussian-splat-compression.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompressedGaussianSplats: () => (/* binding */ X),\n/* harmony export */   GSplineBuffer: () => (/* binding */ ar),\n/* harmony export */   GaussianPLYData: () => (/* binding */ U)\n/* harmony export */ });\nvar Re=Object.create;var fr=Object.defineProperty;var Be=Object.getOwnPropertyDescriptor;var Ue=Object.getOwnPropertyNames;var Ye=Object.getPrototypeOf,Ge=Object.prototype.hasOwnProperty;var Qe=(r,e,n)=>e in r?fr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[e]=n;var K=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),ur=(r,e)=>{for(var n in e)fr(r,n,{get:e[n],enumerable:!0})},$e=(r,e,n,a)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let t of Ue(e))!Ge.call(r,t)&&t!==n&&fr(r,t,{get:()=>e[t],enumerable:!(a=Be(e,t))||a.enumerable});return r};var Q=(r,e,n)=>(n=r!=null?Re(Ye(r)):{},$e(e||!r||!r.__esModule?fr(n,\"default\",{value:r,enumerable:!0}):n,r));var $=(r,e,n)=>(Qe(r,typeof e!=\"symbol\"?e+\"\":e,n),n);var Lr=K((gt,Cr)=>{\"use strict\";function He(r){for(var e=new Array(r),n=0;n<r;++n)e[n]=n;return e}Cr.exports=He});var Tr=K((Mt,Er)=>{Er.exports=function(r){return r!=null&&(Dr(r)||We(r)||!!r._isBuffer)};function Dr(r){return!!r.constructor&&typeof r.constructor.isBuffer==\"function\"&&r.constructor.isBuffer(r)}function We(r){return typeof r.readFloatLE==\"function\"&&typeof r.slice==\"function\"&&Dr(r.slice(0,0))}});var tr=K((_t,Pr)=>{var Ze=Lr(),Xe=Tr(),Ke=typeof Float64Array<\"u\";function Je(r,e){return r[0]-e[0]}function rn(){var r=this.stride,e=new Array(r.length),n;for(n=0;n<e.length;++n)e[n]=[Math.abs(r[n]),n];e.sort(Je);var a=new Array(e.length);for(n=0;n<a.length;++n)a[n]=e[n][1];return a}function en(r,e){var n=[\"View\",e,\"d\",r].join(\"\");e<0&&(n=\"View_Nil\"+r);var a=r===\"generic\";if(e===-1){var t=\"function \"+n+\"(a){this.data=a;};var proto=\"+n+\".prototype;proto.dtype='\"+r+\"';proto.index=function(){return -1};proto.size=0;proto.dimension=-1;proto.shape=proto.stride=proto.order=[];proto.lo=proto.hi=proto.transpose=proto.step=function(){return new \"+n+\"(this.data);};proto.get=proto.set=function(){};proto.pick=function(){return null};return function construct_\"+n+\"(a){return new \"+n+\"(a);}\",d=new Function(t);return d()}else if(e===0){var t=\"function \"+n+\"(a,d) {this.data = a;this.offset = d};var proto=\"+n+\".prototype;proto.dtype='\"+r+\"';proto.index=function(){return this.offset};proto.dimension=0;proto.size=1;proto.shape=proto.stride=proto.order=[];proto.lo=proto.hi=proto.transpose=proto.step=function \"+n+\"_copy() {return new \"+n+\"(this.data,this.offset)};proto.pick=function \"+n+\"_pick(){return TrivialArray(this.data);};proto.valueOf=proto.get=function \"+n+\"_get(){return \"+(a?\"this.data.get(this.offset)\":\"this.data[this.offset]\")+\"};proto.set=function \"+n+\"_set(v){return \"+(a?\"this.data.set(this.offset,v)\":\"this.data[this.offset]=v\")+\"};return function construct_\"+n+\"(a,b,c,d){return new \"+n+\"(a,d)}\",d=new Function(\"TrivialArray\",t);return d(yr[r][0])}var t=[\"'use strict'\"],s=Ze(e),i=s.map(function(y){return\"i\"+y}),h=\"this.offset+\"+s.map(function(y){return\"this.stride[\"+y+\"]*i\"+y}).join(\"+\"),o=s.map(function(y){return\"b\"+y}).join(\",\"),c=s.map(function(y){return\"c\"+y}).join(\",\");t.push(\"function \"+n+\"(a,\"+o+\",\"+c+\",d){this.data=a\",\"this.shape=[\"+o+\"]\",\"this.stride=[\"+c+\"]\",\"this.offset=d|0}\",\"var proto=\"+n+\".prototype\",\"proto.dtype='\"+r+\"'\",\"proto.dimension=\"+e),t.push(\"Object.defineProperty(proto,'size',{get:function \"+n+\"_size(){return \"+s.map(function(y){return\"this.shape[\"+y+\"]\"}).join(\"*\"),\"}})\"),e===1?t.push(\"proto.order=[0]\"):(t.push(\"Object.defineProperty(proto,'order',{get:\"),e<4?(t.push(\"function \"+n+\"_order(){\"),e===2?t.push(\"return (Math.abs(this.stride[0])>Math.abs(this.stride[1]))?[1,0]:[0,1]}})\"):e===3&&t.push(\"var s0=Math.abs(this.stride[0]),s1=Math.abs(this.stride[1]),s2=Math.abs(this.stride[2]);if(s0>s1){if(s1>s2){return [2,1,0];}else if(s0>s2){return [1,2,0];}else{return [1,0,2];}}else if(s0>s2){return [2,0,1];}else if(s2>s1){return [0,1,2];}else{return [0,2,1];}}})\")):t.push(\"ORDER})\")),t.push(\"proto.set=function \"+n+\"_set(\"+i.join(\",\")+\",v){\"),a?t.push(\"return this.data.set(\"+h+\",v)}\"):t.push(\"return this.data[\"+h+\"]=v}\"),t.push(\"proto.get=function \"+n+\"_get(\"+i.join(\",\")+\"){\"),a?t.push(\"return this.data.get(\"+h+\")}\"):t.push(\"return this.data[\"+h+\"]}\"),t.push(\"proto.index=function \"+n+\"_index(\",i.join(),\"){return \"+h+\"}\"),t.push(\"proto.hi=function \"+n+\"_hi(\"+i.join(\",\")+\"){return new \"+n+\"(this.data,\"+s.map(function(y){return[\"(typeof i\",y,\"!=='number'||i\",y,\"<0)?this.shape[\",y,\"]:i\",y,\"|0\"].join(\"\")}).join(\",\")+\",\"+s.map(function(y){return\"this.stride[\"+y+\"]\"}).join(\",\")+\",this.offset)}\");var l=s.map(function(y){return\"a\"+y+\"=this.shape[\"+y+\"]\"}),f=s.map(function(y){return\"c\"+y+\"=this.stride[\"+y+\"]\"});t.push(\"proto.lo=function \"+n+\"_lo(\"+i.join(\",\")+\"){var b=this.offset,d=0,\"+l.join(\",\")+\",\"+f.join(\",\"));for(var p=0;p<e;++p)t.push(\"if(typeof i\"+p+\"==='number'&&i\"+p+\">=0){d=i\"+p+\"|0;b+=c\"+p+\"*d;a\"+p+\"-=d}\");t.push(\"return new \"+n+\"(this.data,\"+s.map(function(y){return\"a\"+y}).join(\",\")+\",\"+s.map(function(y){return\"c\"+y}).join(\",\")+\",b)}\"),t.push(\"proto.step=function \"+n+\"_step(\"+i.join(\",\")+\"){var \"+s.map(function(y){return\"a\"+y+\"=this.shape[\"+y+\"]\"}).join(\",\")+\",\"+s.map(function(y){return\"b\"+y+\"=this.stride[\"+y+\"]\"}).join(\",\")+\",c=this.offset,d=0,ceil=Math.ceil\");for(var p=0;p<e;++p)t.push(\"if(typeof i\"+p+\"==='number'){d=i\"+p+\"|0;if(d<0){c+=b\"+p+\"*(a\"+p+\"-1);a\"+p+\"=ceil(-a\"+p+\"/d)}else{a\"+p+\"=ceil(a\"+p+\"/d)}b\"+p+\"*=d}\");t.push(\"return new \"+n+\"(this.data,\"+s.map(function(y){return\"a\"+y}).join(\",\")+\",\"+s.map(function(y){return\"b\"+y}).join(\",\")+\",c)}\");for(var v=new Array(e),m=new Array(e),p=0;p<e;++p)v[p]=\"a[i\"+p+\"]\",m[p]=\"b[i\"+p+\"]\";t.push(\"proto.transpose=function \"+n+\"_transpose(\"+i+\"){\"+i.map(function(y,u){return y+\"=(\"+y+\"===undefined?\"+u+\":\"+y+\"|0)\"}).join(\";\"),\"var a=this.shape,b=this.stride;return new \"+n+\"(this.data,\"+v.join(\",\")+\",\"+m.join(\",\")+\",this.offset)}\"),t.push(\"proto.pick=function \"+n+\"_pick(\"+i+\"){var a=[],b=[],c=this.offset\");for(var p=0;p<e;++p)t.push(\"if(typeof i\"+p+\"==='number'&&i\"+p+\">=0){c=(c+this.stride[\"+p+\"]*i\"+p+\")|0}else{a.push(this.shape[\"+p+\"]);b.push(this.stride[\"+p+\"])}\");t.push(\"var ctor=CTOR_LIST[a.length+1];return ctor(this.data,a,b,c)}\"),t.push(\"return function construct_\"+n+\"(data,shape,stride,offset){return new \"+n+\"(data,\"+s.map(function(y){return\"shape[\"+y+\"]\"}).join(\",\")+\",\"+s.map(function(y){return\"stride[\"+y+\"]\"}).join(\",\")+\",offset)}\");var d=new Function(\"CTOR_LIST\",\"ORDER\",t.join(`\n`));return d(yr[r],rn)}function nn(r){if(Xe(r))return\"buffer\";if(Ke)switch(Object.prototype.toString.call(r)){case\"[object Float64Array]\":return\"float64\";case\"[object Float32Array]\":return\"float32\";case\"[object Int8Array]\":return\"int8\";case\"[object Int16Array]\":return\"int16\";case\"[object Int32Array]\":return\"int32\";case\"[object Uint8Array]\":return\"uint8\";case\"[object Uint16Array]\":return\"uint16\";case\"[object Uint32Array]\":return\"uint32\";case\"[object Uint8ClampedArray]\":return\"uint8_clamped\";case\"[object BigInt64Array]\":return\"bigint64\";case\"[object BigUint64Array]\":return\"biguint64\"}return Array.isArray(r)?\"array\":\"generic\"}var yr={float32:[],float64:[],int8:[],int16:[],int32:[],uint8:[],uint16:[],uint32:[],array:[],uint8_clamped:[],bigint64:[],biguint64:[],buffer:[],generic:[]};function an(r,e,n,a){if(r===void 0){var c=yr.array[0];return c([])}else typeof r==\"number\"&&(r=[r]);e===void 0&&(e=[r.length]);var t=e.length;if(n===void 0){n=new Array(t);for(var s=t-1,i=1;s>=0;--s)n[s]=i,i*=e[s]}if(a===void 0){a=0;for(var s=0;s<t;++s)n[s]<0&&(a-=(e[s]-1)*n[s])}for(var h=nn(r),o=yr[h];o.length<=t+1;)o.push(en(h,o.length-1));var c=o[t+1];return c(r,e,n,a)}Pr.exports=an});var Or=K((bt,Vr)=>{\"use strict\";function tn(r,e){for(var n=1,a=r.length,t=r[0],s=r[0],i=1;i<a;++i)if(s=t,t=r[i],e(t,s)){if(i===n){n++;continue}r[n++]=t}return r.length=n,r}function sn(r){for(var e=1,n=r.length,a=r[0],t=r[0],s=1;s<n;++s,t=a)if(t=a,a=r[s],a!==t){if(s===e){e++;continue}r[e++]=a}return r.length=e,r}function on(r,e,n){return r.length===0?r:e?(n||r.sort(e),tn(r,e)):(n||r.sort(),sn(r))}Vr.exports=on});var Br=K((At,Rr)=>{\"use strict\";var hn=Or();function jr(r,e,n){var a=r.length,t=e.arrayArgs.length,s=e.indexArgs.length>0,i=[],h=[],o=0,c=0,l,f;for(l=0;l<a;++l)h.push([\"i\",l,\"=0\"].join(\"\"));for(f=0;f<t;++f)for(l=0;l<a;++l)c=o,o=r[l],l===0?h.push([\"d\",f,\"s\",l,\"=t\",f,\"p\",o].join(\"\")):h.push([\"d\",f,\"s\",l,\"=(t\",f,\"p\",o,\"-s\",c,\"*t\",f,\"p\",c,\")\"].join(\"\"));for(h.length>0&&i.push(\"var \"+h.join(\",\")),l=a-1;l>=0;--l)o=r[l],i.push([\"for(i\",l,\"=0;i\",l,\"<s\",o,\";++i\",l,\"){\"].join(\"\"));for(i.push(n),l=0;l<a;++l){for(c=o,o=r[l],f=0;f<t;++f)i.push([\"p\",f,\"+=d\",f,\"s\",l].join(\"\"));s&&(l>0&&i.push([\"index[\",c,\"]-=s\",c].join(\"\")),i.push([\"++index[\",o,\"]\"].join(\"\"))),i.push(\"}\")}return i.join(`\n`)}function cn(r,e,n,a){for(var t=e.length,s=n.arrayArgs.length,i=n.blockSize,h=n.indexArgs.length>0,o=[],c=0;c<s;++c)o.push([\"var offset\",c,\"=p\",c].join(\"\"));for(var c=r;c<t;++c)o.push([\"for(var j\"+c+\"=SS[\",e[c],\"]|0;j\",c,\">0;){\"].join(\"\")),o.push([\"if(j\",c,\"<\",i,\"){\"].join(\"\")),o.push([\"s\",e[c],\"=j\",c].join(\"\")),o.push([\"j\",c,\"=0\"].join(\"\")),o.push([\"}else{s\",e[c],\"=\",i].join(\"\")),o.push([\"j\",c,\"-=\",i,\"}\"].join(\"\")),h&&o.push([\"index[\",e[c],\"]=j\",c].join(\"\"));for(var c=0;c<s;++c){for(var l=[\"offset\"+c],f=r;f<t;++f)l.push([\"j\",f,\"*t\",c,\"p\",e[f]].join(\"\"));o.push([\"p\",c,\"=(\",l.join(\"+\"),\")\"].join(\"\"))}o.push(jr(e,n,a));for(var c=r;c<t;++c)o.push(\"}\");return o.join(`\n`)}function ln(r){for(var e=0,n=r[0].length;e<n;){for(var a=1;a<r.length;++a)if(r[a][e]!==r[0][e])return e;++e}return e}function xr(r,e,n){for(var a=r.body,t=[],s=[],i=0;i<r.args.length;++i){var h=r.args[i];if(!(h.count<=0)){var o=new RegExp(h.name,\"g\"),c=\"\",l=e.arrayArgs.indexOf(i);switch(e.argTypes[i]){case\"offset\":var f=e.offsetArgIndex.indexOf(i),p=e.offsetArgs[f];l=p.array,c=\"+q\"+f;case\"array\":c=\"p\"+l+c;var v=\"l\"+i,m=\"a\"+l;if(e.arrayBlockIndices[l]===0)h.count===1?n[l]===\"generic\"?h.lvalue?(t.push([\"var \",v,\"=\",m,\".get(\",c,\")\"].join(\"\")),a=a.replace(o,v),s.push([m,\".set(\",c,\",\",v,\")\"].join(\"\"))):a=a.replace(o,[m,\".get(\",c,\")\"].join(\"\")):a=a.replace(o,[m,\"[\",c,\"]\"].join(\"\")):n[l]===\"generic\"?(t.push([\"var \",v,\"=\",m,\".get(\",c,\")\"].join(\"\")),a=a.replace(o,v),h.lvalue&&s.push([m,\".set(\",c,\",\",v,\")\"].join(\"\"))):(t.push([\"var \",v,\"=\",m,\"[\",c,\"]\"].join(\"\")),a=a.replace(o,v),h.lvalue&&s.push([m,\"[\",c,\"]=\",v].join(\"\")));else{for(var d=[h.name],y=[c],u=0;u<Math.abs(e.arrayBlockIndices[l]);u++)d.push(\"\\\\s*\\\\[([^\\\\]]+)\\\\]\"),y.push(\"$\"+(u+1)+\"*t\"+l+\"b\"+u);if(o=new RegExp(d.join(\"\"),\"g\"),c=y.join(\"+\"),n[l]===\"generic\")throw new Error(\"cwise: Generic arrays not supported in combination with blocks!\");a=a.replace(o,[m,\"[\",c,\"]\"].join(\"\"))}break;case\"scalar\":a=a.replace(o,\"Y\"+e.scalarArgs.indexOf(i));break;case\"index\":a=a.replace(o,\"index\");break;case\"shape\":a=a.replace(o,\"shape\");break}}}return[t.join(`\n`),a,s.join(`\n`)].join(`\n`).trim()}function pn(r){for(var e=new Array(r.length),n=!0,a=0;a<r.length;++a){var t=r[a],s=t.match(/\\d+/);s?s=s[0]:s=\"\",t.charAt(0)===0?e[a]=\"u\"+t.charAt(1)+s:e[a]=t.charAt(0)+s,a>0&&(n=n&&e[a]===e[a-1])}return n?e[0]:e.join(\"\")}function fn(r,e){for(var n=e[1].length-Math.abs(r.arrayBlockIndices[0])|0,a=new Array(r.arrayArgs.length),t=new Array(r.arrayArgs.length),s=0;s<r.arrayArgs.length;++s)t[s]=e[2*s],a[s]=e[2*s+1];for(var i=[],h=[],o=[],c=[],l=[],s=0;s<r.arrayArgs.length;++s){r.arrayBlockIndices[s]<0?(o.push(0),c.push(n),i.push(n),h.push(n+r.arrayBlockIndices[s])):(o.push(r.arrayBlockIndices[s]),c.push(r.arrayBlockIndices[s]+n),i.push(0),h.push(r.arrayBlockIndices[s]));for(var f=[],p=0;p<a[s].length;p++)o[s]<=a[s][p]&&a[s][p]<c[s]&&f.push(a[s][p]-o[s]);l.push(f)}for(var v=[\"SS\"],m=[\"'use strict'\"],d=[],p=0;p<n;++p)d.push([\"s\",p,\"=SS[\",p,\"]\"].join(\"\"));for(var s=0;s<r.arrayArgs.length;++s){v.push(\"a\"+s),v.push(\"t\"+s),v.push(\"p\"+s);for(var p=0;p<n;++p)d.push([\"t\",s,\"p\",p,\"=t\",s,\"[\",o[s]+p,\"]\"].join(\"\"));for(var p=0;p<Math.abs(r.arrayBlockIndices[s]);++p)d.push([\"t\",s,\"b\",p,\"=t\",s,\"[\",i[s]+p,\"]\"].join(\"\"))}for(var s=0;s<r.scalarArgs.length;++s)v.push(\"Y\"+s);if(r.shapeArgs.length>0&&d.push(\"shape=SS.slice(0)\"),r.indexArgs.length>0){for(var y=new Array(n),s=0;s<n;++s)y[s]=\"0\";d.push([\"index=[\",y.join(\",\"),\"]\"].join(\"\"))}for(var s=0;s<r.offsetArgs.length;++s){for(var u=r.offsetArgs[s],x=[],p=0;p<u.offset.length;++p)u.offset[p]!==0&&(u.offset[p]===1?x.push([\"t\",u.array,\"p\",p].join(\"\")):x.push([u.offset[p],\"*t\",u.array,\"p\",p].join(\"\")));x.length===0?d.push(\"q\"+s+\"=0\"):d.push([\"q\",s,\"=\",x.join(\"+\")].join(\"\"))}var M=hn([].concat(r.pre.thisVars).concat(r.body.thisVars).concat(r.post.thisVars));d=d.concat(M),d.length>0&&m.push(\"var \"+d.join(\",\"));for(var s=0;s<r.arrayArgs.length;++s)m.push(\"p\"+s+\"|=0\");r.pre.body.length>3&&m.push(xr(r.pre,r,t));var b=xr(r.body,r,t),_=ln(l);_<n?m.push(cn(_,l[0],r,b)):m.push(jr(l[0],r,b)),r.post.body.length>3&&m.push(xr(r.post,r,t)),r.debug&&console.log(\"-----Generated cwise routine for \",e,`:\n`+m.join(`\n`)+`\n----------`);var g=[r.funcName||\"unnamed\",\"_cwise_loop_\",a[0].join(\"s\"),\"m\",_,pn(t)].join(\"\"),N=new Function([\"function \",g,\"(\",v.join(\",\"),\"){\",m.join(`\n`),\"} return \",g].join(\"\"));return N()}Rr.exports=fn});var Yr=K((wt,Ur)=>{\"use strict\";var yn=Br();function dn(r){var e=[\"'use strict'\",\"var CACHED={}\"],n=[],a=r.funcName+\"_cwise_thunk\";e.push([\"return function \",a,\"(\",r.shimArgs.join(\",\"),\"){\"].join(\"\"));for(var t=[],s=[],i=[[\"array\",r.arrayArgs[0],\".shape.slice(\",Math.max(0,r.arrayBlockIndices[0]),r.arrayBlockIndices[0]<0?\",\"+r.arrayBlockIndices[0]+\")\":\")\"].join(\"\")],h=[],o=[],c=0;c<r.arrayArgs.length;++c){var l=r.arrayArgs[c];n.push([\"t\",l,\"=array\",l,\".dtype,\",\"r\",l,\"=array\",l,\".order\"].join(\"\")),t.push(\"t\"+l),t.push(\"r\"+l),s.push(\"t\"+l),s.push(\"r\"+l+\".join()\"),i.push(\"array\"+l+\".data\"),i.push(\"array\"+l+\".stride\"),i.push(\"array\"+l+\".offset|0\"),c>0&&(h.push(\"array\"+r.arrayArgs[0]+\".shape.length===array\"+l+\".shape.length+\"+(Math.abs(r.arrayBlockIndices[0])-Math.abs(r.arrayBlockIndices[c]))),o.push(\"array\"+r.arrayArgs[0]+\".shape[shapeIndex+\"+Math.max(0,r.arrayBlockIndices[0])+\"]===array\"+l+\".shape[shapeIndex+\"+Math.max(0,r.arrayBlockIndices[c])+\"]\"))}r.arrayArgs.length>1&&(e.push(\"if (!(\"+h.join(\" && \")+\")) throw new Error('cwise: Arrays do not all have the same dimensionality!')\"),e.push(\"for(var shapeIndex=array\"+r.arrayArgs[0]+\".shape.length-\"+Math.abs(r.arrayBlockIndices[0])+\"; shapeIndex-->0;) {\"),e.push(\"if (!(\"+o.join(\" && \")+\")) throw new Error('cwise: Arrays do not all have the same shape!')\"),e.push(\"}\"));for(var c=0;c<r.scalarArgs.length;++c)i.push(\"scalar\"+r.scalarArgs[c]);n.push([\"type=[\",s.join(\",\"),\"].join()\"].join(\"\")),n.push(\"proc=CACHED[type]\"),e.push(\"var \"+n.join(\",\")),e.push([\"if(!proc){\",\"CACHED[type]=proc=compile([\",t.join(\",\"),\"])}\",\"return proc(\",i.join(\",\"),\")}\"].join(\"\")),r.debug&&console.log(`-----Generated thunk:\n`+e.join(`\n`)+`\n----------`);var f=new Function(\"compile\",e.join(`\n`));return f(yn.bind(void 0,r))}Ur.exports=dn});var Qr=K((kt,Gr)=>{\"use strict\";var vn=Yr();function mn(){this.argTypes=[],this.shimArgs=[],this.arrayArgs=[],this.arrayBlockIndices=[],this.scalarArgs=[],this.offsetArgs=[],this.offsetArgIndex=[],this.indexArgs=[],this.shapeArgs=[],this.funcName=\"\",this.pre=null,this.body=null,this.post=null,this.debug=!1}function un(r){var e=new mn;e.pre=r.pre,e.body=r.body,e.post=r.post;var n=r.args.slice(0);e.argTypes=n;for(var a=0;a<n.length;++a){var t=n[a];if(t===\"array\"||typeof t==\"object\"&&t.blockIndices){if(e.argTypes[a]=\"array\",e.arrayArgs.push(a),e.arrayBlockIndices.push(t.blockIndices?t.blockIndices:0),e.shimArgs.push(\"array\"+a),a<e.pre.args.length&&e.pre.args[a].count>0)throw new Error(\"cwise: pre() block may not reference array args\");if(a<e.post.args.length&&e.post.args[a].count>0)throw new Error(\"cwise: post() block may not reference array args\")}else if(t===\"scalar\")e.scalarArgs.push(a),e.shimArgs.push(\"scalar\"+a);else if(t===\"index\"){if(e.indexArgs.push(a),a<e.pre.args.length&&e.pre.args[a].count>0)throw new Error(\"cwise: pre() block may not reference array index\");if(a<e.body.args.length&&e.body.args[a].lvalue)throw new Error(\"cwise: body() block may not write to array index\");if(a<e.post.args.length&&e.post.args[a].count>0)throw new Error(\"cwise: post() block may not reference array index\")}else if(t===\"shape\"){if(e.shapeArgs.push(a),a<e.pre.args.length&&e.pre.args[a].lvalue)throw new Error(\"cwise: pre() block may not write to array shape\");if(a<e.body.args.length&&e.body.args[a].lvalue)throw new Error(\"cwise: body() block may not write to array shape\");if(a<e.post.args.length&&e.post.args[a].lvalue)throw new Error(\"cwise: post() block may not write to array shape\")}else if(typeof t==\"object\"&&t.offset)e.argTypes[a]=\"offset\",e.offsetArgs.push({array:t.array,offset:t.offset}),e.offsetArgIndex.push(a);else throw new Error(\"cwise: Unknown argument type \"+n[a])}if(e.arrayArgs.length<=0)throw new Error(\"cwise: No array arguments specified\");if(e.pre.args.length>n.length)throw new Error(\"cwise: Too many arguments in pre() block\");if(e.body.args.length>n.length)throw new Error(\"cwise: Too many arguments in body() block\");if(e.post.args.length>n.length)throw new Error(\"cwise: Too many arguments in post() block\");return e.debug=!!r.printCode||!!r.debug,e.funcName=r.funcName||\"cwise\",e.blockSize=r.blockSize||64,vn(e)}Gr.exports=un});var sr=K(I=>{\"use strict\";var B=Qr(),dr={body:\"\",args:[],thisVars:[],localVars:[]};function gr(r){if(!r)return dr;for(var e=0;e<r.args.length;++e){var n=r.args[e];e===0?r.args[e]={name:n,lvalue:!0,rvalue:!!r.rvalue,count:r.count||1}:r.args[e]={name:n,lvalue:!1,rvalue:!0,count:1}}return r.thisVars||(r.thisVars=[]),r.localVars||(r.localVars=[]),r}function xn(r){return B({args:r.args,pre:gr(r.pre),body:gr(r.body),post:gr(r.proc),funcName:r.funcName})}function O(r){for(var e=[],n=0;n<r.args.length;++n)e.push(\"a\"+n);var a=new Function(\"P\",[\"return function \",r.funcName,\"_ndarrayops(\",e.join(\",\"),\") {P(\",e.join(\",\"),\");return a0}\"].join(\"\"));return a(xn(r))}var $r={add:\"+\",sub:\"-\",mul:\"*\",div:\"/\",mod:\"%\",band:\"&\",bor:\"|\",bxor:\"^\",lshift:\"<<\",rshift:\">>\",rrshift:\">>>\"};(function(){for(var r in $r){var e=$r[r];I[r]=O({args:[\"array\",\"array\",\"array\"],body:{args:[\"a\",\"b\",\"c\"],body:\"a=b\"+e+\"c\"},funcName:r}),I[r+\"eq\"]=O({args:[\"array\",\"array\"],body:{args:[\"a\",\"b\"],body:\"a\"+e+\"=b\"},rvalue:!0,funcName:r+\"eq\"}),I[r+\"s\"]=O({args:[\"array\",\"array\",\"scalar\"],body:{args:[\"a\",\"b\",\"s\"],body:\"a=b\"+e+\"s\"},funcName:r+\"s\"}),I[r+\"seq\"]=O({args:[\"array\",\"scalar\"],body:{args:[\"a\",\"s\"],body:\"a\"+e+\"=s\"},rvalue:!0,funcName:r+\"seq\"})}})();var Hr={not:\"!\",bnot:\"~\",neg:\"-\",recip:\"1.0/\"};(function(){for(var r in Hr){var e=Hr[r];I[r]=O({args:[\"array\",\"array\"],body:{args:[\"a\",\"b\"],body:\"a=\"+e+\"b\"},funcName:r}),I[r+\"eq\"]=O({args:[\"array\"],body:{args:[\"a\"],body:\"a=\"+e+\"a\"},rvalue:!0,count:2,funcName:r+\"eq\"})}})();var Wr={and:\"&&\",or:\"||\",eq:\"===\",neq:\"!==\",lt:\"<\",gt:\">\",leq:\"<=\",geq:\">=\"};(function(){for(var r in Wr){var e=Wr[r];I[r]=O({args:[\"array\",\"array\",\"array\"],body:{args:[\"a\",\"b\",\"c\"],body:\"a=b\"+e+\"c\"},funcName:r}),I[r+\"s\"]=O({args:[\"array\",\"array\",\"scalar\"],body:{args:[\"a\",\"b\",\"s\"],body:\"a=b\"+e+\"s\"},funcName:r+\"s\"}),I[r+\"eq\"]=O({args:[\"array\",\"array\"],body:{args:[\"a\",\"b\"],body:\"a=a\"+e+\"b\"},rvalue:!0,count:2,funcName:r+\"eq\"}),I[r+\"seq\"]=O({args:[\"array\",\"scalar\"],body:{args:[\"a\",\"s\"],body:\"a=a\"+e+\"s\"},rvalue:!0,count:2,funcName:r+\"seq\"})}})();var Zr=[\"abs\",\"acos\",\"asin\",\"atan\",\"ceil\",\"cos\",\"exp\",\"floor\",\"log\",\"round\",\"sin\",\"sqrt\",\"tan\"];(function(){for(var r=0;r<Zr.length;++r){var e=Zr[r];I[e]=O({args:[\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(b)\",thisVars:[\"this_f\"]},funcName:e}),I[e+\"eq\"]=O({args:[\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\"],body:\"a=this_f(a)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"eq\"})}})();var Xr=[\"max\",\"min\",\"atan2\",\"pow\"];(function(){for(var r=0;r<Xr.length;++r){var e=Xr[r];I[e]=O({args:[\"array\",\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\",\"c\"],body:\"a=this_f(b,c)\",thisVars:[\"this_f\"]},funcName:e}),I[e+\"s\"]=O({args:[\"array\",\"array\",\"scalar\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\",\"c\"],body:\"a=this_f(b,c)\",thisVars:[\"this_f\"]},funcName:e+\"s\"}),I[e+\"eq\"]=O({args:[\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(a,b)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"eq\"}),I[e+\"seq\"]=O({args:[\"array\",\"scalar\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(a,b)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"seq\"})}})();var Kr=[\"atan2\",\"pow\"];(function(){for(var r=0;r<Kr.length;++r){var e=Kr[r];I[e+\"op\"]=O({args:[\"array\",\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\",\"c\"],body:\"a=this_f(c,b)\",thisVars:[\"this_f\"]},funcName:e+\"op\"}),I[e+\"ops\"]=O({args:[\"array\",\"array\",\"scalar\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\",\"c\"],body:\"a=this_f(c,b)\",thisVars:[\"this_f\"]},funcName:e+\"ops\"}),I[e+\"opeq\"]=O({args:[\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(b,a)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"opeq\"}),I[e+\"opseq\"]=O({args:[\"array\",\"scalar\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(b,a)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"opseq\"})}})();I.any=B({args:[\"array\"],pre:dr,body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:1}],body:\"if(a){return true}\",localVars:[],thisVars:[]},post:{args:[],localVars:[],thisVars:[],body:\"return false\"},funcName:\"any\"});I.all=B({args:[\"array\"],pre:dr,body:{args:[{name:\"x\",lvalue:!1,rvalue:!0,count:1}],body:\"if(!x){return false}\",localVars:[],thisVars:[]},post:{args:[],localVars:[],thisVars:[],body:\"return true\"},funcName:\"all\"});I.sum=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:1}],body:\"this_s+=a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"sum\"});I.prod=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=1\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:1}],body:\"this_s*=a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"prod\"});I.norm2squared=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:2}],body:\"this_s+=a*a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"norm2squared\"});I.norm2=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:2}],body:\"this_s+=a*a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return Math.sqrt(this_s)\"},funcName:\"norm2\"});I.norminf=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:4}],body:\"if(-a>this_s){this_s=-a}else if(a>this_s){this_s=a}\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"norminf\"});I.norm1=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:3}],body:\"this_s+=a<0?-a:a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"norm1\"});I.sup=B({args:[\"array\"],pre:{body:\"this_h=-Infinity\",args:[],thisVars:[\"this_h\"],localVars:[]},body:{body:\"if(_inline_1_arg0_>this_h)this_h=_inline_1_arg0_\",args:[{name:\"_inline_1_arg0_\",lvalue:!1,rvalue:!0,count:2}],thisVars:[\"this_h\"],localVars:[]},post:{body:\"return this_h\",args:[],thisVars:[\"this_h\"],localVars:[]}});I.inf=B({args:[\"array\"],pre:{body:\"this_h=Infinity\",args:[],thisVars:[\"this_h\"],localVars:[]},body:{body:\"if(_inline_1_arg0_<this_h)this_h=_inline_1_arg0_\",args:[{name:\"_inline_1_arg0_\",lvalue:!1,rvalue:!0,count:2}],thisVars:[\"this_h\"],localVars:[]},post:{body:\"return this_h\",args:[],thisVars:[\"this_h\"],localVars:[]}});I.argmin=B({args:[\"index\",\"array\",\"shape\"],pre:{body:\"{this_v=Infinity;this_i=_inline_0_arg2_.slice(0)}\",args:[{name:\"_inline_0_arg0_\",lvalue:!1,rvalue:!1,count:0},{name:\"_inline_0_arg1_\",lvalue:!1,rvalue:!1,count:0},{name:\"_inline_0_arg2_\",lvalue:!1,rvalue:!0,count:1}],thisVars:[\"this_i\",\"this_v\"],localVars:[]},body:{body:\"{if(_inline_1_arg1_<this_v){this_v=_inline_1_arg1_;for(var _inline_1_k=0;_inline_1_k<_inline_1_arg0_.length;++_inline_1_k){this_i[_inline_1_k]=_inline_1_arg0_[_inline_1_k]}}}\",args:[{name:\"_inline_1_arg0_\",lvalue:!1,rvalue:!0,count:2},{name:\"_inline_1_arg1_\",lvalue:!1,rvalue:!0,count:2}],thisVars:[\"this_i\",\"this_v\"],localVars:[\"_inline_1_k\"]},post:{body:\"{return this_i}\",args:[],thisVars:[\"this_i\"],localVars:[]}});I.argmax=B({args:[\"index\",\"array\",\"shape\"],pre:{body:\"{this_v=-Infinity;this_i=_inline_0_arg2_.slice(0)}\",args:[{name:\"_inline_0_arg0_\",lvalue:!1,rvalue:!1,count:0},{name:\"_inline_0_arg1_\",lvalue:!1,rvalue:!1,count:0},{name:\"_inline_0_arg2_\",lvalue:!1,rvalue:!0,count:1}],thisVars:[\"this_i\",\"this_v\"],localVars:[]},body:{body:\"{if(_inline_1_arg1_>this_v){this_v=_inline_1_arg1_;for(var _inline_1_k=0;_inline_1_k<_inline_1_arg0_.length;++_inline_1_k){this_i[_inline_1_k]=_inline_1_arg0_[_inline_1_k]}}}\",args:[{name:\"_inline_1_arg0_\",lvalue:!1,rvalue:!0,count:2},{name:\"_inline_1_arg1_\",lvalue:!1,rvalue:!0,count:2}],thisVars:[\"this_i\",\"this_v\"],localVars:[\"_inline_1_k\"]},post:{body:\"{return this_i}\",args:[],thisVars:[\"this_i\"],localVars:[]}});I.random=O({args:[\"array\"],pre:{args:[],body:\"this_f=Math.random\",thisVars:[\"this_f\"]},body:{args:[\"a\"],body:\"a=this_f()\",thisVars:[\"this_f\"]},funcName:\"random\"});I.assign=O({args:[\"array\",\"array\"],body:{args:[\"a\",\"b\"],body:\"a=b\"},funcName:\"assign\"});I.assigns=O({args:[\"array\",\"scalar\"],body:{args:[\"a\",\"b\"],body:\"a=b\"},funcName:\"assigns\"});I.equals=B({args:[\"array\",\"array\"],pre:dr,body:{args:[{name:\"x\",lvalue:!1,rvalue:!0,count:1},{name:\"y\",lvalue:!1,rvalue:!0,count:1}],body:\"if(x!==y){return false}\",localVars:[],thisVars:[]},post:{args:[],localVars:[],thisVars:[],body:\"return true\"},funcName:\"equals\"})});var G=Q(tr(),1),H=Q(sr(),1);var j=Q(tr(),1),A=Q(sr(),1),J=class{constructor(e,n,a){this._dataNormalized=e,this._minD=n,this._maxD=a}static createFromUnnormalized(e){let n=A.sup(e),a=A.inf(e),t=(0,j.default)(new Float32Array(e.size),e.shape),s=n-a;return s<1e-4?A.assigns(t,0):(A.subs(t,e,a),A.divs(t,t,s)),new J(t,a,n)}get data(){return this._dataNormalized}get minD(){return this._minD}get maxD(){return this._maxD}denormalize(){let e=(0,j.default)(new Float32Array(this._dataNormalized.size),this._dataNormalized.shape);return A.muls(e,this._dataNormalized,this._maxD-this._minD),A.adds(e,e,this._minD),e}},D=class{constructor(e,n){this._quantized=e,this._method=n}get quantized(){return this._quantized}static maxIntBits(e){return 2**e-1}static fromNormalized(e,n){let a=e.data,t;if(n===\"norm8x\"){let s=D.maxIntBits(8),i=(0,j.default)(new Float32Array(a.size),a.shape);A.muls(i,a,s),A.roundeq(i),t=(0,j.default)(new Uint8Array(i.data),a.shape)}else if(n===\"norm565\"){let s=(0,j.default)(new Float32Array(a.size),a.shape);A.assign(s,a),A.mulseq(s.pick(null,0),D.maxIntBits(5)),A.mulseq(s.pick(null,1),D.maxIntBits(6)),A.mulseq(s.pick(null,2),D.maxIntBits(5)),A.roundeq(s);let i=(0,j.default)(new Uint16Array(s.data),a.shape),h=(0,j.default)(new Uint16Array(a.shape[0]),[a.shape[0]]),o=(0,j.default)(new Uint16Array(a.shape[0]),[a.shape[0]]);A.lshifts(h,i.pick(null,0),11),A.lshifts(o,i.pick(null,1),5),A.boreq(h,o),A.boreq(h,i.pick(null,2)),t=h}else{let s=(0,j.default)(new Float32Array(a.size),a.shape);A.assign(s,a),A.mulseq(s.pick(null,0),D.maxIntBits(11)),A.mulseq(s.pick(null,1),D.maxIntBits(10)),A.mulseq(s.pick(null,2),D.maxIntBits(11)),A.roundeq(s);let i=(0,j.default)(new Uint32Array(s.data),a.shape),h=(0,j.default)(new Uint32Array(a.shape[0]),[a.shape[0]]),o=(0,j.default)(new Uint32Array(a.shape[0]),[a.shape[0]]);A.lshifts(h,i.pick(null,0),21),A.lshifts(o,i.pick(null,1),11),A.boreq(h,o),A.boreq(h,i.pick(null,2)),t=h}return new D(t,n)}dequantize(e,n){let a=this._method,t,s=this._quantized;if(a===\"norm8x\"){let i=D.maxIntBits(8);t=(0,j.default)(new Float32Array(s.size),s.shape),A.muls(t,s,1/i)}else if(a===\"norm565\"){let i=(0,j.default)(new Uint8Array(s.shape[0]),[s.shape[0]]),h=(0,j.default)(new Uint8Array(s.shape[0]),[s.shape[0]]),o=(0,j.default)(new Uint8Array(s.shape[0]),[s.shape[0]]);A.rrshifts(i,s,11),A.rrshifts(h,s,5),A.bandseq(h,D.maxIntBits(6)),A.bands(o,s,D.maxIntBits(5)),t=(0,j.default)(new Float32Array(s.shape[0]*3),[s.shape[0],3]),A.muls(t.pick(null,0),i,1/D.maxIntBits(5)),A.muls(t.pick(null,1),h,1/D.maxIntBits(6)),A.muls(t.pick(null,2),o,1/D.maxIntBits(5))}else{let i=(0,j.default)(new Uint16Array(s.shape[0]),[s.shape[0]]),h=(0,j.default)(new Uint16Array(s.shape[0]),[s.shape[0]]),o=(0,j.default)(new Uint16Array(s.shape[0]),[s.shape[0]]);A.rrshifts(i,s,21),A.rrshifts(h,s,11),A.bandseq(h,D.maxIntBits(10)),A.bands(o,s,D.maxIntBits(11)),t=(0,j.default)(new Float32Array(s.shape[0]*3),[s.shape[0],3]),A.muls(t.pick(null,0),i,1/D.maxIntBits(11)),A.muls(t.pick(null,1),h,1/D.maxIntBits(10)),A.muls(t.pick(null,2),o,1/D.maxIntBits(11))}return new J(t,e,n)}};var E=class{constructor(e,n,a,t,s,i=!1){this._quantized=e,this._minMaxMatrix=n,this._chunkSize=a,this._quantizationMethod=t,this._variableChunkSize=s,this._isDynamicChunks=i}get length(){return this._quantized.shape[0]}get nchunks(){return this._minMaxMatrix.shape[0]}get quantized(){return this._quantized}get method(){return this._quantizationMethod}get minmaxMatrix(){return this._minMaxMatrix}_createPrunedMinMax(e){let n=e.length,a=this.minmaxMatrix.shape[0]-n,t=(0,G.default)(new Float32Array(a*2),[a,2]),s=0,i=a,h=0,o=this.minmaxMatrix.shape[0];for(let c=0;c<e.length;c++)o=e[c],i=o-h+s,i>s&&H.assign(t.hi(i,2).lo(s,0),this.minmaxMatrix.hi(o,2).lo(h,0)),s=i,h=o+1;return s<a&&H.assign(t.lo(s,0),this.minmaxMatrix.lo(h,0)),t}_createPrunedQuantized(e){let n=e.length,a=this.quantized.shape[0]-n,t=this._quantizationMethod,s,i;if(t===\"norm8x\"){i=this._quantized.shape[1];let f=i?a*i:a;s=(0,G.default)(new Uint8Array(f),i?[a,i]:[a,1])}else t===\"norm565\"?s=(0,G.default)(new Uint16Array(a),[a]):s=(0,G.default)(new Uint32Array(a),[a]);let h=0,o=a,c=0,l=s.shape[0];for(let f=0;f<e.length;f++)l=e[f],o=l-c+h,o>h&&(i?H.assign(s.hi(o,i).lo(h,0),this._quantized.hi(l,i).lo(c,0)):H.assign(s.hi(o).lo(h),this._quantized.hi(l).lo(c))),h=o,c=l+1;return h<a&&(i?H.assign(s.lo(h,0),this._quantized.lo(c,0)):H.assign(s.lo(h),this._quantized.lo(c))),s}pruneFeature(e,n,a){let t=this._createPrunedQuantized(e),s=this._createPrunedMinMax(n);return new E(t,s,this._chunkSize,this._quantizationMethod,a,!0)}static getRequiredNChunks(e,n){return Math.floor(e/n)}static fromArray(e,n,a){let t=e.shape[0],s=Math.floor(t/a),i=(0,G.default)(new Float32Array(s*2),[s,2],[2,1]),h;n===\"norm8x\"?h=(0,G.default)(new Uint8Array(e.size),e.shape):n===\"norm565\"?h=(0,G.default)(new Uint16Array(e.shape[0]),[e.shape[0]]):h=(0,G.default)(new Uint32Array(e.shape[0]),[e.shape[0]]);for(let o=0;o<s;o++){let c=o*a,l=o+1<s?(o+1)*a:t,f;e.shape.length>1?f=J.createFromUnnormalized(e.hi(l,e.shape[1]).lo(c,0)):f=J.createFromUnnormalized(e.hi(l).lo(c)),i.set(o,0,f.minD),i.set(o,1,f.maxD),h.shape.length>1?H.assign(h.hi(l,h.shape[1]).lo(c,0),D.fromNormalized(f,n).quantized):H.assign(h.hi(l).lo(c),D.fromNormalized(f,n).quantized)}return new E(h,i,a,n)}denormDequant(){let e=this._minMaxMatrix.shape[0],n=this._quantized,a=n.shape[0],t=this._quantizationMethod,s=this._chunkSize,i;if(this._isDynamicChunks){if(!this._variableChunkSize)throw new Error(\"variable chunk must exists if chunkSize isDynamic\");i=this._variableChunkSize}let h;t===\"norm8x\"?h=(0,G.default)(new Float32Array(n.size),n.shape):h=(0,G.default)(new Float32Array(a*3),[a,3]);let o=0,c=s;for(let l=0;l<e;l++){let[f,p]=[this._minMaxMatrix.get(l,0),this._minMaxMatrix.get(l,1)];this._isDynamicChunks&&(c=i[l]);let v=l+1<e?o+c:a,m;n.shape.length>1?m=new D(n.hi(v,n.shape[1]).lo(o,0),t):m=new D(n.hi(v).lo(o),t),H.assign(h.hi(v,h.shape[1]).lo(o,0),m.dequantize(f,p).denormalize()),o=v}return h}static async fetchArrayBuffer(e){return await(await fetch(e,{mode:\"cors\"})).arrayBuffer()}};var Z=Q(tr(),1),k=Q(sr(),1);var Jr=\"http://127.0.0.1:8000\";var er=Q(tr(),1),C=Q(sr(),1);var re=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9];function ee(r){return r<1e5?r<100?r<10?0:1:r<1e4?r<1e3?2:3:4:r<1e7?r<1e6?5:6:r<1e9?r<1e8?7:8:9}function ne(r,e){if(r===e)return 0;if(~~r===r&&~~e===e){if(r===0||e===0)return r<e?-1:1;if(r<0||e<0){if(e>=0)return-1;if(r>=0)return 1;r=-r,e=-e}let t=ee(r),s=ee(e),i=0;return t<s?(r*=re[s-t-1],e/=10,i=-1):t>s&&(e*=re[t-s-1],r/=10,i=1),r===e?i:r<e?-1:1}let n=String(r),a=String(e);return n===a?0:n<a?-1:1}function gn(r){let e=0;for(;r>=32;)e|=r&1,r>>=1;return r+e}function ae(r,e,n,a){let t=e+1;if(t===n)return 1;if(a(r[t++],r[e])<0){for(;t<n&&a(r[t],r[t-1])<0;)t++;Mn(r,e,t)}else for(;t<n&&a(r[t],r[t-1])>=0;)t++;return t-e}function Mn(r,e,n){for(n--;e<n;){let a=r[e];r[e++]=r[n],r[n--]=a}}function te(r,e,n,a,t){for(a===e&&a++;a<n;a++){let s=r[a],i=e,h=a;for(;i<h;){let c=i+h>>>1;t(s,r[c])<0?h=c:i=c+1}let o=a-i;switch(o){case 3:r[i+3]=r[i+2];case 2:r[i+2]=r[i+1];case 1:r[i+1]=r[i];break;default:for(;o>0;)r[i+o]=r[i+o-1],o--}r[i]=s}}function Mr(r,e,n,a,t,s){let i=0,h=0,o=1;if(s(r,e[n+t])>0){for(h=a-t;o<h&&s(r,e[n+t+o])>0;)i=o,o=(o<<1)+1,o<=0&&(o=h);o>h&&(o=h),i+=t,o+=t}else{for(h=t+1;o<h&&s(r,e[n+t-o])<=0;)i=o,o=(o<<1)+1,o<=0&&(o=h);o>h&&(o=h);let c=i;i=t-o,o=t-c}for(i++;i<o;){let c=i+(o-i>>>1);s(r,e[n+c])>0?i=c+1:o=c}return o}function _r(r,e,n,a,t,s){let i=0,h=0,o=1;if(s(r,e[n+t])<0){for(h=t+1;o<h&&s(r,e[n+t-o])<0;)i=o,o=(o<<1)+1,o<=0&&(o=h);o>h&&(o=h);let c=i;i=t-o,o=t-c}else{for(h=a-t;o<h&&s(r,e[n+t+o])>=0;)i=o,o=(o<<1)+1,o<=0&&(o=h);o>h&&(o=h),i+=t,o+=t}for(i++;i<o;){let c=i+(o-i>>>1);s(r,e[n+c])<0?o=c:i=c+1}return o}var br=class{constructor(e,n){$(this,\"array\",null);$(this,\"compare\",null);$(this,\"minGallop\",7);$(this,\"length\",0);$(this,\"tmpStorageLength\",256);$(this,\"stackLength\",0);$(this,\"runStart\",null);$(this,\"runLength\",null);$(this,\"stackSize\",0);this.array=e,this.compare=n,this.length=e.length,this.length<2*256&&(this.tmpStorageLength=this.length>>>1),this.tmp=new Array(this.tmpStorageLength),this.stackLength=this.length<120?5:this.length<1542?10:this.length<119151?19:40,this.runStart=new Array(this.stackLength),this.runLength=new Array(this.stackLength)}pushRun(e,n){this.runStart[this.stackSize]=e,this.runLength[this.stackSize]=n,this.stackSize+=1}mergeRuns(){for(;this.stackSize>1;){let e=this.stackSize-2;if(e>=1&&this.runLength[e-1]<=this.runLength[e]+this.runLength[e+1]||e>=2&&this.runLength[e-2]<=this.runLength[e]+this.runLength[e-1])this.runLength[e-1]<this.runLength[e+1]&&e--;else if(this.runLength[e]>this.runLength[e+1])break;this.mergeAt(e)}}forceMergeRuns(){for(;this.stackSize>1;){let e=this.stackSize-2;e>0&&this.runLength[e-1]<this.runLength[e+1]&&e--,this.mergeAt(e)}}mergeAt(e){let n=this.compare,a=this.array,t=this.runStart[e],s=this.runLength[e],i=this.runStart[e+1],h=this.runLength[e+1];this.runLength[e]=s+h,e===this.stackSize-3&&(this.runStart[e+1]=this.runStart[e+2],this.runLength[e+1]=this.runLength[e+2]),this.stackSize--;let o=_r(a[i],a,t,s,0,n);t+=o,s-=o,s!==0&&(h=Mr(a[t+s-1],a,i,h,h-1,n),h!==0&&(s<=h?this.mergeLow(t,s,i,h):this.mergeHigh(t,s,i,h)))}mergeLow(e,n,a,t){let s=this.compare,i=this.array,h=this.tmp,o=0;for(o=0;o<n;o++)h[o]=i[e+o];let c=0,l=a,f=e;if(i[f++]=i[l++],--t===0){for(o=0;o<n;o++)i[f+o]=h[c+o];return}if(n===1){for(o=0;o<t;o++)i[f+o]=i[l+o];i[f+t]=h[c];return}let p=this.minGallop;for(;;){let v=0,m=0,d=!1;do if(s(i[l],h[c])<0){if(i[f++]=i[l++],m++,v=0,--t===0){d=!0;break}}else if(i[f++]=h[c++],v++,m=0,--n===1){d=!0;break}while((v|m)<p);if(d)break;do{if(v=_r(i[l],h,c,n,0,s),v!==0){for(o=0;o<v;o++)i[f+o]=h[c+o];if(f+=v,c+=v,n-=v,n<=1){d=!0;break}}if(i[f++]=i[l++],--t===0){d=!0;break}if(m=Mr(h[c],i,l,t,0,s),m!==0){for(o=0;o<m;o++)i[f+o]=i[l+o];if(f+=m,l+=m,t-=m,t===0){d=!0;break}}if(i[f++]=h[c++],--n===1){d=!0;break}p--}while(v>=7||m>=7);if(d)break;p<0&&(p=0),p+=2}if(this.minGallop=p,p<1&&(this.minGallop=1),n===1){for(o=0;o<t;o++)i[f+o]=i[l+o];i[f+t]=h[c]}else{if(n===0)throw new Error(\"mergeLow preconditions were not respected\");for(o=0;o<n;o++)i[f+o]=h[c+o]}}mergeHigh(e,n,a,t){let s=this.compare,i=this.array,h=this.tmp,o=0;for(o=0;o<t;o++)h[o]=i[a+o];let c=e+n-1,l=t-1,f=a+t-1,p=0,v=0;if(i[f--]=i[c--],--n===0){for(p=f-(t-1),o=0;o<t;o++)i[p+o]=h[o];return}if(t===1){for(f-=n,c-=n,v=f+1,p=c+1,o=n-1;o>=0;o--)i[v+o]=i[p+o];i[f]=h[l];return}let m=this.minGallop;for(;;){let d=0,y=0,u=!1;do if(s(h[l],i[c])<0){if(i[f--]=i[c--],d++,y=0,--n===0){u=!0;break}}else if(i[f--]=h[l--],y++,d=0,--t===1){u=!0;break}while((d|y)<m);if(u)break;do{if(d=n-_r(h[l],i,e,n,n-1,s),d!==0){for(f-=d,c-=d,n-=d,v=f+1,p=c+1,o=d-1;o>=0;o--)i[v+o]=i[p+o];if(n===0){u=!0;break}}if(i[f--]=h[l--],--t===1){u=!0;break}if(y=t-Mr(i[c],h,0,t,t-1,s),y!==0){for(f-=y,l-=y,t-=y,v=f+1,p=l+1,o=0;o<y;o++)i[v+o]=h[p+o];if(t<=1){u=!0;break}}if(i[f--]=i[c--],--n===0){u=!0;break}m--}while(d>=7||y>=7);if(u)break;m<0&&(m=0),m+=2}if(this.minGallop=m,m<1&&(this.minGallop=1),t===1){for(f-=n,c-=n,v=f+1,p=c+1,o=n-1;o>=0;o--)i[v+o]=i[p+o];i[f]=h[l]}else{if(t===0)throw new Error(\"mergeHigh preconditions were not respected\");for(p=f-(t-1),o=0;o<t;o++)i[p+o]=h[o]}}};function se(r,e,n,a){if(!Array.isArray(r))throw new TypeError(\"Can only sort arrays\");e?typeof e!=\"function\"&&(a=n,n=e,e=ne):e=ne,n||(n=0),a||(a=r.length);let t=a-n;if(t<2)return;let s=0;if(t<32){s=ae(r,n,a,e),te(r,n,a,n+s,e);return}let i=new br(r,e),h=gn(t);do{if(s=ae(r,n,a,e),s<h){let o=t;o>h&&(o=h),te(r,n,n+o,n+s,e),s=o}i.pushRun(n,s),i.mergeRuns(),t-=s,n+=s}while(t!==0);i.forceMergeRuns()}function Ar(r){let e=(0,er.default)(new Int32Array(r.shape[0]),[r.shape[0]]),n=(0,er.default)(new Int32Array(r.shape[0]),[r.shape[0]]);return C.bands(e,r,1023),C.lshifts(n,e,16),C.bxoreq(e,n),C.bandseq(e,4278190335),C.lshifts(n,e,8),C.bxoreq(e,n),C.bandseq(e,50393103),C.lshifts(n,e,4),C.bxoreq(e,n),C.bandseq(e,51130563),C.lshifts(n,e,2),C.bxoreq(e,n),C.bandseq(e,153391689),e}function _n(r){let e=Ar(r.pick(null,0)),n=Ar(r.pick(null,1));C.lshiftseq(n,1);let a=Ar(r.pick(null,2));return C.lshiftseq(a,2),C.boreq(e,n),C.boreq(e,a),e}function rr(r,e){if(r.shape[0]!==e.shape[0])throw new Error(\"wrong length\");let n=(0,er.default)(new Float32Array(r.size),r.shape,r.stride,r.offset);for(let a=0;a<e.shape[0];a++){let t=e.get(a);if(r.shape.length>1)for(let s=0;s<r.shape[1];s++)n.set(a,s,r.get(t,s));else n.set(a,r.get(t))}return n}function wr(r){let e=C.sup(r),n=C.inf(r),a=1e3/Math.min(1e3,e-n),t=(0,er.default)(new Float32Array(r.data),r.shape);C.mulseq(t,a);let s=(0,er.default)(new Int32Array(t.data),r.shape),i=_n(s),o=Array.from(i.data).map((f,p)=>[f,p]);se(o,(f,p)=>f[0]-p[0]);let c=o.map(([f,p])=>p);return(0,er.default)(Uint32Array.from(c))}var U=class{constructor(e,n,a,t,s,i,h,o,c,l){this.propertyDescs=e,this.format=n,this.nsplats=a,this.xyz=t,this.colors=s,this.harmonics=i,this.opacity=h,this.scaling=o,this.rotation=c,this.maxSHDegree=l}getPlyBinary(){let e=U._generateHeaderString(this.propertyDescs,this.format,this.nsplats),n=new TextEncoder().encode(e),a=Object.keys(this.propertyDescs).length,t=(0,Z.default)(new Float32Array(this.nsplats*a),[this.nsplats,a]);if(k.assign(t.pick(null,this.propertyDescs.x.index),this.xyz.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.y.index),this.xyz.pick(null,1)),k.assign(t.pick(null,this.propertyDescs.z.index),this.xyz.pick(null,2)),k.assign(t.pick(null,this.propertyDescs.f_dc_0.index),this.colors.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.f_dc_1.index),this.colors.pick(null,1)),k.assign(t.pick(null,this.propertyDescs.f_dc_2.index),this.colors.pick(null,2)),k.assign(t.pick(null,this.propertyDescs.opacity.index),this.opacity.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.scale_0.index),this.scaling.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.scale_1.index),this.scaling.pick(null,1)),k.assign(t.pick(null,this.propertyDescs.scale_2.index),this.scaling.pick(null,2)),k.assign(t.pick(null,this.propertyDescs.rot_0.index),this.rotation.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.rot_1.index),this.rotation.pick(null,1)),k.assign(t.pick(null,this.propertyDescs.rot_2.index),this.rotation.pick(null,2)),k.assign(t.pick(null,this.propertyDescs.rot_3.index),this.rotation.pick(null,3)),this.harmonics&&this.harmonics.length>0)for(let h=0;h<this.harmonics.length;h++){let o=h*3;k.assign(t.pick(null,this.propertyDescs[`f_rest_${o}`].index),this.harmonics[h].pick(null,0)),k.assign(t.pick(null,this.propertyDescs[`f_rest_${o+1}`].index),this.harmonics[h].pick(null,1)),k.assign(t.pick(null,this.propertyDescs[`f_rest_${o+2}`].index),this.harmonics[h].pick(null,2))}let s=new Uint8Array(t.data.buffer),i=new Uint8Array(s.length+n.length);return i.set(n),i.set(s,n.length),i.buffer}save(e,n){let a=this.getPlyBinary(),t=new Blob([a],{type:\"application/octet-stream\"}),s=new File([t],e),i=new FormData;i.append(\"file\",s),i.append(\"filename\",e),i.append(\"basedir\",n),fetch(`${Jr}/push_file`,{method:\"POST\",body:i})}static async loadFile(e){return await(await fetch(e)).arrayBuffer()}mortonPositionSplatsSort(){let e=wr(this.xyz),n=rr(this.xyz,e),a=rr(this.colors,e),t=rr(this.opacity,e),s=rr(this.scaling,e),i=rr(this.rotation,e),h=[];for(let o=0;o<this.harmonics.length;o++)h.push(rr(this.harmonics[o],e));return new U(this.propertyDescs,this.format,this.nsplats,n,a,h,t,s,i,this.maxSHDegree)}static _generateHeaderString(e,n,a){let t=`ply\nformat ${n.format} ${n.version}\nelement vertex ${a}`,s=Object.keys(e).length,i=Array(s);for(let h in e){let o=e[h];i[o.index]={name:h,dtype:o.dtype}}for(let h=0;h<i.length;h++)t=`${t}\nproperty ${i[h].dtype} ${i[h].name}`;return`${t}\nend_header\n`}static fromArrayBuffer(e,n=3){let{splatCount:a,vertexData:t,propertiesDesc:s,format:i}=U.decodeHeader(e),h=t.buffer.slice(t.byteOffset),o=Object.keys(s).length,c=(0,Z.default)(new Float32Array(h),[a,o]),l=0,f={},p={double:8,int:4,uint:4,float:4,short:2,ushort:2,uchar:1,char:1};for(let _ in s)if(s.hasOwnProperty(_)){let g=s[_].dtype;f[_]=l,l+=p[g]}let v=(0,Z.default)(new Float32Array(a*3),[a,3]);k.assign(v.pick(null,0),c.pick(null,f.x/4)),k.assign(v.pick(null,1),c.pick(null,f.y/4)),k.assign(v.pick(null,2),c.pick(null,f.z/4));let m=(0,Z.default)(new Float32Array(a*3),[a,3]);k.assign(m.pick(null,0),c.pick(null,f.scale_0/4)),k.assign(m.pick(null,1),c.pick(null,f.scale_1/4)),k.assign(m.pick(null,2),c.pick(null,f.scale_2/4));let d=(0,Z.default)(new Float32Array(a*3),[a,3]);k.assign(d.pick(null,0),c.pick(null,f.f_dc_0/4)),k.assign(d.pick(null,1),c.pick(null,f.f_dc_1/4)),k.assign(d.pick(null,2),c.pick(null,f.f_dc_2/4));let y=(0,Z.default)(new Float32Array(a*4),[a,4]);k.assign(y.pick(null,0),c.pick(null,f.rot_1/4)),k.assign(y.pick(null,1),c.pick(null,f.rot_2/4)),k.assign(y.pick(null,2),c.pick(null,f.rot_3/4)),k.assign(y.pick(null,3),c.pick(null,f.rot_0/4));for(let _=0;_<a;_++){let g=y.pick(_,null),N=Math.sqrt(g.get(0)**2+g.get(1)**2+g.get(2)**2+g.get(3)**2);k.divseq(g,N)}let u=(0,Z.default)(new Float32Array(a*1),[a,1]);k.assign(u.pick(null,0),c.pick(null,f.opacity/4)),k.negeq(u),k.expeq(u),k.addseq(u,1),k.recipeq(u),k.mulseq(u,255);let M=(Math.min(Math.max(n,0),3)+1)**2-1,b=[];for(let _=0;_<M;_++){let g=(0,Z.default)(new Float32Array(a*3),[a,3]),N=_*3;k.assign(g.pick(null,0),c.pick(null,f[`f_rest_${N}`]/4)),k.assign(g.pick(null,1),c.pick(null,f[`f_rest_${N+1}`]/4)),k.assign(g.pick(null,2),c.pick(null,f[`f_rest_${N+2}`]/4)),b.push(g)}return new U(s,i,a,v,d,b,u,m,y,n)}static async fromPLYFile(e,n=3){let a=await U.loadFile(e);return U.fromArrayBuffer(a,n)}static decodeHeader(e){let n=new TextDecoder,a=0,t=\"\",s=100;for(;;){if(a+s>=e.byteLength)throw new Error(\"End of file reached while searching for end of header\");let m=new Uint8Array(e,a,s);t+=n.decode(m),a+=s;let d=a-s*2,y=new Uint8Array(e,Math.max(0,d),d>0?s*2:s);if(n.decode(y).includes(\"end_header\"))break}let i=t.split(`\n`),h=0,o={},c={},l=0,f;for(let m=0;m<i.length;m++){let d=i[m].trim();if(d.startsWith(\"element vertex\")){let y=d.match(/\\d+/);y&&(h=parseInt(y[0]))}else if(d.startsWith(\"property\")){let y=d.match(/(\\w+)\\s+(\\w+)\\s+(\\w+)/);if(y){let u=y[2],x=y[3];o[x]=l,c[x]={dtype:u,index:l},l++}}else if(d.startsWith(\"format\")){let y=d.match(/(\\w+)\\s+(\\w+)\\s+(\\d+\\.?\\d*)/);y&&(f={format:y[2],version:y[3]})}else if(d===\"end_header\")break}let p=t.indexOf(\"end_header\")+10+1,v=new DataView(e,p);return{splatCount:h,vertexData:v,headerOffset:a,propertiesDesc:c,format:f}}};var X=class{constructor(e,n,a,t,s,i,h,o){this.config=e,this.xyz=n,this.scaling=a,this.color=t,this.opacity=s,this.harmonics=h,this.quaternion=i,this.variableChunkSize=o}get isDynamicChunks(){return this.variableChunkSize&&this.variableChunkSize.length>0}get nchunks(){return this.xyz.nchunks}get nsplats(){return this.xyz.length}get chunkSize(){return this.config.chunkSize}static compressFromGaussianData(e,n){let a=E.fromArray(e.xyz,n.xyz,n.chunkSize),t=E.fromArray(e.scaling,n.scaling,n.chunkSize),s=E.fromArray(e.colors,n.color,n.chunkSize),i=E.fromArray(e.opacity,n.opacity,n.chunkSize),h=E.fromArray(e.rotation,n.quaternion,n.chunkSize),o=e.harmonics,c=[];if(n.harmonics)for(let l=0;l<o.length;l++){let f=E.fromArray(o[l],n.harmonics,n.chunkSize);c.push(f)}return new X(n,a,t,s,i,h,c)}_countIndexesInChunks(e){let n=[],a=this.nchunks,t=this.chunkSize,s=this.nsplats,i=E.getRequiredNChunks(s,t);if(a===i)for(let h=0;h<e.length;h++){let o=e[h],c=Math.floor(o/this.chunkSize);c in n?n[c].push(o):n[c]=[o]}else{let h=this.variableChunkSize,o={},c=0;for(let l=0;l<a;l++)o[l]=c,c+=h[l];for(let l=0;l<e.length;l++){let f=e[l],p=Math.min(Math.floor(f/t),a-1);for(;f>=o[p]+h[p];)p++;p in n?n[p].push(f):n[p]=[f]}}return n}pruneSplats(e){let n=this._countIndexesInChunks(e),a,t=[];return n.length>0&&(a=this.variableChunkSize?[...this.variableChunkSize]:Array(this.nchunks).fill(this.chunkSize),n.forEach((s,i)=>{a[i]-=s.length,a[i]<=0&&t.push(i)}),a=a.filter(s=>s>0)),new X(this.config,this.xyz.pruneFeature(e,t,a),this.scaling.pruneFeature(e,t,a),this.color.pruneFeature(e,t,a),this.opacity.pruneFeature(e,t,a),this.quaternion.pruneFeature(e,t,a),this.harmonics?this.harmonics.map(s=>s.pruneFeature(e,t,this.variableChunkSize)):void 0,a)}static async loadConfig(e){return await(await fetch(e,{method:\"GET\",mode:\"cors\",headers:{Accept:\"application/json\"}})).json()}toGaussians(){let e={format:\"binary_little_endian\",version:\"1.0\"},n={},a=0;if(n.x={dtype:\"float\",index:a},a++,n.y={dtype:\"float\",index:a},a++,n.z={dtype:\"float\",index:a},a++,n.f_dc_0={dtype:\"float\",index:a},a++,n.f_dc_1={dtype:\"float\",index:a},a++,n.f_dc_2={dtype:\"float\",index:a},a++,this.harmonics&&this.harmonics.length>0)for(let i=0;i<this.harmonics.length;i++)n[`f_rest_${i}`]={dtype:\"float\",index:a},a++,n[`f_rest_${i+1}`]={dtype:\"float\",index:a},a++,n[`f_rest_${i+2}`]={dtype:\"float\",index:a},a++;n.opacity={dtype:\"float\",index:a},a++,n.scale_0={dtype:\"float\",index:a},a++,n.scale_1={dtype:\"float\",index:a},a++,n.scale_2={dtype:\"float\",index:a},a++,n.rot_0={dtype:\"float\",index:a},a++,n.rot_1={dtype:\"float\",index:a},a++,n.rot_2={dtype:\"float\",index:a},a++,n.rot_3={dtype:\"float\",index:a},a++;let t=this.harmonics?.map(i=>i.denormDequant());return new U(n,e,this.xyz.length,this.xyz.denormDequant(),this.color.denormDequant(),t||[],this.opacity.denormDequant(),this.scaling.denormDequant(),this.quaternion.denormDequant(),3)}};var nr=Q(tr(),1),R=Q(sr(),1);var S=1e-6,P=typeof Float32Array<\"u\"?Float32Array:Array,ir=Math.random;var Wt=Math.PI/180;Math.hypot||(Math.hypot=function(){for(var r=0,e=arguments.length;e--;)r+=arguments[e]*arguments[e];return Math.sqrt(r)});var W={};ur(W,{add:()=>Yn,adjoint:()=>Sn,clone:()=>An,copy:()=>wn,create:()=>kr,determinant:()=>In,equals:()=>Hn,exactEquals:()=>$n,frob:()=>Un,fromMat2d:()=>Vn,fromMat4:()=>bn,fromQuat:()=>On,fromRotation:()=>Tn,fromScaling:()=>Pn,fromTranslation:()=>En,fromValues:()=>kn,identity:()=>qn,invert:()=>Fn,mul:()=>Wn,multiply:()=>ie,multiplyScalar:()=>Gn,multiplyScalarAndAdd:()=>Qn,normalFromMat4:()=>jn,projection:()=>Rn,rotate:()=>Ln,scale:()=>Dn,set:()=>zn,str:()=>Bn,sub:()=>Zn,subtract:()=>oe,translate:()=>Cn,transpose:()=>Nn});function kr(){var r=new P(9);return P!=Float32Array&&(r[1]=0,r[2]=0,r[3]=0,r[5]=0,r[6]=0,r[7]=0),r[0]=1,r[4]=1,r[8]=1,r}function bn(r,e){return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[4],r[4]=e[5],r[5]=e[6],r[6]=e[8],r[7]=e[9],r[8]=e[10],r}function An(r){var e=new P(9);return e[0]=r[0],e[1]=r[1],e[2]=r[2],e[3]=r[3],e[4]=r[4],e[5]=r[5],e[6]=r[6],e[7]=r[7],e[8]=r[8],e}function wn(r,e){return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r}function kn(r,e,n,a,t,s,i,h,o){var c=new P(9);return c[0]=r,c[1]=e,c[2]=n,c[3]=a,c[4]=t,c[5]=s,c[6]=i,c[7]=h,c[8]=o,c}function zn(r,e,n,a,t,s,i,h,o,c){return r[0]=e,r[1]=n,r[2]=a,r[3]=t,r[4]=s,r[5]=i,r[6]=h,r[7]=o,r[8]=c,r}function qn(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=1,r[5]=0,r[6]=0,r[7]=0,r[8]=1,r}function Nn(r,e){if(r===e){var n=e[1],a=e[2],t=e[5];r[1]=e[3],r[2]=e[6],r[3]=n,r[5]=e[7],r[6]=a,r[7]=t}else r[0]=e[0],r[1]=e[3],r[2]=e[6],r[3]=e[1],r[4]=e[4],r[5]=e[7],r[6]=e[2],r[7]=e[5],r[8]=e[8];return r}function Fn(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8],f=l*i-h*c,p=-l*s+h*o,v=c*s-i*o,m=n*f+a*p+t*v;return m?(m=1/m,r[0]=f*m,r[1]=(-l*a+t*c)*m,r[2]=(h*a-t*i)*m,r[3]=p*m,r[4]=(l*n-t*o)*m,r[5]=(-h*n+t*s)*m,r[6]=v*m,r[7]=(-c*n+a*o)*m,r[8]=(i*n-a*s)*m,r):null}function Sn(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8];return r[0]=i*l-h*c,r[1]=t*c-a*l,r[2]=a*h-t*i,r[3]=h*o-s*l,r[4]=n*l-t*o,r[5]=t*s-n*h,r[6]=s*c-i*o,r[7]=a*o-n*c,r[8]=n*i-a*s,r}function In(r){var e=r[0],n=r[1],a=r[2],t=r[3],s=r[4],i=r[5],h=r[6],o=r[7],c=r[8];return e*(c*s-i*o)+n*(-c*t+i*h)+a*(o*t-s*h)}function ie(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=e[8],p=n[0],v=n[1],m=n[2],d=n[3],y=n[4],u=n[5],x=n[6],M=n[7],b=n[8];return r[0]=p*a+v*i+m*c,r[1]=p*t+v*h+m*l,r[2]=p*s+v*o+m*f,r[3]=d*a+y*i+u*c,r[4]=d*t+y*h+u*l,r[5]=d*s+y*o+u*f,r[6]=x*a+M*i+b*c,r[7]=x*t+M*h+b*l,r[8]=x*s+M*o+b*f,r}function Cn(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=e[8],p=n[0],v=n[1];return r[0]=a,r[1]=t,r[2]=s,r[3]=i,r[4]=h,r[5]=o,r[6]=p*a+v*i+c,r[7]=p*t+v*h+l,r[8]=p*s+v*o+f,r}function Ln(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=e[8],p=Math.sin(n),v=Math.cos(n);return r[0]=v*a+p*i,r[1]=v*t+p*h,r[2]=v*s+p*o,r[3]=v*i-p*a,r[4]=v*h-p*t,r[5]=v*o-p*s,r[6]=c,r[7]=l,r[8]=f,r}function Dn(r,e,n){var a=n[0],t=n[1];return r[0]=a*e[0],r[1]=a*e[1],r[2]=a*e[2],r[3]=t*e[3],r[4]=t*e[4],r[5]=t*e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r}function En(r,e){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=1,r[5]=0,r[6]=e[0],r[7]=e[1],r[8]=1,r}function Tn(r,e){var n=Math.sin(e),a=Math.cos(e);return r[0]=a,r[1]=n,r[2]=0,r[3]=-n,r[4]=a,r[5]=0,r[6]=0,r[7]=0,r[8]=1,r}function Pn(r,e){return r[0]=e[0],r[1]=0,r[2]=0,r[3]=0,r[4]=e[1],r[5]=0,r[6]=0,r[7]=0,r[8]=1,r}function Vn(r,e){return r[0]=e[0],r[1]=e[1],r[2]=0,r[3]=e[2],r[4]=e[3],r[5]=0,r[6]=e[4],r[7]=e[5],r[8]=1,r}function On(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=n+n,h=a+a,o=t+t,c=n*i,l=a*i,f=a*h,p=t*i,v=t*h,m=t*o,d=s*i,y=s*h,u=s*o;return r[0]=1-f-m,r[3]=l-u,r[6]=p+y,r[1]=l+u,r[4]=1-c-m,r[7]=v-d,r[2]=p-y,r[5]=v+d,r[8]=1-c-f,r}function jn(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8],f=e[9],p=e[10],v=e[11],m=e[12],d=e[13],y=e[14],u=e[15],x=n*h-a*i,M=n*o-t*i,b=n*c-s*i,_=a*o-t*h,g=a*c-s*h,N=t*c-s*o,q=l*d-f*m,w=l*y-p*m,F=l*u-v*m,L=f*y-p*d,T=f*u-v*d,V=p*u-v*y,z=x*V-M*T+b*L+_*F-g*w+N*q;return z?(z=1/z,r[0]=(h*V-o*T+c*L)*z,r[1]=(o*F-i*V-c*w)*z,r[2]=(i*T-h*F+c*q)*z,r[3]=(t*T-a*V-s*L)*z,r[4]=(n*V-t*F+s*w)*z,r[5]=(a*F-n*T-s*q)*z,r[6]=(d*N-y*g+u*_)*z,r[7]=(y*b-m*N-u*M)*z,r[8]=(m*g-d*b+u*x)*z,r):null}function Rn(r,e,n){return r[0]=2/e,r[1]=0,r[2]=0,r[3]=0,r[4]=-2/n,r[5]=0,r[6]=-1,r[7]=1,r[8]=1,r}function Bn(r){return\"mat3(\"+r[0]+\", \"+r[1]+\", \"+r[2]+\", \"+r[3]+\", \"+r[4]+\", \"+r[5]+\", \"+r[6]+\", \"+r[7]+\", \"+r[8]+\")\"}function Un(r){return Math.hypot(r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8])}function Yn(r,e,n){return r[0]=e[0]+n[0],r[1]=e[1]+n[1],r[2]=e[2]+n[2],r[3]=e[3]+n[3],r[4]=e[4]+n[4],r[5]=e[5]+n[5],r[6]=e[6]+n[6],r[7]=e[7]+n[7],r[8]=e[8]+n[8],r}function oe(r,e,n){return r[0]=e[0]-n[0],r[1]=e[1]-n[1],r[2]=e[2]-n[2],r[3]=e[3]-n[3],r[4]=e[4]-n[4],r[5]=e[5]-n[5],r[6]=e[6]-n[6],r[7]=e[7]-n[7],r[8]=e[8]-n[8],r}function Gn(r,e,n){return r[0]=e[0]*n,r[1]=e[1]*n,r[2]=e[2]*n,r[3]=e[3]*n,r[4]=e[4]*n,r[5]=e[5]*n,r[6]=e[6]*n,r[7]=e[7]*n,r[8]=e[8]*n,r}function Qn(r,e,n,a){return r[0]=e[0]+n[0]*a,r[1]=e[1]+n[1]*a,r[2]=e[2]+n[2]*a,r[3]=e[3]+n[3]*a,r[4]=e[4]+n[4]*a,r[5]=e[5]+n[5]*a,r[6]=e[6]+n[6]*a,r[7]=e[7]+n[7]*a,r[8]=e[8]+n[8]*a,r}function $n(r,e){return r[0]===e[0]&&r[1]===e[1]&&r[2]===e[2]&&r[3]===e[3]&&r[4]===e[4]&&r[5]===e[5]&&r[6]===e[6]&&r[7]===e[7]&&r[8]===e[8]}function Hn(r,e){var n=r[0],a=r[1],t=r[2],s=r[3],i=r[4],h=r[5],o=r[6],c=r[7],l=r[8],f=e[0],p=e[1],v=e[2],m=e[3],d=e[4],y=e[5],u=e[6],x=e[7],M=e[8];return Math.abs(n-f)<=S*Math.max(1,Math.abs(n),Math.abs(f))&&Math.abs(a-p)<=S*Math.max(1,Math.abs(a),Math.abs(p))&&Math.abs(t-v)<=S*Math.max(1,Math.abs(t),Math.abs(v))&&Math.abs(s-m)<=S*Math.max(1,Math.abs(s),Math.abs(m))&&Math.abs(i-d)<=S*Math.max(1,Math.abs(i),Math.abs(d))&&Math.abs(h-y)<=S*Math.max(1,Math.abs(h),Math.abs(y))&&Math.abs(o-u)<=S*Math.max(1,Math.abs(o),Math.abs(u))&&Math.abs(c-x)<=S*Math.max(1,Math.abs(c),Math.abs(x))&&Math.abs(l-M)<=S*Math.max(1,Math.abs(l),Math.abs(M))}var Wn=ie,Zn=oe;var lr={};ur(lr,{add:()=>Da,adjoint:()=>ta,clone:()=>Kn,copy:()=>Jn,create:()=>Xn,determinant:()=>sa,equals:()=>Va,exactEquals:()=>Pa,frob:()=>La,fromQuat:()=>Aa,fromQuat2:()=>xa,fromRotation:()=>da,fromRotationTranslation:()=>le,fromRotationTranslationScale:()=>_a,fromRotationTranslationScaleOrigin:()=>ba,fromScaling:()=>ya,fromTranslation:()=>fa,fromValues:()=>ra,fromXRotation:()=>va,fromYRotation:()=>ma,fromZRotation:()=>ua,frustum:()=>wa,getRotation:()=>Ma,getScaling:()=>pe,getTranslation:()=>ga,identity:()=>he,invert:()=>aa,lookAt:()=>Sa,mul:()=>Oa,multiply:()=>ce,multiplyScalar:()=>Ea,multiplyScalarAndAdd:()=>Ta,ortho:()=>Na,orthoNO:()=>ye,orthoZO:()=>Fa,perspective:()=>ka,perspectiveFromFieldOfView:()=>qa,perspectiveNO:()=>fe,perspectiveZO:()=>za,rotate:()=>ha,rotateX:()=>ca,rotateY:()=>la,rotateZ:()=>pa,scale:()=>oa,set:()=>ea,str:()=>Ca,sub:()=>ja,subtract:()=>de,targetTo:()=>Ia,translate:()=>ia,transpose:()=>na});function Xn(){var r=new P(16);return P!=Float32Array&&(r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[11]=0,r[12]=0,r[13]=0,r[14]=0),r[0]=1,r[5]=1,r[10]=1,r[15]=1,r}function Kn(r){var e=new P(16);return e[0]=r[0],e[1]=r[1],e[2]=r[2],e[3]=r[3],e[4]=r[4],e[5]=r[5],e[6]=r[6],e[7]=r[7],e[8]=r[8],e[9]=r[9],e[10]=r[10],e[11]=r[11],e[12]=r[12],e[13]=r[13],e[14]=r[14],e[15]=r[15],e}function Jn(r,e){return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r[9]=e[9],r[10]=e[10],r[11]=e[11],r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15],r}function ra(r,e,n,a,t,s,i,h,o,c,l,f,p,v,m,d){var y=new P(16);return y[0]=r,y[1]=e,y[2]=n,y[3]=a,y[4]=t,y[5]=s,y[6]=i,y[7]=h,y[8]=o,y[9]=c,y[10]=l,y[11]=f,y[12]=p,y[13]=v,y[14]=m,y[15]=d,y}function ea(r,e,n,a,t,s,i,h,o,c,l,f,p,v,m,d,y){return r[0]=e,r[1]=n,r[2]=a,r[3]=t,r[4]=s,r[5]=i,r[6]=h,r[7]=o,r[8]=c,r[9]=l,r[10]=f,r[11]=p,r[12]=v,r[13]=m,r[14]=d,r[15]=y,r}function he(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=1,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=1,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function na(r,e){if(r===e){var n=e[1],a=e[2],t=e[3],s=e[6],i=e[7],h=e[11];r[1]=e[4],r[2]=e[8],r[3]=e[12],r[4]=n,r[6]=e[9],r[7]=e[13],r[8]=a,r[9]=s,r[11]=e[14],r[12]=t,r[13]=i,r[14]=h}else r[0]=e[0],r[1]=e[4],r[2]=e[8],r[3]=e[12],r[4]=e[1],r[5]=e[5],r[6]=e[9],r[7]=e[13],r[8]=e[2],r[9]=e[6],r[10]=e[10],r[11]=e[14],r[12]=e[3],r[13]=e[7],r[14]=e[11],r[15]=e[15];return r}function aa(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8],f=e[9],p=e[10],v=e[11],m=e[12],d=e[13],y=e[14],u=e[15],x=n*h-a*i,M=n*o-t*i,b=n*c-s*i,_=a*o-t*h,g=a*c-s*h,N=t*c-s*o,q=l*d-f*m,w=l*y-p*m,F=l*u-v*m,L=f*y-p*d,T=f*u-v*d,V=p*u-v*y,z=x*V-M*T+b*L+_*F-g*w+N*q;return z?(z=1/z,r[0]=(h*V-o*T+c*L)*z,r[1]=(t*T-a*V-s*L)*z,r[2]=(d*N-y*g+u*_)*z,r[3]=(p*g-f*N-v*_)*z,r[4]=(o*F-i*V-c*w)*z,r[5]=(n*V-t*F+s*w)*z,r[6]=(y*b-m*N-u*M)*z,r[7]=(l*N-p*b+v*M)*z,r[8]=(i*T-h*F+c*q)*z,r[9]=(a*F-n*T-s*q)*z,r[10]=(m*g-d*b+u*x)*z,r[11]=(f*b-l*g-v*x)*z,r[12]=(h*w-i*L-o*q)*z,r[13]=(n*L-a*w+t*q)*z,r[14]=(d*M-m*_-y*x)*z,r[15]=(l*_-f*M+p*x)*z,r):null}function ta(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8],f=e[9],p=e[10],v=e[11],m=e[12],d=e[13],y=e[14],u=e[15];return r[0]=h*(p*u-v*y)-f*(o*u-c*y)+d*(o*v-c*p),r[1]=-(a*(p*u-v*y)-f*(t*u-s*y)+d*(t*v-s*p)),r[2]=a*(o*u-c*y)-h*(t*u-s*y)+d*(t*c-s*o),r[3]=-(a*(o*v-c*p)-h*(t*v-s*p)+f*(t*c-s*o)),r[4]=-(i*(p*u-v*y)-l*(o*u-c*y)+m*(o*v-c*p)),r[5]=n*(p*u-v*y)-l*(t*u-s*y)+m*(t*v-s*p),r[6]=-(n*(o*u-c*y)-i*(t*u-s*y)+m*(t*c-s*o)),r[7]=n*(o*v-c*p)-i*(t*v-s*p)+l*(t*c-s*o),r[8]=i*(f*u-v*d)-l*(h*u-c*d)+m*(h*v-c*f),r[9]=-(n*(f*u-v*d)-l*(a*u-s*d)+m*(a*v-s*f)),r[10]=n*(h*u-c*d)-i*(a*u-s*d)+m*(a*c-s*h),r[11]=-(n*(h*v-c*f)-i*(a*v-s*f)+l*(a*c-s*h)),r[12]=-(i*(f*y-p*d)-l*(h*y-o*d)+m*(h*p-o*f)),r[13]=n*(f*y-p*d)-l*(a*y-t*d)+m*(a*p-t*f),r[14]=-(n*(h*y-o*d)-i*(a*y-t*d)+m*(a*o-t*h)),r[15]=n*(h*p-o*f)-i*(a*p-t*f)+l*(a*o-t*h),r}function sa(r){var e=r[0],n=r[1],a=r[2],t=r[3],s=r[4],i=r[5],h=r[6],o=r[7],c=r[8],l=r[9],f=r[10],p=r[11],v=r[12],m=r[13],d=r[14],y=r[15],u=e*i-n*s,x=e*h-a*s,M=e*o-t*s,b=n*h-a*i,_=n*o-t*i,g=a*o-t*h,N=c*m-l*v,q=c*d-f*v,w=c*y-p*v,F=l*d-f*m,L=l*y-p*m,T=f*y-p*d;return u*T-x*L+M*F+b*w-_*q+g*N}function ce(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=e[8],p=e[9],v=e[10],m=e[11],d=e[12],y=e[13],u=e[14],x=e[15],M=n[0],b=n[1],_=n[2],g=n[3];return r[0]=M*a+b*h+_*f+g*d,r[1]=M*t+b*o+_*p+g*y,r[2]=M*s+b*c+_*v+g*u,r[3]=M*i+b*l+_*m+g*x,M=n[4],b=n[5],_=n[6],g=n[7],r[4]=M*a+b*h+_*f+g*d,r[5]=M*t+b*o+_*p+g*y,r[6]=M*s+b*c+_*v+g*u,r[7]=M*i+b*l+_*m+g*x,M=n[8],b=n[9],_=n[10],g=n[11],r[8]=M*a+b*h+_*f+g*d,r[9]=M*t+b*o+_*p+g*y,r[10]=M*s+b*c+_*v+g*u,r[11]=M*i+b*l+_*m+g*x,M=n[12],b=n[13],_=n[14],g=n[15],r[12]=M*a+b*h+_*f+g*d,r[13]=M*t+b*o+_*p+g*y,r[14]=M*s+b*c+_*v+g*u,r[15]=M*i+b*l+_*m+g*x,r}function ia(r,e,n){var a=n[0],t=n[1],s=n[2],i,h,o,c,l,f,p,v,m,d,y,u;return e===r?(r[12]=e[0]*a+e[4]*t+e[8]*s+e[12],r[13]=e[1]*a+e[5]*t+e[9]*s+e[13],r[14]=e[2]*a+e[6]*t+e[10]*s+e[14],r[15]=e[3]*a+e[7]*t+e[11]*s+e[15]):(i=e[0],h=e[1],o=e[2],c=e[3],l=e[4],f=e[5],p=e[6],v=e[7],m=e[8],d=e[9],y=e[10],u=e[11],r[0]=i,r[1]=h,r[2]=o,r[3]=c,r[4]=l,r[5]=f,r[6]=p,r[7]=v,r[8]=m,r[9]=d,r[10]=y,r[11]=u,r[12]=i*a+l*t+m*s+e[12],r[13]=h*a+f*t+d*s+e[13],r[14]=o*a+p*t+y*s+e[14],r[15]=c*a+v*t+u*s+e[15]),r}function oa(r,e,n){var a=n[0],t=n[1],s=n[2];return r[0]=e[0]*a,r[1]=e[1]*a,r[2]=e[2]*a,r[3]=e[3]*a,r[4]=e[4]*t,r[5]=e[5]*t,r[6]=e[6]*t,r[7]=e[7]*t,r[8]=e[8]*s,r[9]=e[9]*s,r[10]=e[10]*s,r[11]=e[11]*s,r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15],r}function ha(r,e,n,a){var t=a[0],s=a[1],i=a[2],h=Math.hypot(t,s,i),o,c,l,f,p,v,m,d,y,u,x,M,b,_,g,N,q,w,F,L,T,V,z,Y;return h<S?null:(h=1/h,t*=h,s*=h,i*=h,o=Math.sin(n),c=Math.cos(n),l=1-c,f=e[0],p=e[1],v=e[2],m=e[3],d=e[4],y=e[5],u=e[6],x=e[7],M=e[8],b=e[9],_=e[10],g=e[11],N=t*t*l+c,q=s*t*l+i*o,w=i*t*l-s*o,F=t*s*l-i*o,L=s*s*l+c,T=i*s*l+t*o,V=t*i*l+s*o,z=s*i*l-t*o,Y=i*i*l+c,r[0]=f*N+d*q+M*w,r[1]=p*N+y*q+b*w,r[2]=v*N+u*q+_*w,r[3]=m*N+x*q+g*w,r[4]=f*F+d*L+M*T,r[5]=p*F+y*L+b*T,r[6]=v*F+u*L+_*T,r[7]=m*F+x*L+g*T,r[8]=f*V+d*z+M*Y,r[9]=p*V+y*z+b*Y,r[10]=v*V+u*z+_*Y,r[11]=m*V+x*z+g*Y,e!==r&&(r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15]),r)}function ca(r,e,n){var a=Math.sin(n),t=Math.cos(n),s=e[4],i=e[5],h=e[6],o=e[7],c=e[8],l=e[9],f=e[10],p=e[11];return e!==r&&(r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15]),r[4]=s*t+c*a,r[5]=i*t+l*a,r[6]=h*t+f*a,r[7]=o*t+p*a,r[8]=c*t-s*a,r[9]=l*t-i*a,r[10]=f*t-h*a,r[11]=p*t-o*a,r}function la(r,e,n){var a=Math.sin(n),t=Math.cos(n),s=e[0],i=e[1],h=e[2],o=e[3],c=e[8],l=e[9],f=e[10],p=e[11];return e!==r&&(r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15]),r[0]=s*t-c*a,r[1]=i*t-l*a,r[2]=h*t-f*a,r[3]=o*t-p*a,r[8]=s*a+c*t,r[9]=i*a+l*t,r[10]=h*a+f*t,r[11]=o*a+p*t,r}function pa(r,e,n){var a=Math.sin(n),t=Math.cos(n),s=e[0],i=e[1],h=e[2],o=e[3],c=e[4],l=e[5],f=e[6],p=e[7];return e!==r&&(r[8]=e[8],r[9]=e[9],r[10]=e[10],r[11]=e[11],r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15]),r[0]=s*t+c*a,r[1]=i*t+l*a,r[2]=h*t+f*a,r[3]=o*t+p*a,r[4]=c*t-s*a,r[5]=l*t-i*a,r[6]=f*t-h*a,r[7]=p*t-o*a,r}function fa(r,e){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=1,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=1,r[11]=0,r[12]=e[0],r[13]=e[1],r[14]=e[2],r[15]=1,r}function ya(r,e){return r[0]=e[0],r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=e[1],r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=e[2],r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function da(r,e,n){var a=n[0],t=n[1],s=n[2],i=Math.hypot(a,t,s),h,o,c;return i<S?null:(i=1/i,a*=i,t*=i,s*=i,h=Math.sin(e),o=Math.cos(e),c=1-o,r[0]=a*a*c+o,r[1]=t*a*c+s*h,r[2]=s*a*c-t*h,r[3]=0,r[4]=a*t*c-s*h,r[5]=t*t*c+o,r[6]=s*t*c+a*h,r[7]=0,r[8]=a*s*c+t*h,r[9]=t*s*c-a*h,r[10]=s*s*c+o,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r)}function va(r,e){var n=Math.sin(e),a=Math.cos(e);return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=a,r[6]=n,r[7]=0,r[8]=0,r[9]=-n,r[10]=a,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function ma(r,e){var n=Math.sin(e),a=Math.cos(e);return r[0]=a,r[1]=0,r[2]=-n,r[3]=0,r[4]=0,r[5]=1,r[6]=0,r[7]=0,r[8]=n,r[9]=0,r[10]=a,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function ua(r,e){var n=Math.sin(e),a=Math.cos(e);return r[0]=a,r[1]=n,r[2]=0,r[3]=0,r[4]=-n,r[5]=a,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=1,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function le(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=a+a,o=t+t,c=s+s,l=a*h,f=a*o,p=a*c,v=t*o,m=t*c,d=s*c,y=i*h,u=i*o,x=i*c;return r[0]=1-(v+d),r[1]=f+x,r[2]=p-u,r[3]=0,r[4]=f-x,r[5]=1-(l+d),r[6]=m+y,r[7]=0,r[8]=p+u,r[9]=m-y,r[10]=1-(l+v),r[11]=0,r[12]=n[0],r[13]=n[1],r[14]=n[2],r[15]=1,r}function xa(r,e){var n=new P(3),a=-e[0],t=-e[1],s=-e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=a*a+t*t+s*s+i*i;return f>0?(n[0]=(h*i+l*a+o*s-c*t)*2/f,n[1]=(o*i+l*t+c*a-h*s)*2/f,n[2]=(c*i+l*s+h*t-o*a)*2/f):(n[0]=(h*i+l*a+o*s-c*t)*2,n[1]=(o*i+l*t+c*a-h*s)*2,n[2]=(c*i+l*s+h*t-o*a)*2),le(r,e,n),r}function ga(r,e){return r[0]=e[12],r[1]=e[13],r[2]=e[14],r}function pe(r,e){var n=e[0],a=e[1],t=e[2],s=e[4],i=e[5],h=e[6],o=e[8],c=e[9],l=e[10];return r[0]=Math.hypot(n,a,t),r[1]=Math.hypot(s,i,h),r[2]=Math.hypot(o,c,l),r}function Ma(r,e){var n=new P(3);pe(n,e);var a=1/n[0],t=1/n[1],s=1/n[2],i=e[0]*a,h=e[1]*t,o=e[2]*s,c=e[4]*a,l=e[5]*t,f=e[6]*s,p=e[8]*a,v=e[9]*t,m=e[10]*s,d=i+l+m,y=0;return d>0?(y=Math.sqrt(d+1)*2,r[3]=.25*y,r[0]=(f-v)/y,r[1]=(p-o)/y,r[2]=(h-c)/y):i>l&&i>m?(y=Math.sqrt(1+i-l-m)*2,r[3]=(f-v)/y,r[0]=.25*y,r[1]=(h+c)/y,r[2]=(p+o)/y):l>m?(y=Math.sqrt(1+l-i-m)*2,r[3]=(p-o)/y,r[0]=(h+c)/y,r[1]=.25*y,r[2]=(f+v)/y):(y=Math.sqrt(1+m-i-l)*2,r[3]=(h-c)/y,r[0]=(p+o)/y,r[1]=(f+v)/y,r[2]=.25*y),r}function _a(r,e,n,a){var t=e[0],s=e[1],i=e[2],h=e[3],o=t+t,c=s+s,l=i+i,f=t*o,p=t*c,v=t*l,m=s*c,d=s*l,y=i*l,u=h*o,x=h*c,M=h*l,b=a[0],_=a[1],g=a[2];return r[0]=(1-(m+y))*b,r[1]=(p+M)*b,r[2]=(v-x)*b,r[3]=0,r[4]=(p-M)*_,r[5]=(1-(f+y))*_,r[6]=(d+u)*_,r[7]=0,r[8]=(v+x)*g,r[9]=(d-u)*g,r[10]=(1-(f+m))*g,r[11]=0,r[12]=n[0],r[13]=n[1],r[14]=n[2],r[15]=1,r}function ba(r,e,n,a,t){var s=e[0],i=e[1],h=e[2],o=e[3],c=s+s,l=i+i,f=h+h,p=s*c,v=s*l,m=s*f,d=i*l,y=i*f,u=h*f,x=o*c,M=o*l,b=o*f,_=a[0],g=a[1],N=a[2],q=t[0],w=t[1],F=t[2],L=(1-(d+u))*_,T=(v+b)*_,V=(m-M)*_,z=(v-b)*g,Y=(1-(p+u))*g,or=(y+x)*g,hr=(m+M)*N,Sr=(y-x)*N,Ir=(1-(p+d))*N;return r[0]=L,r[1]=T,r[2]=V,r[3]=0,r[4]=z,r[5]=Y,r[6]=or,r[7]=0,r[8]=hr,r[9]=Sr,r[10]=Ir,r[11]=0,r[12]=n[0]+q-(L*q+z*w+hr*F),r[13]=n[1]+w-(T*q+Y*w+Sr*F),r[14]=n[2]+F-(V*q+or*w+Ir*F),r[15]=1,r}function Aa(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=n+n,h=a+a,o=t+t,c=n*i,l=a*i,f=a*h,p=t*i,v=t*h,m=t*o,d=s*i,y=s*h,u=s*o;return r[0]=1-f-m,r[1]=l+u,r[2]=p-y,r[3]=0,r[4]=l-u,r[5]=1-c-m,r[6]=v+d,r[7]=0,r[8]=p+y,r[9]=v-d,r[10]=1-c-f,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function wa(r,e,n,a,t,s,i){var h=1/(n-e),o=1/(t-a),c=1/(s-i);return r[0]=s*2*h,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=s*2*o,r[6]=0,r[7]=0,r[8]=(n+e)*h,r[9]=(t+a)*o,r[10]=(i+s)*c,r[11]=-1,r[12]=0,r[13]=0,r[14]=i*s*2*c,r[15]=0,r}function fe(r,e,n,a,t){var s=1/Math.tan(e/2),i;return r[0]=s/n,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=s,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[11]=-1,r[12]=0,r[13]=0,r[15]=0,t!=null&&t!==1/0?(i=1/(a-t),r[10]=(t+a)*i,r[14]=2*t*a*i):(r[10]=-1,r[14]=-2*a),r}var ka=fe;function za(r,e,n,a,t){var s=1/Math.tan(e/2),i;return r[0]=s/n,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=s,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[11]=-1,r[12]=0,r[13]=0,r[15]=0,t!=null&&t!==1/0?(i=1/(a-t),r[10]=t*i,r[14]=t*a*i):(r[10]=-1,r[14]=-a),r}function qa(r,e,n,a){var t=Math.tan(e.upDegrees*Math.PI/180),s=Math.tan(e.downDegrees*Math.PI/180),i=Math.tan(e.leftDegrees*Math.PI/180),h=Math.tan(e.rightDegrees*Math.PI/180),o=2/(i+h),c=2/(t+s);return r[0]=o,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=c,r[6]=0,r[7]=0,r[8]=-((i-h)*o*.5),r[9]=(t-s)*c*.5,r[10]=a/(n-a),r[11]=-1,r[12]=0,r[13]=0,r[14]=a*n/(n-a),r[15]=0,r}function ye(r,e,n,a,t,s,i){var h=1/(e-n),o=1/(a-t),c=1/(s-i);return r[0]=-2*h,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=-2*o,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=2*c,r[11]=0,r[12]=(e+n)*h,r[13]=(t+a)*o,r[14]=(i+s)*c,r[15]=1,r}var Na=ye;function Fa(r,e,n,a,t,s,i){var h=1/(e-n),o=1/(a-t),c=1/(s-i);return r[0]=-2*h,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=-2*o,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=c,r[11]=0,r[12]=(e+n)*h,r[13]=(t+a)*o,r[14]=s*c,r[15]=1,r}function Sa(r,e,n,a){var t,s,i,h,o,c,l,f,p,v,m=e[0],d=e[1],y=e[2],u=a[0],x=a[1],M=a[2],b=n[0],_=n[1],g=n[2];return Math.abs(m-b)<S&&Math.abs(d-_)<S&&Math.abs(y-g)<S?he(r):(l=m-b,f=d-_,p=y-g,v=1/Math.hypot(l,f,p),l*=v,f*=v,p*=v,t=x*p-M*f,s=M*l-u*p,i=u*f-x*l,v=Math.hypot(t,s,i),v?(v=1/v,t*=v,s*=v,i*=v):(t=0,s=0,i=0),h=f*i-p*s,o=p*t-l*i,c=l*s-f*t,v=Math.hypot(h,o,c),v?(v=1/v,h*=v,o*=v,c*=v):(h=0,o=0,c=0),r[0]=t,r[1]=h,r[2]=l,r[3]=0,r[4]=s,r[5]=o,r[6]=f,r[7]=0,r[8]=i,r[9]=c,r[10]=p,r[11]=0,r[12]=-(t*m+s*d+i*y),r[13]=-(h*m+o*d+c*y),r[14]=-(l*m+f*d+p*y),r[15]=1,r)}function Ia(r,e,n,a){var t=e[0],s=e[1],i=e[2],h=a[0],o=a[1],c=a[2],l=t-n[0],f=s-n[1],p=i-n[2],v=l*l+f*f+p*p;v>0&&(v=1/Math.sqrt(v),l*=v,f*=v,p*=v);var m=o*p-c*f,d=c*l-h*p,y=h*f-o*l;return v=m*m+d*d+y*y,v>0&&(v=1/Math.sqrt(v),m*=v,d*=v,y*=v),r[0]=m,r[1]=d,r[2]=y,r[3]=0,r[4]=f*y-p*d,r[5]=p*m-l*y,r[6]=l*d-f*m,r[7]=0,r[8]=l,r[9]=f,r[10]=p,r[11]=0,r[12]=t,r[13]=s,r[14]=i,r[15]=1,r}function Ca(r){return\"mat4(\"+r[0]+\", \"+r[1]+\", \"+r[2]+\", \"+r[3]+\", \"+r[4]+\", \"+r[5]+\", \"+r[6]+\", \"+r[7]+\", \"+r[8]+\", \"+r[9]+\", \"+r[10]+\", \"+r[11]+\", \"+r[12]+\", \"+r[13]+\", \"+r[14]+\", \"+r[15]+\")\"}function La(r){return Math.hypot(r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8],r[9],r[10],r[11],r[12],r[13],r[14],r[15])}function Da(r,e,n){return r[0]=e[0]+n[0],r[1]=e[1]+n[1],r[2]=e[2]+n[2],r[3]=e[3]+n[3],r[4]=e[4]+n[4],r[5]=e[5]+n[5],r[6]=e[6]+n[6],r[7]=e[7]+n[7],r[8]=e[8]+n[8],r[9]=e[9]+n[9],r[10]=e[10]+n[10],r[11]=e[11]+n[11],r[12]=e[12]+n[12],r[13]=e[13]+n[13],r[14]=e[14]+n[14],r[15]=e[15]+n[15],r}function de(r,e,n){return r[0]=e[0]-n[0],r[1]=e[1]-n[1],r[2]=e[2]-n[2],r[3]=e[3]-n[3],r[4]=e[4]-n[4],r[5]=e[5]-n[5],r[6]=e[6]-n[6],r[7]=e[7]-n[7],r[8]=e[8]-n[8],r[9]=e[9]-n[9],r[10]=e[10]-n[10],r[11]=e[11]-n[11],r[12]=e[12]-n[12],r[13]=e[13]-n[13],r[14]=e[14]-n[14],r[15]=e[15]-n[15],r}function Ea(r,e,n){return r[0]=e[0]*n,r[1]=e[1]*n,r[2]=e[2]*n,r[3]=e[3]*n,r[4]=e[4]*n,r[5]=e[5]*n,r[6]=e[6]*n,r[7]=e[7]*n,r[8]=e[8]*n,r[9]=e[9]*n,r[10]=e[10]*n,r[11]=e[11]*n,r[12]=e[12]*n,r[13]=e[13]*n,r[14]=e[14]*n,r[15]=e[15]*n,r}function Ta(r,e,n,a){return r[0]=e[0]+n[0]*a,r[1]=e[1]+n[1]*a,r[2]=e[2]+n[2]*a,r[3]=e[3]+n[3]*a,r[4]=e[4]+n[4]*a,r[5]=e[5]+n[5]*a,r[6]=e[6]+n[6]*a,r[7]=e[7]+n[7]*a,r[8]=e[8]+n[8]*a,r[9]=e[9]+n[9]*a,r[10]=e[10]+n[10]*a,r[11]=e[11]+n[11]*a,r[12]=e[12]+n[12]*a,r[13]=e[13]+n[13]*a,r[14]=e[14]+n[14]*a,r[15]=e[15]+n[15]*a,r}function Pa(r,e){return r[0]===e[0]&&r[1]===e[1]&&r[2]===e[2]&&r[3]===e[3]&&r[4]===e[4]&&r[5]===e[5]&&r[6]===e[6]&&r[7]===e[7]&&r[8]===e[8]&&r[9]===e[9]&&r[10]===e[10]&&r[11]===e[11]&&r[12]===e[12]&&r[13]===e[13]&&r[14]===e[14]&&r[15]===e[15]}function Va(r,e){var n=r[0],a=r[1],t=r[2],s=r[3],i=r[4],h=r[5],o=r[6],c=r[7],l=r[8],f=r[9],p=r[10],v=r[11],m=r[12],d=r[13],y=r[14],u=r[15],x=e[0],M=e[1],b=e[2],_=e[3],g=e[4],N=e[5],q=e[6],w=e[7],F=e[8],L=e[9],T=e[10],V=e[11],z=e[12],Y=e[13],or=e[14],hr=e[15];return Math.abs(n-x)<=S*Math.max(1,Math.abs(n),Math.abs(x))&&Math.abs(a-M)<=S*Math.max(1,Math.abs(a),Math.abs(M))&&Math.abs(t-b)<=S*Math.max(1,Math.abs(t),Math.abs(b))&&Math.abs(s-_)<=S*Math.max(1,Math.abs(s),Math.abs(_))&&Math.abs(i-g)<=S*Math.max(1,Math.abs(i),Math.abs(g))&&Math.abs(h-N)<=S*Math.max(1,Math.abs(h),Math.abs(N))&&Math.abs(o-q)<=S*Math.max(1,Math.abs(o),Math.abs(q))&&Math.abs(c-w)<=S*Math.max(1,Math.abs(c),Math.abs(w))&&Math.abs(l-F)<=S*Math.max(1,Math.abs(l),Math.abs(F))&&Math.abs(f-L)<=S*Math.max(1,Math.abs(f),Math.abs(L))&&Math.abs(p-T)<=S*Math.max(1,Math.abs(p),Math.abs(T))&&Math.abs(v-V)<=S*Math.max(1,Math.abs(v),Math.abs(V))&&Math.abs(m-z)<=S*Math.max(1,Math.abs(m),Math.abs(z))&&Math.abs(d-Y)<=S*Math.max(1,Math.abs(d),Math.abs(Y))&&Math.abs(y-or)<=S*Math.max(1,Math.abs(y),Math.abs(or))&&Math.abs(u-hr)<=S*Math.max(1,Math.abs(u),Math.abs(hr))}var Oa=ce,ja=de;var pr={};ur(pr,{add:()=>ht,calculateW:()=>Xa,clone:()=>tt,conjugate:()=>et,copy:()=>it,create:()=>Nr,dot:()=>Pe,equals:()=>dt,exactEquals:()=>yt,exp:()=>Le,fromEuler:()=>nt,fromMat3:()=>Ee,fromValues:()=>st,getAngle:()=>$a,getAxisAngle:()=>Qa,identity:()=>Ga,invert:()=>rt,len:()=>pt,length:()=>Ve,lerp:()=>lt,ln:()=>De,mul:()=>ct,multiply:()=>Ce,normalize:()=>Fr,pow:()=>Ka,random:()=>Ja,rotateX:()=>Ha,rotateY:()=>Wa,rotateZ:()=>Za,rotationTo:()=>vt,scale:()=>Te,set:()=>ot,setAxes:()=>ut,setAxisAngle:()=>Ie,slerp:()=>mr,sqlerp:()=>mt,sqrLen:()=>ft,squaredLength:()=>Oe,str:()=>at});function zr(){var r=new P(3);return P!=Float32Array&&(r[0]=0,r[1]=0,r[2]=0),r}function Ra(r){var e=r[0],n=r[1],a=r[2];return Math.hypot(e,n,a)}function qr(r,e,n){var a=new P(3);return a[0]=r,a[1]=e,a[2]=n,a}function ve(r,e){var n=e[0],a=e[1],t=e[2],s=n*n+a*a+t*t;return s>0&&(s=1/Math.sqrt(s)),r[0]=e[0]*s,r[1]=e[1]*s,r[2]=e[2]*s,r}function me(r,e){return r[0]*e[0]+r[1]*e[1]+r[2]*e[2]}function vr(r,e,n){var a=e[0],t=e[1],s=e[2],i=n[0],h=n[1],o=n[2];return r[0]=t*o-s*h,r[1]=s*i-a*o,r[2]=a*h-t*i,r}var ue=Ra;var Zt=function(){var r=zr();return function(e,n,a,t,s,i){var h,o;for(n||(n=3),a||(a=0),t?o=Math.min(t*n+a,e.length):o=e.length,h=a;h<o;h+=n)r[0]=e[h],r[1]=e[h+1],r[2]=e[h+2],s(r,r,i),e[h]=r[0],e[h+1]=r[1],e[h+2]=r[2];return e}}();function Ua(){var r=new P(4);return P!=Float32Array&&(r[0]=0,r[1]=0,r[2]=0,r[3]=0),r}function xe(r){var e=new P(4);return e[0]=r[0],e[1]=r[1],e[2]=r[2],e[3]=r[3],e}function ge(r,e,n,a){var t=new P(4);return t[0]=r,t[1]=e,t[2]=n,t[3]=a,t}function Me(r,e){return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r}function _e(r,e,n,a,t){return r[0]=e,r[1]=n,r[2]=a,r[3]=t,r}function be(r,e,n){return r[0]=e[0]+n[0],r[1]=e[1]+n[1],r[2]=e[2]+n[2],r[3]=e[3]+n[3],r}function Ae(r,e,n){return r[0]=e[0]*n,r[1]=e[1]*n,r[2]=e[2]*n,r[3]=e[3]*n,r}function we(r){var e=r[0],n=r[1],a=r[2],t=r[3];return Math.hypot(e,n,a,t)}function ke(r){var e=r[0],n=r[1],a=r[2],t=r[3];return e*e+n*n+a*a+t*t}function ze(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=n*n+a*a+t*t+s*s;return i>0&&(i=1/Math.sqrt(i)),r[0]=n*i,r[1]=a*i,r[2]=t*i,r[3]=s*i,r}function qe(r,e){return r[0]*e[0]+r[1]*e[1]+r[2]*e[2]+r[3]*e[3]}function Ne(r,e,n,a){var t=e[0],s=e[1],i=e[2],h=e[3];return r[0]=t+a*(n[0]-t),r[1]=s+a*(n[1]-s),r[2]=i+a*(n[2]-i),r[3]=h+a*(n[3]-h),r}function Fe(r,e){return r[0]===e[0]&&r[1]===e[1]&&r[2]===e[2]&&r[3]===e[3]}function Se(r,e){var n=r[0],a=r[1],t=r[2],s=r[3],i=e[0],h=e[1],o=e[2],c=e[3];return Math.abs(n-i)<=S*Math.max(1,Math.abs(n),Math.abs(i))&&Math.abs(a-h)<=S*Math.max(1,Math.abs(a),Math.abs(h))&&Math.abs(t-o)<=S*Math.max(1,Math.abs(t),Math.abs(o))&&Math.abs(s-c)<=S*Math.max(1,Math.abs(s),Math.abs(c))}var Xt=function(){var r=Ua();return function(e,n,a,t,s,i){var h,o;for(n||(n=4),a||(a=0),t?o=Math.min(t*n+a,e.length):o=e.length,h=a;h<o;h+=n)r[0]=e[h],r[1]=e[h+1],r[2]=e[h+2],r[3]=e[h+3],s(r,r,i),e[h]=r[0],e[h+1]=r[1],e[h+2]=r[2],e[h+3]=r[3];return e}}();function Nr(){var r=new P(4);return P!=Float32Array&&(r[0]=0,r[1]=0,r[2]=0),r[3]=1,r}function Ga(r){return r[0]=0,r[1]=0,r[2]=0,r[3]=1,r}function Ie(r,e,n){n=n*.5;var a=Math.sin(n);return r[0]=a*e[0],r[1]=a*e[1],r[2]=a*e[2],r[3]=Math.cos(n),r}function Qa(r,e){var n=Math.acos(e[3])*2,a=Math.sin(n/2);return a>S?(r[0]=e[0]/a,r[1]=e[1]/a,r[2]=e[2]/a):(r[0]=1,r[1]=0,r[2]=0),n}function $a(r,e){var n=Pe(r,e);return Math.acos(2*n*n-1)}function Ce(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=n[0],o=n[1],c=n[2],l=n[3];return r[0]=a*l+i*h+t*c-s*o,r[1]=t*l+i*o+s*h-a*c,r[2]=s*l+i*c+a*o-t*h,r[3]=i*l-a*h-t*o-s*c,r}function Ha(r,e,n){n*=.5;var a=e[0],t=e[1],s=e[2],i=e[3],h=Math.sin(n),o=Math.cos(n);return r[0]=a*o+i*h,r[1]=t*o+s*h,r[2]=s*o-t*h,r[3]=i*o-a*h,r}function Wa(r,e,n){n*=.5;var a=e[0],t=e[1],s=e[2],i=e[3],h=Math.sin(n),o=Math.cos(n);return r[0]=a*o-s*h,r[1]=t*o+i*h,r[2]=s*o+a*h,r[3]=i*o-t*h,r}function Za(r,e,n){n*=.5;var a=e[0],t=e[1],s=e[2],i=e[3],h=Math.sin(n),o=Math.cos(n);return r[0]=a*o+t*h,r[1]=t*o-a*h,r[2]=s*o+i*h,r[3]=i*o-s*h,r}function Xa(r,e){var n=e[0],a=e[1],t=e[2];return r[0]=n,r[1]=a,r[2]=t,r[3]=Math.sqrt(Math.abs(1-n*n-a*a-t*t)),r}function Le(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=Math.sqrt(n*n+a*a+t*t),h=Math.exp(s),o=i>0?h*Math.sin(i)/i:0;return r[0]=n*o,r[1]=a*o,r[2]=t*o,r[3]=h*Math.cos(i),r}function De(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=Math.sqrt(n*n+a*a+t*t),h=i>0?Math.atan2(i,s)/i:0;return r[0]=n*h,r[1]=a*h,r[2]=t*h,r[3]=.5*Math.log(n*n+a*a+t*t+s*s),r}function Ka(r,e,n){return De(r,e),Te(r,r,n),Le(r,r),r}function mr(r,e,n,a){var t=e[0],s=e[1],i=e[2],h=e[3],o=n[0],c=n[1],l=n[2],f=n[3],p,v,m,d,y;return v=t*o+s*c+i*l+h*f,v<0&&(v=-v,o=-o,c=-c,l=-l,f=-f),1-v>S?(p=Math.acos(v),m=Math.sin(p),d=Math.sin((1-a)*p)/m,y=Math.sin(a*p)/m):(d=1-a,y=a),r[0]=d*t+y*o,r[1]=d*s+y*c,r[2]=d*i+y*l,r[3]=d*h+y*f,r}function Ja(r){var e=ir(),n=ir(),a=ir(),t=Math.sqrt(1-e),s=Math.sqrt(e);return r[0]=t*Math.sin(2*Math.PI*n),r[1]=t*Math.cos(2*Math.PI*n),r[2]=s*Math.sin(2*Math.PI*a),r[3]=s*Math.cos(2*Math.PI*a),r}function rt(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=n*n+a*a+t*t+s*s,h=i?1/i:0;return r[0]=-n*h,r[1]=-a*h,r[2]=-t*h,r[3]=s*h,r}function et(r,e){return r[0]=-e[0],r[1]=-e[1],r[2]=-e[2],r[3]=e[3],r}function Ee(r,e){var n=e[0]+e[4]+e[8],a;if(n>0)a=Math.sqrt(n+1),r[3]=.5*a,a=.5/a,r[0]=(e[5]-e[7])*a,r[1]=(e[6]-e[2])*a,r[2]=(e[1]-e[3])*a;else{var t=0;e[4]>e[0]&&(t=1),e[8]>e[t*3+t]&&(t=2);var s=(t+1)%3,i=(t+2)%3;a=Math.sqrt(e[t*3+t]-e[s*3+s]-e[i*3+i]+1),r[t]=.5*a,a=.5/a,r[3]=(e[s*3+i]-e[i*3+s])*a,r[s]=(e[s*3+t]+e[t*3+s])*a,r[i]=(e[i*3+t]+e[t*3+i])*a}return r}function nt(r,e,n,a){var t=.5*Math.PI/180;e*=t,n*=t,a*=t;var s=Math.sin(e),i=Math.cos(e),h=Math.sin(n),o=Math.cos(n),c=Math.sin(a),l=Math.cos(a);return r[0]=s*o*l-i*h*c,r[1]=i*h*l+s*o*c,r[2]=i*o*c-s*h*l,r[3]=i*o*l+s*h*c,r}function at(r){return\"quat(\"+r[0]+\", \"+r[1]+\", \"+r[2]+\", \"+r[3]+\")\"}var tt=xe,st=ge,it=Me,ot=_e,ht=be,ct=Ce,Te=Ae,Pe=qe,lt=Ne,Ve=we,pt=Ve,Oe=ke,ft=Oe,Fr=ze,yt=Fe,dt=Se,vt=function(){var r=zr(),e=qr(1,0,0),n=qr(0,1,0);return function(a,t,s){var i=me(t,s);return i<-.999999?(vr(r,e,t),ue(r)<1e-6&&vr(r,n,t),ve(r,r),Ie(a,r,Math.PI),a):i>.999999?(a[0]=0,a[1]=0,a[2]=0,a[3]=1,a):(vr(r,t,s),a[0]=r[0],a[1]=r[1],a[2]=r[2],a[3]=1+i,Fr(a,a))}}(),mt=function(){var r=Nr(),e=Nr();return function(n,a,t,s,i,h){return mr(r,a,i,h),mr(e,t,s,h),mr(n,r,e,2*h*(1-h)),n}}(),ut=function(){var r=kr();return function(e,n,a,t){return r[0]=a[0],r[3]=a[1],r[6]=a[2],r[1]=t[0],r[4]=t[1],r[7]=t[2],r[2]=-n[0],r[5]=-n[1],r[8]=-n[2],Fr(e,Ee(e,r))}}();var je={xyz:3,color:3,opacity:1,scaling:3,quaternion:4,harmonics:3},ar=class{constructor(e){this.version=\"\";this._buffer=e}get buffer(){return this._buffer}get decoded(){return this._decoded||(this._decoded=this.decodeBuffer()),this._decoded}get colorsA(){let e=.28209479177387814,n=this.decoded.color.denormDequant(),a=this.decoded.opacity.denormDequant(),t=(0,nr.default)(new Float32Array(n.shape[0]*4),[n.shape[0],4]);return R.mulseq(n,e),R.addseq(n,.5),R.mulseq(n,255),R.maxseq(n,0),R.minseq(n,255),this.version===\"\"&&(R.negeq(a),R.expeq(a),R.addseq(a,1),R.recipeq(a),R.mulseq(a,255)),R.assign(t.hi(n.shape[0],3).lo(0,0),n),R.assign(t.hi(n.shape[0],4).lo(0,3),a),(0,nr.default)(new Uint8Array(t.data),[n.shape[0],4]).data}get nsplats(){return this.decoded.nsplats}getSplatCount(){return this.decoded.nsplats}get precomputedCovarianceBufferData(){return this._precomputedCovarianceBufferData}decodeBuffer(){let{splatCount:e,chunkCount:n,chunkSize:a,typeChunks:t,vertexData:s,propertiesDesc:i,version:h}=this.decodeHeader();this.version=h;let o={xyz:i.xyz.compressionMethod,color:i.color.compressionMethod,opacity:i.opacity.compressionMethod,scaling:i.scaling.compressionMethod,quaternion:i.quaternion.compressionMethod,chunkSize:a};i.harmonics_0&&(o.harmonics=i.harmonics_0.compressionMethod);let c=s.byteOffset,l=Array(Object.keys(i).length);for(let x in i)l[i[x].index]={name:x,method:i[x].compressionMethod};let f=n*2*4,p=c,v=t===\"dynamic\"?n*2:0,m,d=!1;if(v>0){let x=new Uint16Array(s.buffer.slice(p,p+v));p+=v,m=Array.from(x),d=!0}let y={};for(let x of l){let M=0,b=!0;if(x.method===\"norm8x\")M=e*1*je[x.name];else if(x.method===\"norm11\")M=e*4;else if(x.method===\"norm565\")M=e*2;else throw b=!1,new Error(`Not Implemented format: ${x.method}`);let _;if(b){let q=s.buffer.slice(p,p+f);_=(0,nr.default)(new Float32Array(q),[n,2]),p+=f}else throw new Error(\"loading chunk byt hasnot minmax!\");let g=s.buffer.slice(p,p+M);p+=M;let N;if(x.method===\"norm8x\")N=(0,nr.default)(new Uint8Array(g),[e,je[x.name]]);else if(x.method===\"norm11\")N=(0,nr.default)(new Uint32Array(g));else if(x.method===\"norm565\")N=(0,nr.default)(new Uint16Array(g));else throw new Error(`Not Implemented format: ${x.method}`);y[x.name]=new E(N,_,a,x.method,m,d)}let u=[];for(let x=0;x<15;x++){let M=y[`harmonics_${x}`];M&&(u.push(M),delete y[`harmonics_${x}`])}return u.length>0&&(y.harmonics=u),new X(o,y.xyz,y.scaling,y.color,y.opacity,y.quaternion,y.harmonics,m)}buildPreComputedBuffers(){let a=this.decoded,t=a.nsplats,s=new ArrayBuffer(24*t),i=new Float32Array(s),h=a.scaling.denormDequant(),o=a.quaternion.denormDequant(),c=pr.create(),l=W.create(),f=W.create(),p=W.create(),v=lr.create();for(let m=0;m<t;m++){lr.fromScaling(v,[Math.exp(h.get(m,0)),Math.exp(h.get(m,1)),Math.exp(h.get(m,2))]),W.fromMat4(f,v),pr.set(c,o.get(m,0),o.get(m,1),o.get(m,2),o.get(m,3)),W.fromQuat(l,c),W.multiply(p,l,f);let d=p;i[6*m]=d[0]*d[0]+d[3]*d[3]+d[6]*d[6],i[6*m+1]=d[0]*d[1]+d[3]*d[4]+d[6]*d[7],i[6*m+2]=d[0]*d[2]+d[3]*d[5]+d[6]*d[8],i[6*m+3]=d[1]*d[1]+d[4]*d[4]+d[7]*d[7],i[6*m+4]=d[1]*d[2]+d[4]*d[5]+d[7]*d[8],i[6*m+5]=d[2]*d[2]+d[5]*d[5]+d[8]*d[8]}this._precomputedCovarianceBufferData=s}decodeHeader(){let e=this._buffer,n=new TextDecoder,a=0,t=\"\",s=100;for(;;){if(a+s>=e.byteLength)throw new Error(\"End of file reached while searching for end of header\");let y=new Uint8Array(e,a,s);t+=n.decode(y),a+=s;let u=a-s*2,x=new Uint8Array(e,Math.max(0,u),u>=0?s*2:s);if(n.decode(x).includes(\"end_header\"))break}let i=t.split(`\n`),h=0,o=0,c=0,l=0,f=\"\",p=\"\",v={};for(let y=0;y<i.length;y++){let u=i[y].trim();if(u.startsWith(\"version\"))p=u.split(\" \")[1]??\"\";else if(u.startsWith(\"element vertex\")){let x=u.match(/\\d+/);x&&(h=parseInt(x[0]))}else if(u.startsWith(\"property\")){let x=u.match(/(\\w+)\\s+(\\w+)\\s+(\\w+)/);if(x){let M=x[2],b=x[3];v[M]={compressionMethod:b,index:l},l++}}else if(u.startsWith(\"element chunks\")){let x=u.match(/\\d+/);x&&(o=parseInt(x[0]))}else if(u.startsWith(\"element chunkSize\")){let x=u.match(/\\d+/);x&&(c=parseInt(x[0]))}else if(u.startsWith(\"element typeChunks\")){let x=u.match(/(\\w+)\\s+(\\w+)\\s+(\\w+)/);x&&(f=x[3])}else if(u===\"end_header\")break}let m=t.indexOf(\"end_header\")+10+1,d=new DataView(e,m);return{splatCount:h,chunkCount:o,chunkSize:c,typeChunks:f,vertexData:d,propertiesDesc:v,version:p}}pruneSplats(e){let a=this.decodeBuffer().pruneSplats(e);return ar.fromCompressedGaussianSplats(a,this.version)}static fromCompressedGaussianSplats(e,n){let a=e.xyz.length,t=e.xyz.nchunks,s=`gspline\nversion ${n}\nelement vertex ${a}\nelement chunks ${t}\nelement chunkSize ${e.chunkSize}\nelement typeChunks ${e.isDynamicChunks?\"dynamic\":\"static\"}\nproperty xyz ${e.xyz.method}\nproperty color ${e.color.method}\nproperty opacity ${e.opacity.method}\nproperty scaling ${e.scaling.method}\nproperty quaternion ${e.quaternion.method}`;if(e.harmonics&&e.harmonics.length>0)for(let F=0;F<e.harmonics.length;F++)s=`${s}\nproperty harmonics_${F} ${e.harmonics[F].method}`;s=`${s}\nend_header\n`;let h=new TextEncoder().encode(s),o=t*2*4,c=e.xyz.quantized.data.buffer.byteLength,l=e.xyz instanceof E?o:0,f=e.color.quantized.data.buffer.byteLength,p=e.color instanceof E?o:0,v=e.opacity.quantized.data.buffer.byteLength,m=e.opacity instanceof E?o:0,d=e.scaling.quantized.data.buffer.byteLength,y=e.scaling instanceof E?o:0,u=e.quaternion.quantized.data.buffer.byteLength,x=e.quaternion instanceof E?o:0,M=e.variableChunkSize?Uint16Array.from(e.variableChunkSize):void 0,b=M?M.byteLength:0,_=h.byteLength+b+c+l+f+p+v+m+d+y+u+x,g=0,N=0;if(e.harmonics&&e.harmonics.length>0)for(let F=0;F<e.harmonics.length;F++)g+=e.harmonics[F].quantized.data.buffer.byteLength,N+=e.harmonics[F]instanceof E?o:0;g=0,N=0,_+=g+N;let q=new Uint8Array(_),w=0;if(q.set(h,w),w+=h.byteLength,b>0&&(q.set(new Uint8Array(M.buffer),w),w+=b),e.xyz instanceof E&&(q.set(new Uint8Array(e.xyz.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.xyz.quantized.data.buffer),w),w+=c,e.color instanceof E&&(q.set(new Uint8Array(e.color.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.color.quantized.data.buffer),w),w+=f,e.opacity instanceof E&&(q.set(new Uint8Array(e.opacity.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.opacity.quantized.data.buffer),w),w+=v,e.scaling instanceof E&&(q.set(new Uint8Array(e.scaling.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.scaling.quantized.data.buffer),w),w+=d,e.quaternion instanceof E&&(q.set(new Uint8Array(e.quaternion.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.quaternion.quantized.data.buffer),w),w+=u,g>0&&e.harmonics&&e.harmonics.length>0)for(let F=0;F<e.harmonics.length;F++){let L=e.harmonics[F];L instanceof E&&(q.set(new Uint8Array(L.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(L.quantized.data.buffer),w),w+=L.quantized.data.byteLength}return new ar(q.buffer)}};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../node_modules/@splinetool/runtime/build/gaussian-splat-compression.js\n"));

/***/ })

}]);
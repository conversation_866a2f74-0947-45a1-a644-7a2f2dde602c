import React from 'react';

export default function AdvancedVisualization() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-purple-600 via-pink-500 to-red-500 text-white p-10">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .card {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    margin: 20px 0;
                    max-width: 600px;
                    text-align: left;
                }

                .image-placeholder {
                    width: 200px;
                    height: 150px;
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px dashed #fff;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 1rem;
                    margin: 16px;
                    flex-shrink: 0; /* Ensures image placeholder doesn't shrink */
                }

                .content-row {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 20px; /* Adds space between cards and placeholders */
                    flex-wrap: wrap; /* Ensures responsiveness on smaller screens */
                }
                `}
            </style>

            <h1 className="text-5xl font-bold mb-8">Advanced Visualization</h1>
            <p className="text-lg text-center max-w-3xl mb-12">
                Explore advanced visualization techniques like <span className="highlight">scatter plots</span>, <span className="highlight">heatmaps</span>, and <span className="highlight">interactive dashboards</span> to uncover deeper insights.
            </p>

            {/* Example 1: Scatter Plots */}
            <div className="content-row">
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">🔵 Scatter Plots</h2>
                    <p className="text-lg">
                        Scatter plots show relationships between two variables. For instance:
                        <br />
                        - Examining the correlation between study hours and test scores.<br />
                        - Understanding the relationship between advertising spend and sales.
                    </p>
                </div>
                <img
                    src="/scatter_plot_example.jpg" /* Replace with actual image path */
                    alt="Scatter Plot Example"
                    className="image-placeholder"
                />
            </div>

            {/* Example 2: Heatmaps */}
            <div className="content-row">
                <img
                    src="/heatmap_example.jpg" /* Replace with actual image path */
                    alt="Heatmap Example"
                    className="image-placeholder"
                />
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">🔥 Heatmaps</h2>
                    <p className="text-lg">
                        Heatmaps represent data density or intensity using colors. For example:
                        <br />
                        - Visualizing website user clicks to understand popular sections.<br />
                        - Displaying temperature variations across a geographical region.
                    </p>
                </div>
            </div>

            {/* Example 3: Interactive Dashboards */}
            <div className="content-row">
                <div className="card">
                    <h2 className="text-3xl font-bold mb-4">📊 Dashboards</h2>
                    <p className="text-lg">
                        Dashboards combine multiple visualizations for real-time data insights. For instance:
                        <br />
                        - Monitoring sales performance across regions.<br />
                        - Tracking website traffic and engagement metrics in one view.
                    </p>
                </div>
                <img
                    src="/dashboard_example.jpg" /* Replace with actual image path */
                    alt="Dashboard Example"
                    className="image-placeholder"
                />
            </div>

            {/* Encouragement Section */}
            <p className="text-center text-xl font-semibold mt-16 max-w-2xl">
                Advanced visualizations bring data to life, helping you uncover hidden insights and make smarter decisions. Ready to take your charts to the next level?
            </p>
        </div>
    );
}


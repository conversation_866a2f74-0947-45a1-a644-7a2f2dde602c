/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrush = createReactComponent("outline", "brush", "IconBrush", [["path", { "d": "M3 21v-4a4 4 0 1 1 4 4h-4", "key": "svg-0" }], ["path", { "d": "M21 3a16 16 0 0 0 -12.8 10.2", "key": "svg-1" }], ["path", { "d": "M21 3a16 16 0 0 1 -10.2 12.8", "key": "svg-2" }], ["path", { "d": "M10.6 9a9 9 0 0 1 4.4 4.4", "key": "svg-3" }]]);

export { IconBrush as default };
//# sourceMappingURL=IconBrush.mjs.map

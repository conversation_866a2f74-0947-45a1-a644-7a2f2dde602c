/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandKotlin = createReactComponent("outline", "brand-kotlin", "IconBrandKotlin", [["path", { "d": "M20 20h-16v-16h16", "key": "svg-0" }], ["path", { "d": "M4 20l16 -16", "key": "svg-1" }], ["path", { "d": "M4 12l8 -8", "key": "svg-2" }], ["path", { "d": "M12 12l8 8", "key": "svg-3" }]]);

export { IconBrandKotlin as default };
//# sourceMappingURL=IconBrandKotlin.mjs.map

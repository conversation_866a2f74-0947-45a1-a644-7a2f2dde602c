/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandZapier = createReactComponent("outline", "brand-zapier", "IconBrandZapier", [["path", { "d": "M3 12h6", "key": "svg-0" }], ["path", { "d": "M21 12h-6", "key": "svg-1" }], ["path", { "d": "M12 3v6", "key": "svg-2" }], ["path", { "d": "M12 15v6", "key": "svg-3" }], ["path", { "d": "M5.636 5.636l4.243 4.243", "key": "svg-4" }], ["path", { "d": "M18.364 18.364l-4.243 -4.243", "key": "svg-5" }], ["path", { "d": "M18.364 5.636l-4.243 4.243", "key": "svg-6" }], ["path", { "d": "M9.879 14.121l-4.243 4.243", "key": "svg-7" }]]);

export { IconBrandZapier as default };
//# sourceMappingURL=IconBrandZapier.mjs.map

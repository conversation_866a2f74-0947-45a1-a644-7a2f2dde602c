/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrowserPlus = createReactComponent("outline", "browser-plus", "IconBrowserPlus", [["path", { "d": "M4 8h16", "key": "svg-0" }], ["path", { "d": "M12 20h-6a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v6", "key": "svg-1" }], ["path", { "d": "M8 4v4", "key": "svg-2" }], ["path", { "d": "M16 19h6", "key": "svg-3" }], ["path", { "d": "M19 16v6", "key": "svg-4" }]]);

export { IconBrowserPlus as default };
//# sourceMappingURL=IconBrowserPlus.mjs.map

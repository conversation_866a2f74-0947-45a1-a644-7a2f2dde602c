import React, { useState, useEffect } from "react";
import Link from "next/link";
import Spline from "@splinetool/react-spline";
import Hello from "./level-4/Section1/1.1HelloWorld";
import Pyramid from "./level-4/Section1/1.2Pyramid";
import AsciiArt from "./level-4/Section1/1.3Ascii";
import LetterToFutureSelf from "./level-4/Section1/1.4Letter";
import DataTypesAndVariables from "./level-4/Section1/2.1DataTypes";
import DataTypesAndOperators from "./level-4/Section1/2.3whew";
import WeakPassword from "./level-4/Section1/3.1ControlFlow";
import AreYouACatPerson from "./level-4/Section1/3.2AreYouACatPerson";
import TheGoodTheBadAndTheElif from "./level-4/Section1/3.3TheGoodTheBadAndTheElif";
import BookOfAnswers from "./level-4/Section1/3.4BookOfAnswers";
import GodOfThunder from "./level-4/Section1/3.5GodOfThunder";
import DontMakeMeGuess from "./level-4/Section1/4.1DontMakeMeGuess";
import OhYouAreActuallyMakingMeGuess from "./level-4/Section1/4.2OhYouAreActuallyMakingMeGuess";
import PlierEtendreReleverElancer from "./level-4/Section1/4.3PlierEtendreReleverElancer";
import MyTrueLoveSentToMe from "./level-4/Section1/4.4MyTrueLoveSentToMe";
import GoodOlFizzBuzz from "./level-4/Section1/4.5GoodOlFizzBuzz";
import FinishLine from "./level-4/Section1/FinishLine";
import PrintPython from "./level-4/Section1/1.0PrintPython";
import Integer from "./level-4/Section1/2.2DataTypes";
import Howitallbegan from "./level-4/Section2/1.1_2WhatisEsports";
import Esportsvs from "./level-4/Section2/1.3Esportsvs";
import AQuicktour from "./level-4/Section2/1.4AQuicktour";
import Sportsgames from "./level-4/Section2/2.1Sportsgames";
import Strategygames from "./level-4/Section2/2.2Strategygames";
import Actiongames from "./level-4/Section2/2.3Actiongames";
import PuzzleandPartyGames from "./level-4/Section2/2.4PuzzleandPartyGames";
import BuildingYourGamingSetup from "./level-4/Section2/3.1BuildingYourGamingSetup";
import UnderstandingGameMechanics from "./level-4/Section2/4.1UnderstandingGameMechanics";
import TeamworkmakestheDreamWork from "./level-4/Section2/4.2TeamworkmakestheDreamWork";
import Prompt from "./level-4/Section3/1.1Prompt";
import Cookingupan from "./level-4/Section3/1.2Prompt";
import Thewannabe from "./level-4/Section3/1.3Prompt";
import Wherewould from "./level-4/Section3/2.1Prompt";
import Letsget from "./level-4/Section3/2.2Prompt";
import Iamsoewhat from "./level-4/Section3/2.3Prompt";
import Callmemaster from "./level-4/Section3/3.1Prompt";
import Sometimes from "./level-4/Section3/3.2Prompt";
import Keyand from "./level-4/Section3/3.3Prompt";
import Queation from "./level-4/Section4/4.1Prompt";


const Section1Data = {
  majorTopics: [
    {
      title: "HELLO WORLD!",
      minorTopics: [
        { title: "HELLO WORLD", component: <Hello /> },
        { title: "PYRAMID", component: <Pyramid /> },
        { title: "ASCII ART", component: <AsciiArt /> },
        { title: "DEAR FUTURE ME", component: <LetterToFutureSelf /> },
      ],
    },
    {
      title: "DATA TYPES",
      minorTopics: [
        { title: "VARIABLES", component: <DataTypesAndVariables /> },
        { title: "DATA TYPES", component: <Integer /> },
        {
          title: "WHEW!! IT’S HOT OUT HERE",
          component: <DataTypesAndOperators />,
        },
      ],
    },
    {
      title: "CONTROL FLOW",
      minorTopics: [
        { title: "WEAK PASSWORD, TRY AGAIN", component: <WeakPassword /> },
        { title: "ARE YOU A CAT PERSON?", component: <AreYouACatPerson /> },
        {
          title: "THE GOOD, THE BAD, AND THE ELIF",
          component: <TheGoodTheBadAndTheElif />,
        },
        { title: "BOOK OF ANSWERS", component: <BookOfAnswers /> },
        { title: "GOD OF THUNDER - THOR ODINSON", component: <GodOfThunder /> },
      ],
    },
    {
      title: "LOOPS AND HOOPS",
      minorTopics: [
        { title: "DON'T MAKE ME GUESS", component: <DontMakeMeGuess /> },
        {
          title: "OH, YOU ARE ACTUALLY MAKING ME GUESS",
          component: <OhYouAreActuallyMakingMeGuess />,
        },
        {
          title: "PLIER, ÉTENDRE, RELEVER, ÉLANCER",
          component: <PlierEtendreReleverElancer />,
        },
        { title: "MY TRUE LOVE SENT TO ME", component: <MyTrueLoveSentToMe /> },
        { title: "GOOD OL' FIZZBUZZ", component: <GoodOlFizzBuzz /> },
      ],
    },
    {
      title: "FINISH LINE",
      minorTopics: [{ title: "FINISH LINE", component: <FinishLine /> }],
    },
  ],
};

const Section2Data = {
  majorTopics: [
    {
      title: "What is Esports?",
      minorTopics: [
        {
          title: "How it all began: A brief history",
          component: <Howitallbegan />,
        },
        { title: "Esports vs. Traditional Sports", component: <Esportsvs /> },
        { title: "Popular Esports Games Overview", component: <AQuicktour /> },
      ],
    },
    {
      title: "Types of Esports Games",
      minorTopics: [
        { title: "Sports Games", component: <Sportsgames /> },
        { title: "Strategy Games", component: <Strategygames /> },
        { title: "Battle Games", component: <Actiongames /> },
        { title: "Puzzle and Party Games", component: <PuzzleandPartyGames /> },
      ],
    },
    {
      title: "Playing Like A Pro",
      minorTopics: [
        {
          title: "Building Your Gaming Setup",
          component: <BuildingYourGamingSetup />,
        },
        {
          title: "Understanding Game Mechanics",
          component: <UnderstandingGameMechanics />,
        },
        {
          title: "Teamwork makes the Dream Work",
          component: <TeamworkmakestheDreamWork />,
        },
      ],
    },
  ],
};

const Section3Data = {
  majorTopics: [
    {
      title: "Prompt Crafting for Scientists",
      minorTopics: [
        { title: "WHAT? WHY? AND HOW?", component: <Prompt /> },
        { title: "COOKING UP AN EFFECTIVE PROMPT", component: <Cookingupan /> },
        { title: "THE WANNABE SCIENTIST", component: <Thewannabe /> },
      ],
    },
    {
      title: "Creative Collector's Perspective",
      minorTopics: [
        { title: "WHERE WOULD I EVER USE THIS?", component: <Wherewould /> },
        { title: "LET’S GET A LIL’ CREATIVE OVA HERE!", component: <Letsget /> },
        { title: "I AM SOMEWHAT OF A COLLECTOR MYSELF", component: <Iamsoewhat /> },
      ],
    },
    {
      title: "Mastering Reflection and Key Terms",
      minorTopics: [
        { title: "CALL ME MASTER", component: <Callmemaster /> },
        { title: "SIT BACK AND REFLECT", component: <Sometimes /> },
        { title: "KEY TERMS AND DEFINITIONS", component: <Keyand /> },
      ],
    },
  ],
};

const Section4Data = {
  majorTopics: [
    {
      title: "Questionaire.py",
      minorTopics: [
        { title: "Quiz Time!", component: <Queation /> },
      ],
    },
  ],
};

export default function SectionLessons() {
  const [currentSection, setCurrentSection] = useState(null);
  const [currentMajorTopic, setCurrentMajorTopic] = useState(null);
  const [currentMinorTopic, setCurrentMinorTopic] = useState(null);
  const [shakeClass, setShakeClass] = useState("");

  useEffect(() => {
    const script = document.createElement("script");
    script.src =
      "https://unpkg.com/@splinetool/viewer@1.9.46/build/spline-viewer.js";
    script.type = "module";
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  useEffect(() => {
    if (currentMajorTopic !== null && currentMinorTopic !== null) {
      setShakeClass("shake");

      const timeout = setTimeout(() => setShakeClass(""), 500);

      return () => clearTimeout(timeout);
    }
  }, [currentMajorTopic, currentMinorTopic]);

  const handleSectionClick = (sectionIndex) => {
    setCurrentSection(sectionIndex);
    setCurrentMajorTopic(null);
    setCurrentMinorTopic(null);
  };

  const handleMajorTopicClick = (majorIndex) => {
    // When a major topic is clicked, set the current major topic and automatically set the first minor topic
    setCurrentMajorTopic(majorIndex);
    setCurrentMinorTopic(0); // Automatically select the first minor topic
  };

  const handleMinorTopicClick = (majorIndex, minorIndex) => {
    setCurrentMajorTopic(majorIndex);
    setCurrentMinorTopic(minorIndex);
  };

  const handleBackClick = () => {
    if (currentMinorTopic !== null) {
      // If we're in the minor topics view, just return to the major topics sidebar
      setCurrentMinorTopic(null); // Remove the minor topic to go back to major topics
    } else if (currentMajorTopic !== null) {
      // If we're in the major topics view, just return to the section view
      setCurrentMajorTopic(null); // Remove the major topic to go back to section view
    } else {
      // If we're in the section view, go back to the course selection view
      setCurrentSection(null);
    }
  };

  const getCurrentTopicData = () => {
    if (currentSection === 0) return Section1Data;
    if (currentSection === 1) return Section2Data;
    if (currentSection === 2) return Section3Data;
    return Section4Data;
  };

  const currentData = getCurrentTopicData();
  const currentTopic =
    currentData.majorTopics[currentMajorTopic]?.minorTopics[currentMinorTopic];
  const CurrentLessonComponent = currentTopic?.component;

  return (
    <div className="flex min-h-screen bg-[#FFF6E5] overflow-hidden">
      <style jsx>{`
        @keyframes fallFromTop {
          0% {
            top: -100%; // Start above the screen
          }
          100% {
            top: 210px; // Final position as in your original code
          }
        }
        @keyframes shake {
          0% {
            transform: translateX(0);
          }
          25% {
            transform: translateX(-5px);
          }
          50% {
            transform: translateX(5px);
          }
          75% {
            transform: translateX(-5px);
          }
          100% {
            transform: translateX(0);
          }
        }

        .shake {
          animation: shake 0.5s ease-in-out;
        }
        @keyframes fallingWords {
          from {
            opacity: 0;
            transform: translateX(-50px) translateY(-30px);
          }
          to {
            opacity: 1;
            transform: translateX(0) translateY(0);
          }
        }
        .falling-word {
          display: inline-block;
          opacity: 0;
          animation: fallingWords 0.5s ease-out forwards;
        }
        .falling-word:nth-child(1) {
          animation-delay: 0.1s;
        }
        .falling-word:nth-child(2) {
          animation-delay: 0.2s;
        }
        .falling-word:nth-child(3) {
          animation-delay: 0.3s;
        }

        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .subtitle {
          opacity: 0;
          transform: translateY(-20px);
          animation: slideDown 0.5s ease-out forwards;
        }
        .subtitle:nth-child(1) {
          animation-delay: 0.5s;
        }
        .subtitle:nth-child(2) {
          animation-delay: 1s;
        }
        .subtitle:nth-child(3) {
          animation-delay: 1.5s;
        }
        .subtitle:nth-child(4) {
          animation-delay: 2s;
        }
        .subtitle:nth-child(5) {
          animation-delay: 2.5s;
        }
      `}</style>

      {/* Sidebar */}
      <div className="w-1/4 bg-[#5E2C99] p-6 text-white fixed top-0 left-0 h-screen flex flex-col justify-between overflow-hidden">
        <h2 className="text-3xl font-bold mb-8 text-center">
          {currentSection === null
            ? "Course Progress"
            : [
                "Introduction to Python",
                "A Glimpse into the world of esports",
                "Prompt engineering?",
                "Questionaire.py",
              ][currentSection] || "Section not found"}
        </h2>
        <ul className="space-y-6 text-lg font-semibold">
          {currentSection === null ? (
            <div>
              {[
                "Introduction to Python",
                "A Glimpse into the world of esports",
                "Prompt engineering?",
                "Questionaire.py",
              ].map((sectionTitle, sectionIndex) => (
                <li key={sectionIndex} className="w-full">
                  <div className="flex items-center justify-between">
                    <h3
                      className="text-2xl font-bold mb-4 cursor-pointer"
                      onClick={() => handleSectionClick(sectionIndex)}
                    >
                      {sectionTitle}
                    </h3>
                  </div>
                </li>
              ))}
            </div>
          ) : currentMajorTopic === null ? (
            currentData.majorTopics.map((major, majorIndex) => (
              <li key={majorIndex} className="w-full">
                <div className="flex items-center justify-between">
                  {currentSection !== null && majorIndex === 0 && (
                    <>
                      {/* Back button and section title */}
                      <span
                        className="text-4xl cursor-pointer text-white mr-2 text-yellow-400"
                        onClick={handleBackClick}
                        style={{ marginTop: "-24px" }}
                      >
                        «
                      </span>
                      <h2
                        className="text-2xl font-bold mb-4 text-yellow-400 cursor-pointer"
                        onClick={handleBackClick}
                        style={{ marginRight: "auto" }}
                      >
                        {[
                          "Introduction to Python",
                          "A Glimpse into the world of esports",
                          "Prompt engineering?",
                          "Questionaire.py",
                        ][currentSection] || "Section not found"}
                      </h2>
                    </>
                  )}
                </div>
                {/* Major topic title */}
                <div>
                  <h3
                    className="text-2xl font-bold mb-4 cursor-pointer"
                    onClick={() => handleMajorTopicClick(majorIndex)}
                  >
                    {major.title.split(" ").map((word, index) => (
                      <span
                        key={index}
                        className={`falling-word nth-child-${index + 1}`}
                      >
                        {word}&nbsp;
                      </span>
                    ))}
                  </h3>
                </div>
              </li>
            ))
          ) : (
            <li className="w-full">
              <div className="flex items-center">
                <span
                  className="text-4xl cursor-pointer text-white mr-2 text-yellow-400"
                  onClick={handleBackClick}
                  style={{ marginTop: "-24px" }}
                >
                  «
                </span>
                <h3
                  className="text-2xl font-bold mb-4 text-yellow-400 cursor-pointer"
                  onClick={handleBackClick}
                >
                  {currentData.majorTopics[currentMajorTopic]?.title}
                </h3>
              </div>
              <ul className="space-y-4 pl-4">
                {currentData.majorTopics[currentMajorTopic]?.minorTopics.map(
                  (minor, minorIndex) => (
                    <li
                      key={minorIndex}
                      className={`subtitle cursor-pointer py-2 px-4 rounded-lg transition-all ${
                        currentMinorTopic === minorIndex
                          ? "bg-yellow-500 text-white"
                          : "bg-gray-500 text-white hover:bg-gray-400"
                      }`}
                      onClick={() =>
                        handleMinorTopicClick(currentMajorTopic, minorIndex)
                      }
                    >
                      {minor.title}
                    </li>
                  )
                )}
              </ul>
            </li>
          )}
        </ul>

        <Link href="/dashboard" passHref>
          <button className="bg-[#4CAF50] text-white rounded-lg px-4 py-2 mt-4 w-full hover:bg-green-600 transition-all">
            Back to Dashboard
          </button>
        </Link>
      </div>

      {/* Main Content */}
      <div className={`flex-1 overflow-auto ml-[25%]`}>
        {currentMajorTopic === null && currentMinorTopic === null && (
          <div style={{ position: "relative", overflow: "hidden" }}>
            <PrintPython />
            <Spline
              scene="https://prod.spline.design/tIgL1mgfvZ9GEoOp/scene.splinecode"
              style={{
                position: "fixed",
                top: "0px",
                left: "500px",
                width: "120%",
                height: "90%",
                animation: "fallFromTop 2s ease-out forwards",
              }}
            />
          </div>
        )}
        {currentMajorTopic !== null && currentMinorTopic !== null && (
          <div style={{ position: "relative", overflow: "hidden" }}>
            <div
              className={`content-container ${shakeClass}`} // Shake animation only applies here
              style={{ position: "relative" }}
            >
              {CurrentLessonComponent}
            </div>
            <Spline
              scene="https://prod.spline.design/tIgL1mgfvZ9GEoOp/scene.splinecode"
              style={{
                position: "fixed",
                top: "0px", // Start from the top of the screen
                left: "500px",
                width: "120%",
                height: "90%",
                animation: "fallFromTop 2s ease-out forwards", // Falling animation
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}

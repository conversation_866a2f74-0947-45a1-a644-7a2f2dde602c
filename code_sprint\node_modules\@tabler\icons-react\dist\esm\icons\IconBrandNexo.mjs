/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandNexo = createReactComponent("outline", "brand-nexo", "IconBrandNexo", [["path", { "d": "M17 3l5 3v12l-5 3l-10 -6v-6l10 6v-6l-5 -3z", "key": "svg-0" }], ["path", { "d": "M12 6l-5 -3l-5 3v12l5 3l4.7 -3.13", "key": "svg-1" }]]);

export { IconBrandNexo as default };
//# sourceMappingURL=IconBrandNexo.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandPnpm = createReactComponent("outline", "brand-pnpm", "IconBrandPnpm", [["path", { "d": "M3 17h4v4h-4z", "key": "svg-0" }], ["path", { "d": "M10 17h4v4h-4z", "key": "svg-1" }], ["path", { "d": "M17 17h4v4h-4z", "key": "svg-2" }], ["path", { "d": "M17 10h4v4h-4z", "key": "svg-3" }], ["path", { "d": "M17 3h4v4h-4z", "key": "svg-4" }], ["path", { "d": "M10 10h4v4h-4z", "key": "svg-5" }], ["path", { "d": "M10 3h4v4h-4z", "key": "svg-6" }], ["path", { "d": "M3 3h4v4h-4z", "key": "svg-7" }]]);

export { IconBrandPnpm as default };
//# sourceMappingURL=IconBrandPnpm.mjs.map

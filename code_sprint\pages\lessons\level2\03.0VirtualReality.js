import React from "react";

export default function VirtualReality() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-blue-600 to-purple-600 text-white p-10">
      <h1 className="text-5xl font-bold mb-8">Virtual Reality</h1>
      <p className="text-lg max-w-3xl text-center mb-8">
        Virtual Reality (VR) takes you into a whole new world where you can explore, play, and learn using special devices like VR headsets, motion trackers, and controllers. It's like stepping inside a video game!
      </p>

      {/* Key Components Section */}
      <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg max-w-2xl mb-8">
        <h2 className="text-3xl font-bold text-yellow-300 mb-4">
          Key Components of VR:
        </h2>
        <ul className="list-disc ml-5 space-y-2 text-lg text-gray-800">
          <li>
            <strong>VR Headsets:</strong> Devices like Oculus Quest or HTC Vive that let you see the 3D world around you.
          </li>
          <li>
            <strong>Motion Tracking Sensors:</strong> Track your movements so you can look around, walk, or even dance in the virtual space.
          </li>
          <li>
            <strong>VR Controllers:</strong> Handheld devices that let you interact with objects in the virtual world, like picking things up or drawing.
          </li>
        </ul>
        <img 
          src="path-to-image/vr-components.jpg" 
          alt="VR Components Example" 
          className="w-full rounded-lg mt-4"
        />
      </div>

      {/* Fun Applications Section */}
      <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg max-w-2xl mb-8">
        <h2 className="text-3xl font-bold text-yellow-300 mb-4">
          Fun Things You Can Do with VR:
        </h2>
        <ul className="list-disc ml-5 space-y-2 text-lg text-gray-800">
          <li>
            <strong>Explore Space:</strong> Visit planets and stars using VR apps like Google Expeditions.
          </li>
          <li>
            <strong>Underwater Adventures:</strong> Dive into the ocean to swim with dolphins and explore coral reefs.
          </li>
          <li>
            <strong>Play Games:</strong> Fight dragons, race cars, or build virtual cities in exciting VR games.
          </li>
        </ul>
        <img 
          src="path-to-image/vr-exploration.jpg" 
          alt="Kids Exploring VR Worlds" 
          className="w-full rounded-lg mt-4"
        />
      </div>

      {/* Why VR is Amazing Section */}
      <div className="bg-white bg-opacity-20 p-6 rounded-lg shadow-lg max-w-2xl">
        <h2 className="text-3xl font-bold text-yellow-300 mb-4">
          Why VR is Amazing
        </h2>
        <p className="text-lg text-gray-800">
          Virtual Reality lets you explore places you've never been, learn about the world in fun ways, and have adventures that feel real! It’s perfect for gaming, education, and even relaxing in a peaceful virtual forest.
        </p>
        <img 
          src="path-to-image/vr-amazing.jpg" 
          alt="Why VR is Amazing" 
          className="w-full rounded-lg mt-4"
        />
      </div>
    </div>
  );
}

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandPaypay = createReactComponent("outline", "brand-paypay", "IconBrandPaypay", [["path", { "d": "M6.375 21l3.938 -13.838", "key": "svg-0" }], ["path", { "d": "M3 6c16.731 0 21.231 9.881 4.5 11", "key": "svg-1" }], ["path", { "d": "M21 19v-14a2 2 0 0 0 -2 -2h-14a2 2 0 0 0 -2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2 -2z", "key": "svg-2" }]]);

export { IconBrandPaypay as default };
//# sourceMappingURL=IconBrandPaypay.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBriefcase = createReactComponent("outline", "briefcase", "IconBriefcase", [["path", { "d": "M3 7m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v9a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M8 7v-2a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v2", "key": "svg-1" }], ["path", { "d": "M12 12l0 .01", "key": "svg-2" }], ["path", { "d": "M3 13a20 20 0 0 0 18 0", "key": "svg-3" }]]);

export { IconBriefcase as default };
//# sourceMappingURL=IconBriefcase.mjs.map

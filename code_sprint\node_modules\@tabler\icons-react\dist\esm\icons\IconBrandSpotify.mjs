/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandSpotify = createReactComponent("outline", "brand-spotify", "IconBrandSpotify", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M8 11.973c2.5 -1.473 5.5 -.973 7.5 .527", "key": "svg-1" }], ["path", { "d": "M9 15c1.5 -1 4 -1 5 .5", "key": "svg-2" }], ["path", { "d": "M7 9c2 -1 6 -2 10 .5", "key": "svg-3" }]]);

export { IconBrandSpotify as default };
//# sourceMappingURL=IconBrandSpotify.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandTwilio = createReactComponent("outline", "brand-twilio", "IconBrandTwilio", [["path", { "d": "M21 12a9 9 0 1 1 -18 0a9 9 0 0 1 18 0z", "key": "svg-0" }], ["path", { "d": "M9 9m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-1" }], ["path", { "d": "M15 9m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-2" }], ["path", { "d": "M15 15m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-3" }], ["path", { "d": "M9 15m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-4" }]]);

export { IconBrandTwilio as default };
//# sourceMappingURL=IconBrandTwilio.mjs.map

/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBrandOpenSourceFilled = createReactComponent("filled", "brand-open-source-filled", "IconBrandOpenSourceFilled", [["path", { "d": "M12.283 2.004a10 10 0 0 1 3.736 19.155a1 1 0 0 1 -1.332 -.551l-2.193 -5.602a1 1 0 0 1 .456 -1.245a2 2 0 1 0 -1.9 0a1 1 0 0 1 .457 1.244l-2.193 5.603a1 1 0 0 1 -1.332 .552a10 10 0 0 1 4.018 -19.16z", "key": "svg-0" }]]);

export { IconBrandOpenSourceFilled as default };
//# sourceMappingURL=IconBrandOpenSourceFilled.mjs.map
